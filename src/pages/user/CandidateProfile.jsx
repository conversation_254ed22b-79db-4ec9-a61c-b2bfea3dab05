
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { PageHeader } from '@/components/common/PageHeader';
import { Button } from '@/components/ui/button';
import { StatusBadge } from '@/components/common/StatusBadge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Ta<PERSON>,
  Ta<PERSON>Content,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { mockCandidates, mockJobRoles } from '@/data/mockData';
import { ChevronLeft, Download, FileText, Phone, Mail, Calendar, Play } from 'lucide-react';

const CandidateProfile = () => {
  const { id } = useParams();
  const candidate = mockCandidates.find(c => c.id === id);
  
  if (!candidate) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium text-gray-900">Candidate not found</h3>
        <p className="mt-1 text-sm text-gray-500">
          The candidate you're looking for doesn't exist or has been removed.
        </p>
        <div className="mt-6">
          <Link to="/candidates">
            <Button>Back to Candidates</Button>
          </Link>
        </div>
      </div>
    );
  }
  
  const jobRole = mockJobRoles.find(role => role.id === candidate.jobRoleId);
  const interviewDate = candidate.scheduleDateTime ? new Date(candidate.scheduleDateTime) : null;
  
  return (
    <div>
      <div className="mb-6">
        <Link to="/candidates" className="text-primary hover:underline flex items-center">
          <ChevronLeft className="h-4 w-4 mr-1" /> Back to Candidates
        </Link>
      </div>
      
      <PageHeader 
        title={candidate.name}
        description={jobRole?.name || 'Unknown Role'}
        actions={
          <Button variant="outline" className="flex items-center gap-2">
            <Download className="h-4 w-4" /> Export PDF
          </Button>
        }
      />
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left column - Summary and Communication */}
        <div className="lg:col-span-1 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Candidate Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="text-sm font-medium text-gray-500">Email</h4>
                <p className="flex items-center mt-1">
                  <Mail className="h-4 w-4 mr-2 text-gray-400" />
                  <a href={`mailto:${candidate.email}`} className="text-primary hover:underline">
                    {candidate.email}
                  </a>
                </p>
              </div>
              
              <div>
                <h4 className="text-sm font-medium text-gray-500">Phone</h4>
                <p className="flex items-center mt-1">
                  <Phone className="h-4 w-4 mr-2 text-gray-400" />
                  <a href={`tel:${candidate.phone}`} className="text-primary hover:underline">
                    {candidate.phone}
                  </a>
                </p>
              </div>
              
              <div>
                <h4 className="text-sm font-medium text-gray-500">Interview Status</h4>
                <div className="mt-1">
                  <StatusBadge status={candidate.interviewStatus} />
                </div>
              </div>
              
              {interviewDate && (
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Interview Date</h4>
                  <p className="flex items-center mt-1">
                    <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                    {interviewDate.toLocaleString()}
                  </p>
                </div>
              )}
              
              {candidate.interviewResult !== 'N/A' && (
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Interview Result</h4>
                  <div className="mt-1">
                    <StatusBadge status={candidate.interviewResult} />
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Communication History</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {candidate.communicationHistory?.emails.length || candidate.communicationHistory?.calls.length ? (
                <div className="space-y-6">
                  {candidate.communicationHistory.emails.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-2">Emails</h4>
                      <div className="space-y-3">
                        {candidate.communicationHistory.emails.map((email, i) => (
                          <div key={i} className="flex items-start pb-3 border-b border-gray-100">
                            <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center shrink-0">
                              <Mail className="h-5 w-5 text-blue-600" />
                            </div>
                            <div className="ml-3">
                              <p className="text-sm font-medium">{email.subject}</p>
                              <p className="text-xs text-gray-500">{email.date} • {email.status}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {candidate.communicationHistory.calls.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-2">Calls</h4>
                      <div className="space-y-3">
                        {candidate.communicationHistory.calls.map((call, i) => (
                          <div key={i} className="flex items-start pb-3 border-b border-gray-100">
                            <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center shrink-0">
                              <Phone className="h-5 w-5 text-green-600" />
                            </div>
                            <div className="ml-3">
                              <p className="text-sm font-medium">{call.response}</p>
                              <p className="text-xs text-gray-500">{call.date}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {candidate.contactOutcome && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-2">Outcome</h4>
                      <p className="text-sm bg-green-50 border border-green-100 rounded-md p-3">
                        {candidate.contactOutcome}
                      </p>
                    </div>
                  )}
                </div>
              ) : (
                <p className="text-sm text-gray-500">No communication history yet.</p>
              )}
            </CardContent>
          </Card>
        </div>
        
        {/* Right column - Detailed information */}
        <div className="lg:col-span-2 space-y-6">
          <Tabs defaultValue="profile" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="profile">Profile</TabsTrigger>
              <TabsTrigger value="metrics">Metrics</TabsTrigger>
              <TabsTrigger value="transcript">Transcript</TabsTrigger>
              <TabsTrigger value="questions">
                <Link to={`/candidates/${candidate.id}/questions`} className="w-full h-full flex items-center justify-center">
                  Q&A
                </Link>
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="profile" className="mt-6 space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Education & Experience</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Education</h4>
                    <p className="mt-1">{candidate.education}</p>
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Experience</h4>
                    <p className="mt-1">{candidate.experience} years</p>
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Skills</h4>
                    <div className="mt-1 flex flex-wrap gap-1">
                      {candidate.skills.map((skill, i) => (
                        <span key={i} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Projects</h4>
                    <ul className="mt-1 list-disc list-inside space-y-1">
                      {candidate.projects.map((project, i) => (
                        <li key={i} className="text-sm">{project}</li>
                      ))}
                    </ul>
                  </div>
                  
                  {candidate.otherDetails && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-500">Other Details</h4>
                      <p className="mt-1">{candidate.otherDetails}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="metrics" className="mt-6 space-y-6">
              {candidate.metrics ? (
                <Card>
                  <CardHeader>
                    <CardTitle>Performance Metrics</CardTitle>
                    <CardDescription>
                      Overall Score: {candidate.metrics.overallScore}/100 • {candidate.interviewResult}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">Communication</span>
                        <span className="text-sm">{candidate.metrics.communication}/10</span>
                      </div>
                      <Progress value={candidate.metrics.communication * 10} className="h-2" />
                      <p className="text-xs text-gray-500">Clear, confident speech</p>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">Skills</span>
                        <span className="text-sm">{candidate.metrics.skills}/10</span>
                      </div>
                      <Progress value={candidate.metrics.skills * 10} className="h-2" />
                      <p className="text-xs text-gray-500">Strong Python, moderate JavaScript</p>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">Response Quality</span>
                        <span className="text-sm">{candidate.metrics.responseQuality}/10</span>
                      </div>
                      <Progress value={candidate.metrics.responseQuality * 10} className="h-2" />
                      <p className="text-xs text-gray-500">Detailed, relevant answers</p>
                    </div>
                    
                    {candidate.agentNotes && (
                      <div className="pt-2 border-t">
                        <h4 className="text-sm font-medium text-gray-500 mb-2">Agent Notes</h4>
                        <p className="text-sm">{candidate.agentNotes}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ) : (
                <Card>
                  <CardHeader>
                    <CardTitle>Performance Metrics</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-center py-6 text-gray-500">
                      Metrics will be available after the interview is completed.
                    </p>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
            
            <TabsContent value="transcript" className="mt-6 space-y-6">
              {candidate.audioUrl ? (
                <Card>
                  <CardHeader>
                    <CardTitle>Interview Transcript</CardTitle>
                    <CardDescription>
                      Recorded on {interviewDate?.toLocaleDateString() || 'Unknown date'}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="bg-gray-50 p-4 rounded-md">
                      <div className="flex items-center space-x-2 mb-4">
                        <Button size="sm" className="rounded-full px-2 flex items-center gap-1">
                          <Play className="h-3 w-3" /> Play
                        </Button>
                        <div className="h-1 bg-gray-200 flex-grow rounded-full overflow-hidden">
                          <div className="bg-primary h-1 w-1/3" />
                        </div>
                        <span className="text-xs text-gray-500">1:30 / 4:25</span>
                      </div>
                      <p className="text-xs text-gray-500">Transcript</p>
                    </div>
                    
                    <div className="space-y-4">
                      <div className="flex gap-3">
                        <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center text-white shrink-0">
                          A
                        </div>
                        <div className="bg-gray-50 px-3 py-2 rounded-lg">
                          <p className="text-sm text-gray-700">
                            Hello, I'm Alex from MyDigitalColleague. Thank you for joining this interview for the Software Engineer role. Can you tell me about your experience with Python?
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex gap-3">
                        <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-700 font-medium shrink-0">
                          {candidate.name.split(' ').map(n => n[0]).join('')}
                        </div>
                        <div className="bg-blue-50 px-3 py-2 rounded-lg">
                          <p className="text-sm text-gray-700">
                            I've been working with Python for about 3 years now. I built a CRM system using Django, which included features like customer segmentation, email campaigns, and reporting dashboards.
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex gap-3">
                        <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center text-white shrink-0">
                          A
                        </div>
                        <div className="bg-gray-50 px-3 py-2 rounded-lg">
                          <p className="text-sm text-gray-700">
                            That sounds impressive. Can you tell me more about how you integrated AWS S3 for file storage in that project?
                          </p>
                        </div>
                      </div>

                      <div className="flex gap-3">
                        <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-700 font-medium shrink-0">
                          {candidate.name.split(' ').map(n => n[0]).join('')}
                        </div>
                        <div className="bg-blue-50 px-3 py-2 rounded-lg">
                          <p className="text-sm text-gray-700">
                            Sure! I used boto3, AWS's Python SDK, to handle the file operations. The system allowed users to upload documents which were stored in S3 buckets with appropriate permissions...
                          </p>
                        </div>
                      </div>

                      <Link to={`/candidates/${candidate.id}/questions`}>
                        <Button variant="outline" className="flex items-center gap-2 w-full">
                          <FileText className="h-4 w-4" /> View Full Q&A
                        </Button>
                      </Link>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <Card>
                  <CardHeader>
                    <CardTitle>Interview Transcript</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-center py-6 text-gray-500">
                      Transcript will be available after the interview is completed.
                    </p>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
            
            <TabsContent value="questions" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Interview Questions & Answers</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-center py-6 text-gray-500">
                    Loading questions and answers...
                  </p>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default CandidateProfile;
