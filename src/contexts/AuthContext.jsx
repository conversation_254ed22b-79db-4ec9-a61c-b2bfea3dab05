
import { createContext, useContext, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useToast } from "@/components/ui/use-toast";

const AuthContext = createContext(undefined);

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();
  const { toast } = useToast();

  useEffect(() => {
    // Check if user is logged in from localStorage
    const savedUser = localStorage.getItem('mdc_user');
    if (savedUser) {
      try {
        setUser(JSON.parse(savedUser));
      } catch (e) {
        localStorage.removeItem('mdc_user');
      }
    }
    setIsLoading(false);
  }, []);

  const login = (email, password) => {
    setIsLoading(true);
    
    // Simulate API call with fixed credentials for demo
    setTimeout(() => {
      if (email === '<EMAIL>' && password === 'User123!') {
        const newUser = { email, role: 'user' };
        setUser(newUser);
        localStorage.setItem('mdc_user', JSON.stringify(newUser));
        navigate('/dashboard');
        toast({
          title: "Login successful",
          description: "Welcome to MyDigitalColleague",
        });
      } else if (email === '<EMAIL>' && password === 'Admin123!') {
        const newUser = { email, role: 'admin' };
        setUser(newUser);
        localStorage.setItem('mdc_user', JSON.stringify(newUser));
        navigate('/admin/dashboard');
        toast({
          title: "Admin login successful",
          description: "Welcome to MyDigitalColleague Admin",
        });
      } else {
        toast({
          title: "Login failed",
          description: "Invalid email or password. Try demo login instead.",
          variant: "destructive",
        });
      }
      setIsLoading(false);
    }, 1000);
  };

  const demoLogin = () => {
    setIsLoading(true);
    
    setTimeout(() => {
      const demoUser = {
        email: '<EMAIL>',
        role: 'demo'
      };
      setUser(demoUser);
      localStorage.setItem('mdc_user', JSON.stringify(demoUser));
      navigate('/dashboard');
      toast({
        title: "Demo login successful",
        description: "Welcome to the MyDigitalColleague demo",
      });
      setIsLoading(false);
    }, 1000);
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('mdc_user');
    navigate('/');
    toast({
      title: "Logged out",
      description: "You have been successfully logged out",
    });
  };

  return (
    <AuthContext.Provider value={{ user, isLoading, login, logout, demoLogin }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
