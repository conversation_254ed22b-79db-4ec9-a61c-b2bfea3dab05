
// Mock data for the application
// All TypeScript interfaces removed - using plain JavaScript objects

export const mockJobRoles: JobRole[] = [
  {
    id: '1',
    name: 'Software Engineer',
    duties: 'Develop web applications using modern technologies. Collaborate with cross-functional teams to define and design new features.',
    skills: ['Python', 'JavaScript', 'React', 'Teamwork'],
    education: 'BS Computer Science',
    experience: 3,
    criteria: 'Strong Python skills, 3+ years experience, good communication',
    createdAt: '2025-05-01',
  },
  {
    id: '2',
    name: 'Data Analyst',
    duties: 'Analyze business data to derive meaningful insights. Create dashboards and reports for stakeholders.',
    skills: ['SQL', 'Excel', 'Tableau', 'Communication'],
    education: 'BA Statistics',
    experience: 2,
    criteria: 'Proficient in SQL, 2+ years experience, analytical mindset',
    createdAt: '2025-05-05',
  },
  {
    id: '3',
    name: 'Product Manager',
    duties: 'Define product vision and strategy. Work with development teams to deliver features.',
    skills: ['Product Strategy', 'Agile', 'Leadership', 'Communication'],
    education: 'MBA or equivalent',
    experience: 5,
    criteria: 'Experience in product development, excellent communication skills',
    createdAt: '2025-05-10',
  },
];

export const mockCandidates: Candidate[] = [
  {
    id: '1',
    name: '<PERSON>e',
    email: '<EMAIL>',
    phone: '******-123-4567',
    jobRoleId: '1',
    experience: 3,
    education: 'BS Computer Science, MIT, 2020',
    skills: ['Python', 'JavaScript', 'React', 'Node.js'],
    projects: ['Developed CRM system using Python/Django', 'Built e-commerce platform with React'],
    otherDetails: 'AWS Certified Developer',
    contactStatus: 'Emailed',
    interviewStatus: 'Completed',
    interviewResult: 'Pass',
    scheduleDateTime: '2025-05-21T10:00:00',
    metrics: {
      communication: 8,
      skills: 7,
      responseQuality: 9,
      overallScore: 80,
    },
    agentNotes: 'Candidate demonstrated strong technical knowledge but hesitated on JavaScript questions.',
    communicationHistory: {
      emails: [
        { date: '2025-05-20', subject: 'Interview Schedule', status: 'Sent' }
      ],
      calls: [
        { date: '2025-05-21', response: 'Confirmed availability' }
      ]
    },
    contactOutcome: 'Confirmed for 2025-05-22, 10:00 AM',
    audioUrl: 'john_doe_20250521.mp3',
    createdAt: '2025-05-15',
  },
  {
    id: '2',
    name: 'Jane Smith',
    email: '<EMAIL>',
    phone: '******-987-6543',
    jobRoleId: '2',
    experience: 2,
    education: 'BA Statistics, Stanford, 2021',
    skills: ['SQL', 'Excel', 'Tableau', 'Python'],
    projects: ['Built sales analytics dashboard', 'Customer segmentation analysis'],
    otherDetails: 'Tableau Certified Associate',
    contactStatus: 'Called',
    interviewStatus: 'Scheduled',
    interviewResult: 'N/A',
    scheduleDateTime: '2025-05-22T14:00:00',
    communicationHistory: {
      emails: [],
      calls: [
        { date: '2025-05-21', response: 'Scheduled interview' }
      ]
    },
    contactOutcome: 'Scheduled for 2025-05-22, 14:00',
    createdAt: '2025-05-16',
  },
  {
    id: '3',
    name: 'Alice Brown',
    email: '<EMAIL>',
    phone: '******-111-2222',
    jobRoleId: '1',
    experience: 4,
    education: 'MS Computer Engineering, Berkeley, 2019',
    skills: ['JavaScript', 'TypeScript', 'React', 'AWS'],
    projects: ['Built serverless application on AWS', 'Developed CI/CD pipeline'],
    otherDetails: 'Open source contributor',
    contactStatus: 'Not Contacted',
    interviewStatus: 'Not Scheduled',
    interviewResult: 'N/A',
    createdAt: '2025-05-17',
  },
  {
    id: '4',
    name: 'Bob Johnson',
    email: '<EMAIL>',
    phone: '******-333-4444',
    jobRoleId: '3',
    experience: 6,
    education: 'MBA, Harvard, 2018',
    skills: ['Product Management', 'Leadership', 'Agile', 'Strategy'],
    projects: ['Led development of fintech product', 'Launched mobile app with 1M users'],
    otherDetails: 'Certified Scrum Master',
    contactStatus: 'Emailed',
    interviewStatus: 'Completed',
    interviewResult: 'Fail',
    scheduleDateTime: '2025-05-19T11:00:00',
    metrics: {
      communication: 6,
      skills: 5,
      responseQuality: 6,
      overallScore: 55,
    },
    agentNotes: 'Candidate lacked depth in technical understanding of product development.',
    communicationHistory: {
      emails: [
        { date: '2025-05-18', subject: 'Interview Schedule', status: 'Sent' }
      ],
      calls: [
        { date: '2025-05-19', response: 'Confirmed availability' }
      ]
    },
    contactOutcome: 'Interview completed, not selected',
    audioUrl: 'bob_johnson_20250519.mp3',
    createdAt: '2025-05-18',
  },
];

export const mockQuestions: Question[] = [
  {
    id: '1',
    candidateId: '1',
    question: 'Describe a Python project you recently worked on.',
    answer: 'I built a CRM system using Django, integrated with AWS S3 for file storage. It included features like customer segmentation, email campaigns, and reporting dashboards.',
  },
  {
    id: '2',
    candidateId: '1',
    question: 'How do you handle team conflicts?',
    answer: 'I use active listening to understand all perspectives and mediate solutions that work for everyone. In my last role, I resolved a conflict between design and development by creating a shared understanding of priorities.',
  },
  {
    id: '3',
    candidateId: '1',
    question: 'What JavaScript frameworks have you used?',
    answer: 'I have used React for the past 2 years, and have some experience with Vue. I prefer React because of its component-based architecture and the ecosystem around it.',
  },
  {
    id: '4',
    candidateId: '4',
    question: 'Describe your approach to product strategy.',
    answer: 'I start with customer research to identify pain points, then validate solutions with prototypes before full development. I use data to measure success and iterate based on feedback.',
  },
  {
    id: '5',
    candidateId: '4',
    question: 'How do you prioritize features?',
    answer: 'I use a framework that considers business value, customer impact, and effort. I create a weighted score for each feature and discuss with stakeholders to ensure alignment.',
  },
];

export const mockUsers: User[] = [
  {
    id: '1',
    name: 'Jane Smith',
    email: '<EMAIL>',
    plan: 'Basic',
    callsUsed: 50,
    callsTotal: 120,
    status: 'Active',
    lastReset: '2025-05-20',
  },
  {
    id: '2',
    name: 'John Doe',
    email: '<EMAIL>',
    plan: 'Voucher',
    callsUsed: 2,
    callsTotal: 5,
    status: 'Active',
  },
  {
    id: '3',
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    plan: 'Pro',
    callsUsed: 150,
    callsTotal: 500,
    status: 'Active',
  },
];

export const mockSubscriptions: Subscription[] = [
  {
    id: '1',
    userId: '1',
    userName: 'Jane Smith',
    invoiceNumber: 'INV001',
    amount: 50,
    status: 'Paid',
    date: '2025-05-01',
  },
  {
    id: '2',
    userId: '2',
    userName: 'John Doe',
    invoiceNumber: 'INV002',
    amount: 25,
    status: 'Paid',
    date: '2025-05-02',
  },
  {
    id: '3',
    userId: '1',
    userName: 'Jane Smith',
    invoiceNumber: 'INV003',
    amount: 150,
    status: 'Paid',
    date: '2025-05-10',
  },
];

export const mockTwilioNumbers: TwilioNumber[] = [
  {
    id: '1',
    number: '******-123-4567',
    assignedTo: 'Jane Smith',
    date: '2025-05-01',
  },
  {
    id: '2',
    number: '******-987-6543',
    assignedTo: 'John Doe',
    date: '2025-05-02',
  },
  {
    id: '3',
    number: '******-456-7890',
    assignedTo: 'Sarah Johnson',
    date: '2025-05-05',
  },
];

export const mockIssues: Issue[] = [
  {
    id: 'ISS001',
    userId: '1',
    userName: 'Jane Smith',
    description: 'Login failed multiple times',
    priority: 'High',
    date: '2025-05-20',
    status: 'Resolved',
  },
  {
    id: 'ISS002',
    userId: '2',
    userName: 'John Doe',
    description: 'Payment declined',
    priority: 'Medium',
    date: '2025-05-21',
    status: 'Pending',
  },
  {
    id: 'ISS003',
    userId: '3',
    userName: 'Sarah Johnson',
    description: 'Call quality issues',
    priority: 'Low',
    date: '2025-05-22',
    status: 'Pending',
  },
];

export const mockActivities: Activity[] = [
  {
    id: '1',
    timestamp: '2025-05-20 10:00',
    action: 'Emailed',
    details: 'Sent interview schedule',
    candidateId: '1',
    candidateName: 'John Doe',
  },
  {
    id: '2',
    timestamp: '2025-05-21 09:00',
    action: 'Called',
    details: 'Confirmed availability',
    candidateId: '2',
    candidateName: 'Jane Smith',
  },
  {
    id: '3',
    timestamp: '2025-05-21 10:30',
    action: 'Interview Completed',
    details: 'Score: 80/100, Pass',
    candidateId: '1',
    candidateName: 'John Doe',
  },
  {
    id: '4',
    timestamp: '2025-05-19 11:30',
    action: 'Interview Completed',
    details: 'Score: 55/100, Fail',
    candidateId: '4',
    candidateName: 'Bob Johnson',
  },
  {
    id: '5',
    timestamp: '2025-05-22 08:15',
    action: 'Scheduled Interview',
    details: 'Set for 2025-05-22, 14:00',
    candidateId: '2',
    candidateName: 'Jane Smith',
  },
];

export const mockAlexConfig = {
  email: {
    provider: 'Gmail',
    user: '<EMAIL>',
    token: 'mock-token-123',
  },
  agentName: 'Alex',
  twilioNumber: '******-123-4567',
  assignedDate: '2025-05-01',
};
