
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { PageHeader } from '@/components/common/PageHeader';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { mockCandidates, mockJobRoles, mockQuestions } from '@/data/mockData';
import { ChevronLeft, Download } from 'lucide-react';

const CandidateQuestions = () => {
  const { id } = useParams();
  const candidate = mockCandidates.find(c => c.id === id);
  const questions = mockQuestions.filter(q => q.candidateId === id);
  
  if (!candidate) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium text-gray-900">Candidate not found</h3>
        <p className="mt-1 text-sm text-gray-500">
          The candidate you're looking for doesn't exist or has been removed.
        </p>
        <div className="mt-6">
          <Link to="/candidates">
            <Button>Back to Candidates</Button>
          </Link>
        </div>
      </div>
    );
  }
  
  const jobRole = mockJobRoles.find(role => role.id === candidate.jobRoleId);
  
  return (
    <div>
      <div className="mb-6">
        <Link to={`/candidates/${id}`} className="text-primary hover:underline flex items-center">
          <ChevronLeft className="h-4 w-4 mr-1" /> Back to Candidate Profile
        </Link>
      </div>
      
      <PageHeader 
        title={`${candidate.name} - Interview Q&A`}
        description={jobRole?.name || 'Unknown Role'}
        actions={
          <Button variant="outline" className="flex items-center gap-2">
            <Download className="h-4 w-4" /> Export Q&A
          </Button>
        }
      />
      
      <Card>
        <CardHeader>
          <CardTitle>Interview Questions & Answers</CardTitle>
          {candidate.scheduleDateTime && (
            <CardDescription>
              Interview conducted on {new Date(candidate.scheduleDateTime).toLocaleDateString()}
            </CardDescription>
          )}
        </CardHeader>
        <CardContent>
          {questions.length > 0 ? (
            <div className="space-y-6">
              {questions.map((qa, index) => (
                <div key={qa.id} className="border-b pb-6 last:border-0 last:pb-0">
                  <div className="flex gap-4">
                    <div className="w-10 h-10 rounded-full bg-primary flex items-center justify-center text-white shrink-0">
                      Q
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">Question {index + 1}</h4>
                      <p className="mt-1">{qa.question}</p>
                    </div>
                  </div>
                  
                  <div className="flex gap-4 mt-4 ml-14">
                    <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-700 font-medium shrink-0">
                      A
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">Answer</h4>
                      <p className="mt-1">{qa.answer}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-center py-6 text-gray-500">
              No questions and answers available yet.
            </p>
          )}
        </CardContent>
      </Card>
      
      {candidate.metrics && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Performance Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-blue-50 p-4 rounded-md">
                <div className="text-sm text-gray-500">Communication</div>
                <div className="text-2xl font-semibold text-blue-700">{candidate.metrics.communication}/10</div>
              </div>
              <div className="bg-green-50 p-4 rounded-md">
                <div className="text-sm text-gray-500">Skills</div>
                <div className="text-2xl font-semibold text-green-700">{candidate.metrics.skills}/10</div>
              </div>
              <div className="bg-purple-50 p-4 rounded-md">
                <div className="text-sm text-gray-500">Response Quality</div>
                <div className="text-2xl font-semibold text-purple-700">{candidate.metrics.responseQuality}/10</div>
              </div>
            </div>
            
            {candidate.agentNotes && (
              <div className="mt-6">
                <h4 className="text-sm font-medium text-gray-500 mb-2">Agent Notes</h4>
                <p className="bg-gray-50 p-4 rounded-md">{candidate.agentNotes}</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default CandidateQuestions;
