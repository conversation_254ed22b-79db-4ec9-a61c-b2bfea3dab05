
import { PageHeader } from '@/components/common/PageHeader';
import { Card } from '@/components/ui/card';
import { mockUsers, mockSubscriptions } from '@/data/mockData';
import { Users, DollarSign, Phone, AlertCircle } from 'lucide-react';

const AdminDashboard = () => {
  // Calculate metrics
  const activeUsers = mockUsers.filter(user => user.status === 'Active').length;
  const basicPlans = mockUsers.filter(user => user.plan === 'Basic').length;
  const proPlans = mockUsers.filter(user => user.plan === 'Pro').length;
  const voucherPlans = mockUsers.filter(user => user.plan === 'Voucher').length;
  
  // Total revenue (simplified calculation)
  const totalRevenue = mockSubscriptions.reduce((sum, sub) => sum + sub.amount, 0);
  
  return (
    <div>
      <PageHeader 
        title="Admin Dashboard"
        description="Overview of system metrics and user statistics"
      />
      
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card className="p-6">
          <div className="flex items-start">
            <div className="p-2 bg-blue-100 rounded-md">
              <Users className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Active Users</p>
              <p className="mt-1 text-2xl font-semibold">{activeUsers}</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-6">
          <div className="flex items-start">
            <div className="p-2 bg-green-100 rounded-md">
              <DollarSign className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Revenue</p>
              <p className="mt-1 text-2xl font-semibold">${totalRevenue}</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-6">
          <div className="flex items-start">
            <div className="p-2 bg-purple-100 rounded-md">
              <Phone className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Twilio Numbers</p>
              <p className="mt-1 text-2xl font-semibold">3</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-6">
          <div className="flex items-start">
            <div className="p-2 bg-red-100 rounded-md">
              <AlertCircle className="h-6 w-6 text-red-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Open Issues</p>
              <p className="mt-1 text-2xl font-semibold">2</p>
            </div>
          </div>
        </Card>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Subscription Distribution */}
        <Card className="p-6">
          <h3 className="text-lg font-medium mb-4">Subscription Distribution</h3>
          <div className="flex items-center space-x-4">
            <div className="w-32 h-32 relative">
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-lg font-medium">{activeUsers}</span>
              </div>
              <svg className="w-full h-full" viewBox="0 0 36 36">
                <circle cx="18" cy="18" r="16" fill="none" stroke="#f3f4f6" strokeWidth="2"></circle>
                <circle 
                  cx="18" cy="18" r="16" fill="none" stroke="#3b82f6" strokeWidth="2" 
                  strokeDasharray={`${basicPlans / activeUsers * 100} 100`}
                  strokeDashoffset="25"
                ></circle>
                <circle 
                  cx="18" cy="18" r="16" fill="none" stroke="#10b981" strokeWidth="2" 
                  strokeDasharray={`${proPlans / activeUsers * 100} 100`}
                  strokeDashoffset={`${(100 - basicPlans / activeUsers * 100) + 25}`}
                ></circle>
                <circle 
                  cx="18" cy="18" r="16" fill="none" stroke="#f59e0b" strokeWidth="2" 
                  strokeDasharray={`${voucherPlans / activeUsers * 100} 100`}
                  strokeDashoffset={`${(100 - basicPlans / activeUsers * 100 - proPlans / activeUsers * 100) + 25}`}
                ></circle>
              </svg>
            </div>
            <div className="space-y-2">
              <div className="flex items-center">
                <span className="w-3 h-3 bg-blue-500 rounded-full mr-2"></span>
                <span className="text-sm">Basic: {basicPlans}</span>
              </div>
              <div className="flex items-center">
                <span className="w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                <span className="text-sm">Pro: {proPlans}</span>
              </div>
              <div className="flex items-center">
                <span className="w-3 h-3 bg-amber-500 rounded-full mr-2"></span>
                <span className="text-sm">Voucher: {voucherPlans}</span>
              </div>
            </div>
          </div>
        </Card>
        
        {/* Recent Activity */}
        <Card className="p-6">
          <h3 className="text-lg font-medium mb-4">Recent System Activity</h3>
          <div className="space-y-4">
            <div className="flex items-start">
              <div className="p-1 bg-green-100 rounded-full">
                <DollarSign className="h-4 w-4 text-green-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm">Jane Smith upgraded to Pro plan</p>
                <p className="text-xs text-gray-500">2025-05-10</p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="p-1 bg-blue-100 rounded-full">
                <Users className="h-4 w-4 text-blue-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm">New user registered: John Doe</p>
                <p className="text-xs text-gray-500">2025-05-02</p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="p-1 bg-red-100 rounded-full">
                <AlertCircle className="h-4 w-4 text-red-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm">Login issue reported by Jane Smith</p>
                <p className="text-xs text-gray-500">2025-05-20</p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="p-1 bg-purple-100 rounded-full">
                <Phone className="h-4 w-4 text-purple-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm">Twilio number assigned to Sarah Johnson</p>
                <p className="text-xs text-gray-500">2025-05-05</p>
              </div>
            </div>
          </div>
        </Card>
        
        {/* Recent Subscriptions */}
        <Card className="p-6">
          <h3 className="text-lg font-medium mb-4">Recent Subscriptions</h3>
          <div className="space-y-2">
            {mockSubscriptions.slice(0, 3).map((sub) => (
              <div key={sub.id} className="flex justify-between items-center p-2 bg-gray-50 rounded-md">
                <div>
                  <p className="font-medium text-sm">{sub.userName}</p>
                  <p className="text-xs text-gray-500">{sub.invoiceNumber} • {sub.date}</p>
                </div>
                <div>
                  <span className="font-medium text-green-600">${sub.amount}</span>
                </div>
              </div>
            ))}
          </div>
        </Card>
        
        {/* Quick Stats */}
        <Card className="p-6">
          <h3 className="text-lg font-medium mb-4">Quick Stats</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Total Calls Used</span>
              <span className="font-medium">202/625</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Average Score</span>
              <span className="font-medium">76/100</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Pass Rate</span>
              <span className="font-medium">67%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Server Status</span>
              <span className="text-green-600 font-medium">Operational</span>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default AdminDashboard;
