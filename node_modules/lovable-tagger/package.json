{"name": "lovable-tagger", "version": "1.1.8", "description": "Vite plugin for component tagging", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "type": "module", "files": ["dist"], "scripts": {"build": "tsup src/index.ts --format cjs,esm --dts --outDir dist", "dev": "tsup src/index.ts --format cjs,esm --dts --watch", "test": "vitest", "lint": "eslint src", "prepublishOnly": "npm run build", "clean": "<PERSON><PERSON><PERSON> dist", "prepare": "npm run clean && npm run build", "format": "prettier --write \"src/**/*.ts\" \"tests/**/*.ts\"", "coverage": "vitest run --coverage"}, "keywords": ["vite", "plugin", "component", "tagger"], "author": "<PERSON>", "license": "MIT", "peerDependencies": {"vite": "^5.0.0"}, "dependencies": {"@babel/parser": "^7.25.9", "@babel/types": "^7.25.8", "esbuild": "^0.25.0", "estree-walker": "^3.0.3", "magic-string": "^0.30.12", "tailwindcss": "^3.4.17"}, "devDependencies": {"@types/babel__core": "^7.20.5", "@types/estree": "^1.0.6", "@types/node": "^22.5.5", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "eslint": "^8.49.0", "rimraf": "^5.0.0", "tsup": "^7.2.0", "typescript": "^5.5.3", "vitest": "^0.34.6"}}