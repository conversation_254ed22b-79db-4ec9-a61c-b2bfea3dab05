{"mappings": ";;;AA2BA,yBAAyB,MAAM,wBAAwB,CAAC,OAAO,UAAU,GAAG,CAAC,CAAC;AAC9E,sCAAgC,SAAQ,iBAAiB;IACvD;;;;OAIG;IACH,2BAA2B,CAAC,EAAE,OAAO,CAAC;IACtC;;;OAGG;IACH,eAAe,CAAC,EAAE,CAAC,KAAK,EAAE,aAAa,KAAK,IAAI,CAAC;IACjD;;;OAGG;IACH,oBAAoB,CAAC,EAAE,CAAC,KAAK,EAAE,uBAAuB,KAAK,IAAI,CAAC;IAChE;;;OAGG;IACH,cAAc,CAAC,EAAE,CAAC,KAAK,EAAE,iBAAiB,KAAK,IAAI,CAAC;IACpD;;;;OAIG;IACH,iBAAiB,CAAC,EAAE,CAAC,KAAK,EAAE,uBAAuB,GAAG,iBAAiB,KAAK,IAAI,CAAC;IACjF;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,IAAI,CAAC;CACxB;AAED,OAAA,MAAM,8GAkHL,CAAC;AAWF,qCAAsC,SAAQ,iBAAiB;CAAG;AAElE,OAAA,MAAM,0HAmBJ,CAAC;AAMH,+BAA+B,WAAW,CAAC;IAAE,aAAa,EAAE,YAAY,CAAA;CAAE,CAAC,CAAC;AAC5E,yBAAyB,WAAW,CAAC;IAAE,aAAa,EAAE,UAAU,CAAA;CAAE,CAAC,CAAC;AAwIpE,OAAA,MAAM,kGAAuB,CAAC;AAC9B,OAAA,MAAM,0GAA+B,CAAC", "sources": ["packages/react/dismissable-layer/src/packages/react/dismissable-layer/src/DismissableLayer.tsx", "packages/react/dismissable-layer/src/packages/react/dismissable-layer/src/index.ts", "packages/react/dismissable-layer/src/index.ts"], "sourcesContent": [null, null, "export {\n  DismissableLayer,\n  DismissableLayerBranch,\n  //\n  Root,\n  Branch,\n} from './DismissableLayer';\nexport type { DismissableLayerProps } from './DismissableLayer';\n"], "names": [], "version": 3, "file": "index.d.ts.map"}