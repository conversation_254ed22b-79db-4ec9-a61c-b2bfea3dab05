#!/bin/bash

# <PERSON>ript to convert TypeScript syntax to JavaScript in all .jsx files

echo "Converting TypeScript syntax to JavaScript..."

# Navigate to project directory
cd /Users/<USER>/Desktop/projects/Alex-Interviewer

# Fix function parameter type annotations
echo "Fixing function parameter types..."
find src -name "*.jsx" -exec sed -i '' 's/(\([^)]*\): string)/(\1)/g' {} \;
find src -name "*.jsx" -exec sed -i '' 's/(\([^)]*\): number)/(\1)/g' {} \;
find src -name "*.jsx" -exec sed -i '' 's/(\([^)]*\): boolean)/(\1)/g' {} \;
find src -name "*.jsx" -exec sed -i '' 's/(\([^)]*\): React\.[^)]*)/(\1)/g' {} \;

# Fix variable type annotations
echo "Fixing variable type annotations..."
find src -name "*.jsx" -exec sed -i '' 's/: string\s*=/=/g' {} \;
find src -name "*.jsx" -exec sed -i '' 's/: number\s*=/=/g' {} \;
find src -name "*.jsx" -exec sed -i '' 's/: boolean\s*=/=/g' {} \;
find src -name "*.jsx" -exec sed -i '' 's/: React\.[^=]*=/=/g' {} \;

# Remove interface declarations
echo "Removing interface declarations..."
find src -name "*.jsx" -exec sed -i '' '/^interface /,/^}/d' {} \;
find src -name "*.jsx" -exec sed -i '' '/^export interface /,/^}/d' {} \;

# Fix React.forwardRef with TypeScript generics
echo "Fixing React.forwardRef..."
find src -name "*.jsx" -exec sed -i '' 's/React\.forwardRef<[^>]*>/React.forwardRef/g' {} \;

# Fix type imports
echo "Fixing type imports..."
find src -name "*.jsx" -exec sed -i '' 's/import { type /import { /g' {} \;
find src -name "*.jsx" -exec sed -i '' 's/, type / /g' {} \;

# Fix export type
echo "Fixing export types..."
find src -name "*.jsx" -exec sed -i '' 's/export type /\/\/ export type /g' {} \;

# Fix function return type annotations
echo "Fixing function return types..."
find src -name "*.jsx" -exec sed -i '' 's/): [^{]*{/){/g' {} \;

# Fix arrow function return types
echo "Fixing arrow function return types..."
find src -name "*.jsx" -exec sed -i '' 's/): [^=]*=>/): =>/g' {} \;

# Fix component prop types in function signatures
echo "Fixing component prop types..."
find src -name "*.jsx" -exec sed -i '' 's/({ [^}]*}: [^)]*)/({ \1 })/g' {} \;

echo "TypeScript to JavaScript conversion completed!"
