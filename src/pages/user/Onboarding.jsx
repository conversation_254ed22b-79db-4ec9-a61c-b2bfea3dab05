
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Logo } from '@/components/common/Logo';
import { useAuth } from '@/contexts/AuthContext';
import { UserDropdown } from '@/components/common/UserDropdown';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { mockAlexConfig } from '@/data/mockData';
import { useToast } from '@/components/ui/use-toast';
import { Check, Mail, User, Phone } from 'lucide-react';

const steps = [
  { id: 'email', title: 'Email Setup', description: 'Connect your email for <PERSON>' },
  { id: 'agent', title: 'Agent Name', description: 'Personalize your AI agent' },
  { id: 'twilio', title: 'Twilio', description: 'View your assigned number' },
];

const Onboarding = () => {
  const [currentStep, setCurrentStep] = useState('email');
  const [emailProvider, setEmailProvider] = useState(mockAlexConfig.email.provider);
  const [agentName, setAgentName] = useState(mockAlexConfig.agentName);
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const { toast } = useToast();
  
  const handleNext = () => {
    if (currentStep === 'email') {
      setCurrentStep('agent');
    } else if (currentStep === 'agent') {
      setCurrentStep('twilio');
    } else {
      // Save and redirect
      toast({
        title: "Configuration Saved",
        description: "Your Alex configuration has been updated",
      });
      navigate('/dashboard');
    }
  };
  
  const handleBack = () => {
    if (currentStep === 'twilio') {
      setCurrentStep('agent');
    } else if (currentStep === 'agent') {
      setCurrentStep('email');
    }
  };
  
  const handleGoogleLogin = () => {
    toast({
      title: "Google Authentication",
      description: "Successfully connected with Gmail",
    });
  };
  
  const handleMicrosoftLogin = () => {
    toast({
      title: "Microsoft Authentication",
      description: "Successfully connected with Outlook",
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm z-10 relative">
        <div className="container-page py-4">
          <div className="flex justify-between items-center">
            <Logo />
            
            {user && (
              <UserDropdown userEmail={user.email} onLogout={logout} />
            )}
          </div>
        </div>
      </header>
      
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="max-w-3xl mx-auto">
          {/* Steps */}
          <nav aria-label="Progress" className="mb-12">
            <ol className="flex items-center">
              {steps.map((step, stepIdx) => (
                <li key={step.id} className={`relative ${stepIdx === steps.length - 1 ? '' : 'pr-8'} ${stepIdx === 0 ? '' : 'pl-8'} flex-1`}>
                  {stepIdx !== 0 && (
                    <div className="absolute inset-0 flex items-center" aria-hidden="true">
                      <div className={`h-0.5 w-full ${currentStep === step.id || steps.findIndex(s => s.id === currentStep) > stepIdx ? 'bg-primary' : 'bg-gray-200'}`} />
                    </div>
                  )}
                  <div className="relative flex flex-col items-center group">
                    <span className={`h-9 w-9 rounded-full flex items-center justify-center ${
                      currentStep === step.id ? 'bg-primary text-white' : 
                      steps.findIndex(s => s.id === currentStep) > stepIdx ? 'bg-primary text-white' : 'bg-white border-2 border-gray-300 text-gray-500'
                    }`}>
                      {steps.findIndex(s => s.id === currentStep) > stepIdx ? (
                        <Check className="h-5 w-5" />
                      ) : (
                        stepIdx + 1
                      )}
                    </span>
                    <div className="mt-3 hidden sm:block">
                      <span className="text-sm font-medium">{step.title}</span>
                      <p className="text-xs text-gray-500">{step.description}</p>
                    </div>
                  </div>
                </li>
              ))}
            </ol>
          </nav>
          
          {/* Content */}
          <div className="bg-white shadow sm:rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              {currentStep === 'email' && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">Email Setup</h2>
                  <p className="mb-6 text-gray-500">
                    Connect your email account to allow Alex to send emails to candidates. Choose your email provider below.
                  </p>
                  
                  <div className="space-y-6">
                    <div className="space-y-2">
                      <Label htmlFor="email-provider">Email Provider</Label>
                      <Select
                        value={emailProvider}
                        onValueChange={setEmailProvider}
                      >
                        <SelectTrigger id="email-provider">
                          <SelectValue placeholder="Select an email provider" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Gmail">Gmail</SelectItem>
                          <SelectItem value="Outlook">Outlook</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="pt-4 border-t">
                      <h3 className="text-sm font-medium text-gray-900 mb-3">Connect Your Account</h3>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <Button 
                          type="button" 
                          variant="outline" 
                          className="flex items-center justify-center gap-2"
                          onClick={handleGoogleLogin}
                          disabled={emailProvider !== 'Gmail'}
                        >
                          <svg width="18" height="18" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 488 512">
                            <path fill="currentColor" d="M488 261.8C488 403.3 391.1 504 248 504 110.8 504 0 393.2 0 256S110.8 8 248 8c66.8 0 123 24.5 166.3 64.9l-67.5 64.9C258.5 52.6 94.3 116.6 94.3 256c0 86.5 69.1 156.6 153.7 156.6 98.2 0 135-70.4 140.8-106.9H248v-85.3h236.1c2.3 12.7 3.9 24.9 3.9 41.4z"/>
                          </svg>
                          Sign in with Google
                        </Button>
                        <Button 
                          type="button" 
                          variant="outline"
                          className="flex items-center justify-center gap-2"
                          onClick={handleMicrosoftLogin}
                          disabled={emailProvider !== 'Outlook'}
                        >
                          <svg width="18" height="18" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 23 23">
                            <path fill="#f3f3f3" d="M0 0h23v23H0z"/>
                            <path fill="#f35325" d="M1 1h10v10H1z"/>
                            <path fill="#81bc06" d="M12 1h10v10H12z"/>
                            <path fill="#05a6f0" d="M1 12h10v10H1z"/>
                            <path fill="#ffba08" d="M12 12h10v10H12z"/>
                          </svg>
                          Sign in with Microsoft
                        </Button>
                      </div>
                    </div>
                    
                    <div className="bg-blue-50 p-4 rounded-md">
                      <div className="flex">
                        <div className="flex-shrink-0">
                          <Mail className="h-5 w-5 text-blue-400" />
                        </div>
                        <div className="ml-3">
                          <h3 className="text-sm font-medium text-blue-800">Connected Account</h3>
                          <div className="mt-2 text-sm text-blue-700">
                            <p>Email: {mockAlexConfig.email.user}</p>
                            <p className="mt-1">Status: ✅ Connected</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              
              {currentStep === 'agent' && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">Agent Name</h2>
                  <p className="mb-6 text-gray-500">
                    Personalize your AI interviewer's name. This name will be used when communicating with candidates.
                  </p>
                  
                  <div className="space-y-6">
                    <div className="space-y-2">
                      <Label htmlFor="agent-name">Agent Name</Label>
                      <Input
                        id="agent-name"
                        value={agentName}
                        onChange={(e) => setAgentName(e.target.value)}
                        className="max-w-md"
                        placeholder="e.g. Alex"
                      />
                      <p className="text-xs text-gray-500">
                        The default name is "Alex", but you can customize it to anything you prefer.
                      </p>
                    </div>
                    
                    <div className="bg-blue-50 p-4 rounded-md">
                      <div className="flex">
                        <div className="flex-shrink-0">
                          <User className="h-5 w-5 text-blue-400" />
                        </div>
                        <div className="ml-3">
                          <h3 className="text-sm font-medium text-blue-800">Agent Preview</h3>
                          <div className="mt-2 text-sm text-blue-700">
                            <p>
                              Hello, I'm {agentName || 'Alex'} from MyDigitalColleague. I'll be conducting your technical interview today.
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              
              {currentStep === 'twilio' && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">Twilio Configuration</h2>
                  <p className="mb-6 text-gray-500">
                    View your assigned Twilio phone number. This number is managed by Alex Interviewer.
                  </p>
                  
                  <div className="space-y-6">
                    <div className="bg-gray-50 border border-gray-200 rounded-md p-4">
                      <div className="flex">
                        <div className="flex-shrink-0">
                          <Phone className="h-5 w-5 text-gray-400" />
                        </div>
                        <div className="ml-3">
                          <h3 className="text-sm font-medium text-gray-800">Assigned Phone Number</h3>
                          <div className="mt-2 text-sm text-gray-700">
                            <p className="font-medium">{mockAlexConfig.twilioNumber}</p>
                            <p className="mt-1 text-gray-500">Assigned on {mockAlexConfig.assignedDate}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-yellow-50 p-4 rounded-md">
                      <div className="flex">
                        <div className="flex-shrink-0">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </div>
                        <div className="ml-3">
                          <h3 className="text-sm font-medium text-yellow-800">Important Note</h3>
                          <div className="mt-2 text-sm text-yellow-700">
                            <p>
                              This phone number is managed by Alex Interviewer and cannot be changed. If you need a different number, please contact support.
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
            
            {/* Actions */}
            <div className="px-4 py-3 bg-gray-50 sm:px-6 flex justify-between rounded-b-lg">
              {currentStep !== 'email' && (
                <Button type="button" variant="outline" onClick={handleBack}>
                  Back
                </Button>
              )}
              {currentStep === 'email' && (
                <div></div> // Placeholder for alignment
              )}
              <Button type="button" className="bg-primary hover:bg-primary/90" onClick={handleNext}>
                {currentStep === 'twilio' ? 'Complete' : 'Next'}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Onboarding;
