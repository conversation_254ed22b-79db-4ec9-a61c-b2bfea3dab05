var Bf=e=>{throw TypeError(e)};var Vl=(e,t,n)=>t.has(e)||Bf("Cannot "+n);var T=(e,t,n)=>(Vl(e,t,"read from private field"),n?n.call(e):t.get(e)),de=(e,t,n)=>t.has(e)?Bf("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n),Z=(e,t,n,r)=>(Vl(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n),We=(e,t,n)=>(Vl(e,t,"access private method"),n);var Ei=(e,t,n,r)=>({set _(s){Z(e,t,s,n)},get _(){return T(e,t,r)}});function yw(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const s in r)if(s!=="default"&&!(s in e)){const i=Object.getOwnPropertyDescriptor(r,s);i&&Object.defineProperty(e,s,i.get?i:{enumerable:!0,get:()=>r[s]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const i of s)if(i.type==="childList")for(const a of i.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&r(a)}).observe(document,{childList:!0,subtree:!0});function n(s){const i={};return s.integrity&&(i.integrity=s.integrity),s.referrerPolicy&&(i.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?i.credentials="include":s.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(s){if(s.ep)return;s.ep=!0;const i=n(s);fetch(s.href,i)}})();function nh(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var rh={exports:{}},el={},sh={exports:{}},te={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var mi=Symbol.for("react.element"),ww=Symbol.for("react.portal"),jw=Symbol.for("react.fragment"),Nw=Symbol.for("react.strict_mode"),bw=Symbol.for("react.profiler"),Sw=Symbol.for("react.provider"),Cw=Symbol.for("react.context"),Ew=Symbol.for("react.forward_ref"),kw=Symbol.for("react.suspense"),Pw=Symbol.for("react.memo"),Tw=Symbol.for("react.lazy"),Vf=Symbol.iterator;function Rw(e){return e===null||typeof e!="object"?null:(e=Vf&&e[Vf]||e["@@iterator"],typeof e=="function"?e:null)}var oh={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},ih=Object.assign,ah={};function Xs(e,t,n){this.props=e,this.context=t,this.refs=ah,this.updater=n||oh}Xs.prototype.isReactComponent={};Xs.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Xs.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function lh(){}lh.prototype=Xs.prototype;function Xu(e,t,n){this.props=e,this.context=t,this.refs=ah,this.updater=n||oh}var Ju=Xu.prototype=new lh;Ju.constructor=Xu;ih(Ju,Xs.prototype);Ju.isPureReactComponent=!0;var Hf=Array.isArray,ch=Object.prototype.hasOwnProperty,Zu={current:null},uh={key:!0,ref:!0,__self:!0,__source:!0};function dh(e,t,n){var r,s={},i=null,a=null;if(t!=null)for(r in t.ref!==void 0&&(a=t.ref),t.key!==void 0&&(i=""+t.key),t)ch.call(t,r)&&!uh.hasOwnProperty(r)&&(s[r]=t[r]);var l=arguments.length-2;if(l===1)s.children=n;else if(1<l){for(var c=Array(l),u=0;u<l;u++)c[u]=arguments[u+2];s.children=c}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)s[r]===void 0&&(s[r]=l[r]);return{$$typeof:mi,type:e,key:i,ref:a,props:s,_owner:Zu.current}}function Mw(e,t){return{$$typeof:mi,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function ed(e){return typeof e=="object"&&e!==null&&e.$$typeof===mi}function Aw(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Wf=/\/+/g;function Hl(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Aw(""+e.key):t.toString(36)}function Zi(e,t,n,r,s){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var a=!1;if(e===null)a=!0;else switch(i){case"string":case"number":a=!0;break;case"object":switch(e.$$typeof){case mi:case ww:a=!0}}if(a)return a=e,s=s(a),e=r===""?"."+Hl(a,0):r,Hf(s)?(n="",e!=null&&(n=e.replace(Wf,"$&/")+"/"),Zi(s,t,n,"",function(u){return u})):s!=null&&(ed(s)&&(s=Mw(s,n+(!s.key||a&&a.key===s.key?"":(""+s.key).replace(Wf,"$&/")+"/")+e)),t.push(s)),1;if(a=0,r=r===""?".":r+":",Hf(e))for(var l=0;l<e.length;l++){i=e[l];var c=r+Hl(i,l);a+=Zi(i,t,n,c,s)}else if(c=Rw(e),typeof c=="function")for(e=c.call(e),l=0;!(i=e.next()).done;)i=i.value,c=r+Hl(i,l++),a+=Zi(i,t,n,c,s);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return a}function ki(e,t,n){if(e==null)return e;var r=[],s=0;return Zi(e,r,"","",function(i){return t.call(n,i,s++)}),r}function Iw(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var st={current:null},ea={transition:null},_w={ReactCurrentDispatcher:st,ReactCurrentBatchConfig:ea,ReactCurrentOwner:Zu};function fh(){throw Error("act(...) is not supported in production builds of React.")}te.Children={map:ki,forEach:function(e,t,n){ki(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return ki(e,function(){t++}),t},toArray:function(e){return ki(e,function(t){return t})||[]},only:function(e){if(!ed(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};te.Component=Xs;te.Fragment=jw;te.Profiler=bw;te.PureComponent=Xu;te.StrictMode=Nw;te.Suspense=kw;te.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=_w;te.act=fh;te.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=ih({},e.props),s=e.key,i=e.ref,a=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,a=Zu.current),t.key!==void 0&&(s=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(c in t)ch.call(t,c)&&!uh.hasOwnProperty(c)&&(r[c]=t[c]===void 0&&l!==void 0?l[c]:t[c])}var c=arguments.length-2;if(c===1)r.children=n;else if(1<c){l=Array(c);for(var u=0;u<c;u++)l[u]=arguments[u+2];r.children=l}return{$$typeof:mi,type:e.type,key:s,ref:i,props:r,_owner:a}};te.createContext=function(e){return e={$$typeof:Cw,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Sw,_context:e},e.Consumer=e};te.createElement=dh;te.createFactory=function(e){var t=dh.bind(null,e);return t.type=e,t};te.createRef=function(){return{current:null}};te.forwardRef=function(e){return{$$typeof:Ew,render:e}};te.isValidElement=ed;te.lazy=function(e){return{$$typeof:Tw,_payload:{_status:-1,_result:e},_init:Iw}};te.memo=function(e,t){return{$$typeof:Pw,type:e,compare:t===void 0?null:t}};te.startTransition=function(e){var t=ea.transition;ea.transition={};try{e()}finally{ea.transition=t}};te.unstable_act=fh;te.useCallback=function(e,t){return st.current.useCallback(e,t)};te.useContext=function(e){return st.current.useContext(e)};te.useDebugValue=function(){};te.useDeferredValue=function(e){return st.current.useDeferredValue(e)};te.useEffect=function(e,t){return st.current.useEffect(e,t)};te.useId=function(){return st.current.useId()};te.useImperativeHandle=function(e,t,n){return st.current.useImperativeHandle(e,t,n)};te.useInsertionEffect=function(e,t){return st.current.useInsertionEffect(e,t)};te.useLayoutEffect=function(e,t){return st.current.useLayoutEffect(e,t)};te.useMemo=function(e,t){return st.current.useMemo(e,t)};te.useReducer=function(e,t,n){return st.current.useReducer(e,t,n)};te.useRef=function(e){return st.current.useRef(e)};te.useState=function(e){return st.current.useState(e)};te.useSyncExternalStore=function(e,t,n){return st.current.useSyncExternalStore(e,t,n)};te.useTransition=function(){return st.current.useTransition()};te.version="18.3.1";sh.exports=te;var f=sh.exports;const O=nh(f),mh=yw({__proto__:null,default:O},[f]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ow=f,Dw=Symbol.for("react.element"),Lw=Symbol.for("react.fragment"),Fw=Object.prototype.hasOwnProperty,zw=Ow.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,$w={key:!0,ref:!0,__self:!0,__source:!0};function ph(e,t,n){var r,s={},i=null,a=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(a=t.ref);for(r in t)Fw.call(t,r)&&!$w.hasOwnProperty(r)&&(s[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)s[r]===void 0&&(s[r]=t[r]);return{$$typeof:Dw,type:e,key:i,ref:a,props:s,_owner:zw.current}}el.Fragment=Lw;el.jsx=ph;el.jsxs=ph;rh.exports=el;var o=rh.exports,hh={exports:{}},Nt={},xh={exports:{}},gh={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(S,P){var L=S.length;S.push(P);e:for(;0<L;){var _=L-1>>>1,B=S[_];if(0<s(B,P))S[_]=P,S[L]=B,L=_;else break e}}function n(S){return S.length===0?null:S[0]}function r(S){if(S.length===0)return null;var P=S[0],L=S.pop();if(L!==P){S[0]=L;e:for(var _=0,B=S.length,Q=B>>>1;_<Q;){var re=2*(_+1)-1,Pe=S[re],q=re+1,H=S[q];if(0>s(Pe,L))q<B&&0>s(H,Pe)?(S[_]=H,S[q]=L,_=q):(S[_]=Pe,S[re]=L,_=re);else if(q<B&&0>s(H,L))S[_]=H,S[q]=L,_=q;else break e}}return P}function s(S,P){var L=S.sortIndex-P.sortIndex;return L!==0?L:S.id-P.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var a=Date,l=a.now();e.unstable_now=function(){return a.now()-l}}var c=[],u=[],p=1,m=null,d=3,v=!1,w=!1,h=!1,j=typeof setTimeout=="function"?setTimeout:null,g=typeof clearTimeout=="function"?clearTimeout:null,x=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function y(S){for(var P=n(u);P!==null;){if(P.callback===null)r(u);else if(P.startTime<=S)r(u),P.sortIndex=P.expirationTime,t(c,P);else break;P=n(u)}}function N(S){if(h=!1,y(S),!w)if(n(c)!==null)w=!0,F(b);else{var P=n(u);P!==null&&G(N,P.startTime-S)}}function b(S,P){w=!1,h&&(h=!1,g(k),k=-1),v=!0;var L=d;try{for(y(P),m=n(c);m!==null&&(!(m.expirationTime>P)||S&&!U());){var _=m.callback;if(typeof _=="function"){m.callback=null,d=m.priorityLevel;var B=_(m.expirationTime<=P);P=e.unstable_now(),typeof B=="function"?m.callback=B:m===n(c)&&r(c),y(P)}else r(c);m=n(c)}if(m!==null)var Q=!0;else{var re=n(u);re!==null&&G(N,re.startTime-P),Q=!1}return Q}finally{m=null,d=L,v=!1}}var E=!1,C=null,k=-1,I=5,M=-1;function U(){return!(e.unstable_now()-M<I)}function D(){if(C!==null){var S=e.unstable_now();M=S;var P=!0;try{P=C(!0,S)}finally{P?W():(E=!1,C=null)}}else E=!1}var W;if(typeof x=="function")W=function(){x(D)};else if(typeof MessageChannel<"u"){var A=new MessageChannel,K=A.port2;A.port1.onmessage=D,W=function(){K.postMessage(null)}}else W=function(){j(D,0)};function F(S){C=S,E||(E=!0,W())}function G(S,P){k=j(function(){S(e.unstable_now())},P)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(S){S.callback=null},e.unstable_continueExecution=function(){w||v||(w=!0,F(b))},e.unstable_forceFrameRate=function(S){0>S||125<S?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):I=0<S?Math.floor(1e3/S):5},e.unstable_getCurrentPriorityLevel=function(){return d},e.unstable_getFirstCallbackNode=function(){return n(c)},e.unstable_next=function(S){switch(d){case 1:case 2:case 3:var P=3;break;default:P=d}var L=d;d=P;try{return S()}finally{d=L}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(S,P){switch(S){case 1:case 2:case 3:case 4:case 5:break;default:S=3}var L=d;d=S;try{return P()}finally{d=L}},e.unstable_scheduleCallback=function(S,P,L){var _=e.unstable_now();switch(typeof L=="object"&&L!==null?(L=L.delay,L=typeof L=="number"&&0<L?_+L:_):L=_,S){case 1:var B=-1;break;case 2:B=250;break;case 5:B=**********;break;case 4:B=1e4;break;default:B=5e3}return B=L+B,S={id:p++,callback:P,priorityLevel:S,startTime:L,expirationTime:B,sortIndex:-1},L>_?(S.sortIndex=L,t(u,S),n(c)===null&&S===n(u)&&(h?(g(k),k=-1):h=!0,G(N,L-_))):(S.sortIndex=B,t(c,S),w||v||(w=!0,F(b))),S},e.unstable_shouldYield=U,e.unstable_wrapCallback=function(S){var P=d;return function(){var L=d;d=P;try{return S.apply(this,arguments)}finally{d=L}}}})(gh);xh.exports=gh;var Uw=xh.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Bw=f,jt=Uw;function R(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var vh=new Set,zo={};function Wr(e,t){zs(e,t),zs(e+"Capture",t)}function zs(e,t){for(zo[e]=t,e=0;e<t.length;e++)vh.add(t[e])}var yn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ac=Object.prototype.hasOwnProperty,Vw=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Kf={},Gf={};function Hw(e){return Ac.call(Gf,e)?!0:Ac.call(Kf,e)?!1:Vw.test(e)?Gf[e]=!0:(Kf[e]=!0,!1)}function Ww(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Kw(e,t,n,r){if(t===null||typeof t>"u"||Ww(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function ot(e,t,n,r,s,i,a){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=s,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=a}var He={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){He[e]=new ot(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];He[t]=new ot(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){He[e]=new ot(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){He[e]=new ot(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){He[e]=new ot(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){He[e]=new ot(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){He[e]=new ot(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){He[e]=new ot(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){He[e]=new ot(e,5,!1,e.toLowerCase(),null,!1,!1)});var td=/[\-:]([a-z])/g;function nd(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(td,nd);He[t]=new ot(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(td,nd);He[t]=new ot(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(td,nd);He[t]=new ot(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){He[e]=new ot(e,1,!1,e.toLowerCase(),null,!1,!1)});He.xlinkHref=new ot("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){He[e]=new ot(e,1,!1,e.toLowerCase(),null,!0,!0)});function rd(e,t,n,r){var s=He.hasOwnProperty(t)?He[t]:null;(s!==null?s.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Kw(t,n,s,r)&&(n=null),r||s===null?Hw(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):s.mustUseProperty?e[s.propertyName]=n===null?s.type===3?!1:"":n:(t=s.attributeName,r=s.attributeNamespace,n===null?e.removeAttribute(t):(s=s.type,n=s===3||s===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Tn=Bw.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Pi=Symbol.for("react.element"),as=Symbol.for("react.portal"),ls=Symbol.for("react.fragment"),sd=Symbol.for("react.strict_mode"),Ic=Symbol.for("react.profiler"),yh=Symbol.for("react.provider"),wh=Symbol.for("react.context"),od=Symbol.for("react.forward_ref"),_c=Symbol.for("react.suspense"),Oc=Symbol.for("react.suspense_list"),id=Symbol.for("react.memo"),zn=Symbol.for("react.lazy"),jh=Symbol.for("react.offscreen"),Qf=Symbol.iterator;function lo(e){return e===null||typeof e!="object"?null:(e=Qf&&e[Qf]||e["@@iterator"],typeof e=="function"?e:null)}var Ce=Object.assign,Wl;function bo(e){if(Wl===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Wl=t&&t[1]||""}return`
`+Wl+e}var Kl=!1;function Gl(e,t){if(!e||Kl)return"";Kl=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var s=u.stack.split(`
`),i=r.stack.split(`
`),a=s.length-1,l=i.length-1;1<=a&&0<=l&&s[a]!==i[l];)l--;for(;1<=a&&0<=l;a--,l--)if(s[a]!==i[l]){if(a!==1||l!==1)do if(a--,l--,0>l||s[a]!==i[l]){var c=`
`+s[a].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}while(1<=a&&0<=l);break}}}finally{Kl=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?bo(e):""}function Gw(e){switch(e.tag){case 5:return bo(e.type);case 16:return bo("Lazy");case 13:return bo("Suspense");case 19:return bo("SuspenseList");case 0:case 2:case 15:return e=Gl(e.type,!1),e;case 11:return e=Gl(e.type.render,!1),e;case 1:return e=Gl(e.type,!0),e;default:return""}}function Dc(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case ls:return"Fragment";case as:return"Portal";case Ic:return"Profiler";case sd:return"StrictMode";case _c:return"Suspense";case Oc:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case wh:return(e.displayName||"Context")+".Consumer";case yh:return(e._context.displayName||"Context")+".Provider";case od:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case id:return t=e.displayName||null,t!==null?t:Dc(e.type)||"Memo";case zn:t=e._payload,e=e._init;try{return Dc(e(t))}catch{}}return null}function Qw(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Dc(t);case 8:return t===sd?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function ar(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Nh(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Yw(e){var t=Nh(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var s=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return s.call(this)},set:function(a){r=""+a,i.call(this,a)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(a){r=""+a},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Ti(e){e._valueTracker||(e._valueTracker=Yw(e))}function bh(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Nh(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function xa(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Lc(e,t){var n=t.checked;return Ce({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Yf(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=ar(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Sh(e,t){t=t.checked,t!=null&&rd(e,"checked",t,!1)}function Fc(e,t){Sh(e,t);var n=ar(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?zc(e,t.type,n):t.hasOwnProperty("defaultValue")&&zc(e,t.type,ar(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function qf(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function zc(e,t,n){(t!=="number"||xa(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var So=Array.isArray;function ws(e,t,n,r){if(e=e.options,t){t={};for(var s=0;s<n.length;s++)t["$"+n[s]]=!0;for(n=0;n<e.length;n++)s=t.hasOwnProperty("$"+e[n].value),e[n].selected!==s&&(e[n].selected=s),s&&r&&(e[n].defaultSelected=!0)}else{for(n=""+ar(n),t=null,s=0;s<e.length;s++){if(e[s].value===n){e[s].selected=!0,r&&(e[s].defaultSelected=!0);return}t!==null||e[s].disabled||(t=e[s])}t!==null&&(t.selected=!0)}}function $c(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(R(91));return Ce({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Xf(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(R(92));if(So(n)){if(1<n.length)throw Error(R(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:ar(n)}}function Ch(e,t){var n=ar(t.value),r=ar(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Jf(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Eh(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Uc(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Eh(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Ri,kh=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,s){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,s)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Ri=Ri||document.createElement("div"),Ri.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Ri.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function $o(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var To={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},qw=["Webkit","ms","Moz","O"];Object.keys(To).forEach(function(e){qw.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),To[t]=To[e]})});function Ph(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||To.hasOwnProperty(e)&&To[e]?(""+t).trim():t+"px"}function Th(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,s=Ph(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,s):e[n]=s}}var Xw=Ce({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Bc(e,t){if(t){if(Xw[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(R(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(R(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(R(61))}if(t.style!=null&&typeof t.style!="object")throw Error(R(62))}}function Vc(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Hc=null;function ad(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Wc=null,js=null,Ns=null;function Zf(e){if(e=xi(e)){if(typeof Wc!="function")throw Error(R(280));var t=e.stateNode;t&&(t=ol(t),Wc(e.stateNode,e.type,t))}}function Rh(e){js?Ns?Ns.push(e):Ns=[e]:js=e}function Mh(){if(js){var e=js,t=Ns;if(Ns=js=null,Zf(e),t)for(e=0;e<t.length;e++)Zf(t[e])}}function Ah(e,t){return e(t)}function Ih(){}var Ql=!1;function _h(e,t,n){if(Ql)return e(t,n);Ql=!0;try{return Ah(e,t,n)}finally{Ql=!1,(js!==null||Ns!==null)&&(Ih(),Mh())}}function Uo(e,t){var n=e.stateNode;if(n===null)return null;var r=ol(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(R(231,t,typeof n));return n}var Kc=!1;if(yn)try{var co={};Object.defineProperty(co,"passive",{get:function(){Kc=!0}}),window.addEventListener("test",co,co),window.removeEventListener("test",co,co)}catch{Kc=!1}function Jw(e,t,n,r,s,i,a,l,c){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(p){this.onError(p)}}var Ro=!1,ga=null,va=!1,Gc=null,Zw={onError:function(e){Ro=!0,ga=e}};function e1(e,t,n,r,s,i,a,l,c){Ro=!1,ga=null,Jw.apply(Zw,arguments)}function t1(e,t,n,r,s,i,a,l,c){if(e1.apply(this,arguments),Ro){if(Ro){var u=ga;Ro=!1,ga=null}else throw Error(R(198));va||(va=!0,Gc=u)}}function Kr(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Oh(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function em(e){if(Kr(e)!==e)throw Error(R(188))}function n1(e){var t=e.alternate;if(!t){if(t=Kr(e),t===null)throw Error(R(188));return t!==e?null:e}for(var n=e,r=t;;){var s=n.return;if(s===null)break;var i=s.alternate;if(i===null){if(r=s.return,r!==null){n=r;continue}break}if(s.child===i.child){for(i=s.child;i;){if(i===n)return em(s),e;if(i===r)return em(s),t;i=i.sibling}throw Error(R(188))}if(n.return!==r.return)n=s,r=i;else{for(var a=!1,l=s.child;l;){if(l===n){a=!0,n=s,r=i;break}if(l===r){a=!0,r=s,n=i;break}l=l.sibling}if(!a){for(l=i.child;l;){if(l===n){a=!0,n=i,r=s;break}if(l===r){a=!0,r=i,n=s;break}l=l.sibling}if(!a)throw Error(R(189))}}if(n.alternate!==r)throw Error(R(190))}if(n.tag!==3)throw Error(R(188));return n.stateNode.current===n?e:t}function Dh(e){return e=n1(e),e!==null?Lh(e):null}function Lh(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Lh(e);if(t!==null)return t;e=e.sibling}return null}var Fh=jt.unstable_scheduleCallback,tm=jt.unstable_cancelCallback,r1=jt.unstable_shouldYield,s1=jt.unstable_requestPaint,Te=jt.unstable_now,o1=jt.unstable_getCurrentPriorityLevel,ld=jt.unstable_ImmediatePriority,zh=jt.unstable_UserBlockingPriority,ya=jt.unstable_NormalPriority,i1=jt.unstable_LowPriority,$h=jt.unstable_IdlePriority,tl=null,sn=null;function a1(e){if(sn&&typeof sn.onCommitFiberRoot=="function")try{sn.onCommitFiberRoot(tl,e,void 0,(e.current.flags&128)===128)}catch{}}var Vt=Math.clz32?Math.clz32:u1,l1=Math.log,c1=Math.LN2;function u1(e){return e>>>=0,e===0?32:31-(l1(e)/c1|0)|0}var Mi=64,Ai=4194304;function Co(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function wa(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,s=e.suspendedLanes,i=e.pingedLanes,a=n&268435455;if(a!==0){var l=a&~s;l!==0?r=Co(l):(i&=a,i!==0&&(r=Co(i)))}else a=n&~s,a!==0?r=Co(a):i!==0&&(r=Co(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&s)&&(s=r&-r,i=t&-t,s>=i||s===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Vt(t),s=1<<n,r|=e[n],t&=~s;return r}function d1(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function f1(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,s=e.expirationTimes,i=e.pendingLanes;0<i;){var a=31-Vt(i),l=1<<a,c=s[a];c===-1?(!(l&n)||l&r)&&(s[a]=d1(l,t)):c<=t&&(e.expiredLanes|=l),i&=~l}}function Qc(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Uh(){var e=Mi;return Mi<<=1,!(Mi&4194240)&&(Mi=64),e}function Yl(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function pi(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Vt(t),e[t]=n}function m1(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var s=31-Vt(n),i=1<<s;t[s]=0,r[s]=-1,e[s]=-1,n&=~i}}function cd(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Vt(n),s=1<<r;s&t|e[r]&t&&(e[r]|=t),n&=~s}}var fe=0;function Bh(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Vh,ud,Hh,Wh,Kh,Yc=!1,Ii=[],Xn=null,Jn=null,Zn=null,Bo=new Map,Vo=new Map,Un=[],p1="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function nm(e,t){switch(e){case"focusin":case"focusout":Xn=null;break;case"dragenter":case"dragleave":Jn=null;break;case"mouseover":case"mouseout":Zn=null;break;case"pointerover":case"pointerout":Bo.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Vo.delete(t.pointerId)}}function uo(e,t,n,r,s,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[s]},t!==null&&(t=xi(t),t!==null&&ud(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,s!==null&&t.indexOf(s)===-1&&t.push(s),e)}function h1(e,t,n,r,s){switch(t){case"focusin":return Xn=uo(Xn,e,t,n,r,s),!0;case"dragenter":return Jn=uo(Jn,e,t,n,r,s),!0;case"mouseover":return Zn=uo(Zn,e,t,n,r,s),!0;case"pointerover":var i=s.pointerId;return Bo.set(i,uo(Bo.get(i)||null,e,t,n,r,s)),!0;case"gotpointercapture":return i=s.pointerId,Vo.set(i,uo(Vo.get(i)||null,e,t,n,r,s)),!0}return!1}function Gh(e){var t=Sr(e.target);if(t!==null){var n=Kr(t);if(n!==null){if(t=n.tag,t===13){if(t=Oh(n),t!==null){e.blockedOn=t,Kh(e.priority,function(){Hh(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ta(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=qc(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Hc=r,n.target.dispatchEvent(r),Hc=null}else return t=xi(n),t!==null&&ud(t),e.blockedOn=n,!1;t.shift()}return!0}function rm(e,t,n){ta(e)&&n.delete(t)}function x1(){Yc=!1,Xn!==null&&ta(Xn)&&(Xn=null),Jn!==null&&ta(Jn)&&(Jn=null),Zn!==null&&ta(Zn)&&(Zn=null),Bo.forEach(rm),Vo.forEach(rm)}function fo(e,t){e.blockedOn===t&&(e.blockedOn=null,Yc||(Yc=!0,jt.unstable_scheduleCallback(jt.unstable_NormalPriority,x1)))}function Ho(e){function t(s){return fo(s,e)}if(0<Ii.length){fo(Ii[0],e);for(var n=1;n<Ii.length;n++){var r=Ii[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Xn!==null&&fo(Xn,e),Jn!==null&&fo(Jn,e),Zn!==null&&fo(Zn,e),Bo.forEach(t),Vo.forEach(t),n=0;n<Un.length;n++)r=Un[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Un.length&&(n=Un[0],n.blockedOn===null);)Gh(n),n.blockedOn===null&&Un.shift()}var bs=Tn.ReactCurrentBatchConfig,ja=!0;function g1(e,t,n,r){var s=fe,i=bs.transition;bs.transition=null;try{fe=1,dd(e,t,n,r)}finally{fe=s,bs.transition=i}}function v1(e,t,n,r){var s=fe,i=bs.transition;bs.transition=null;try{fe=4,dd(e,t,n,r)}finally{fe=s,bs.transition=i}}function dd(e,t,n,r){if(ja){var s=qc(e,t,n,r);if(s===null)oc(e,t,r,Na,n),nm(e,r);else if(h1(s,e,t,n,r))r.stopPropagation();else if(nm(e,r),t&4&&-1<p1.indexOf(e)){for(;s!==null;){var i=xi(s);if(i!==null&&Vh(i),i=qc(e,t,n,r),i===null&&oc(e,t,r,Na,n),i===s)break;s=i}s!==null&&r.stopPropagation()}else oc(e,t,r,null,n)}}var Na=null;function qc(e,t,n,r){if(Na=null,e=ad(r),e=Sr(e),e!==null)if(t=Kr(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Oh(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Na=e,null}function Qh(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(o1()){case ld:return 1;case zh:return 4;case ya:case i1:return 16;case $h:return 536870912;default:return 16}default:return 16}}var Qn=null,fd=null,na=null;function Yh(){if(na)return na;var e,t=fd,n=t.length,r,s="value"in Qn?Qn.value:Qn.textContent,i=s.length;for(e=0;e<n&&t[e]===s[e];e++);var a=n-e;for(r=1;r<=a&&t[n-r]===s[i-r];r++);return na=s.slice(e,1<r?1-r:void 0)}function ra(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function _i(){return!0}function sm(){return!1}function bt(e){function t(n,r,s,i,a){this._reactName=n,this._targetInst=s,this.type=r,this.nativeEvent=i,this.target=a,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(i):i[l]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?_i:sm,this.isPropagationStopped=sm,this}return Ce(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=_i)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=_i)},persist:function(){},isPersistent:_i}),t}var Js={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},md=bt(Js),hi=Ce({},Js,{view:0,detail:0}),y1=bt(hi),ql,Xl,mo,nl=Ce({},hi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:pd,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==mo&&(mo&&e.type==="mousemove"?(ql=e.screenX-mo.screenX,Xl=e.screenY-mo.screenY):Xl=ql=0,mo=e),ql)},movementY:function(e){return"movementY"in e?e.movementY:Xl}}),om=bt(nl),w1=Ce({},nl,{dataTransfer:0}),j1=bt(w1),N1=Ce({},hi,{relatedTarget:0}),Jl=bt(N1),b1=Ce({},Js,{animationName:0,elapsedTime:0,pseudoElement:0}),S1=bt(b1),C1=Ce({},Js,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),E1=bt(C1),k1=Ce({},Js,{data:0}),im=bt(k1),P1={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},T1={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},R1={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function M1(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=R1[e])?!!t[e]:!1}function pd(){return M1}var A1=Ce({},hi,{key:function(e){if(e.key){var t=P1[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=ra(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?T1[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:pd,charCode:function(e){return e.type==="keypress"?ra(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?ra(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),I1=bt(A1),_1=Ce({},nl,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),am=bt(_1),O1=Ce({},hi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:pd}),D1=bt(O1),L1=Ce({},Js,{propertyName:0,elapsedTime:0,pseudoElement:0}),F1=bt(L1),z1=Ce({},nl,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),$1=bt(z1),U1=[9,13,27,32],hd=yn&&"CompositionEvent"in window,Mo=null;yn&&"documentMode"in document&&(Mo=document.documentMode);var B1=yn&&"TextEvent"in window&&!Mo,qh=yn&&(!hd||Mo&&8<Mo&&11>=Mo),lm=" ",cm=!1;function Xh(e,t){switch(e){case"keyup":return U1.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Jh(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var cs=!1;function V1(e,t){switch(e){case"compositionend":return Jh(t);case"keypress":return t.which!==32?null:(cm=!0,lm);case"textInput":return e=t.data,e===lm&&cm?null:e;default:return null}}function H1(e,t){if(cs)return e==="compositionend"||!hd&&Xh(e,t)?(e=Yh(),na=fd=Qn=null,cs=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return qh&&t.locale!=="ko"?null:t.data;default:return null}}var W1={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function um(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!W1[e.type]:t==="textarea"}function Zh(e,t,n,r){Rh(r),t=ba(t,"onChange"),0<t.length&&(n=new md("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Ao=null,Wo=null;function K1(e){ux(e,0)}function rl(e){var t=fs(e);if(bh(t))return e}function G1(e,t){if(e==="change")return t}var ex=!1;if(yn){var Zl;if(yn){var ec="oninput"in document;if(!ec){var dm=document.createElement("div");dm.setAttribute("oninput","return;"),ec=typeof dm.oninput=="function"}Zl=ec}else Zl=!1;ex=Zl&&(!document.documentMode||9<document.documentMode)}function fm(){Ao&&(Ao.detachEvent("onpropertychange",tx),Wo=Ao=null)}function tx(e){if(e.propertyName==="value"&&rl(Wo)){var t=[];Zh(t,Wo,e,ad(e)),_h(K1,t)}}function Q1(e,t,n){e==="focusin"?(fm(),Ao=t,Wo=n,Ao.attachEvent("onpropertychange",tx)):e==="focusout"&&fm()}function Y1(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return rl(Wo)}function q1(e,t){if(e==="click")return rl(t)}function X1(e,t){if(e==="input"||e==="change")return rl(t)}function J1(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Wt=typeof Object.is=="function"?Object.is:J1;function Ko(e,t){if(Wt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var s=n[r];if(!Ac.call(t,s)||!Wt(e[s],t[s]))return!1}return!0}function mm(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function pm(e,t){var n=mm(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=mm(n)}}function nx(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?nx(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function rx(){for(var e=window,t=xa();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=xa(e.document)}return t}function xd(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Z1(e){var t=rx(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&nx(n.ownerDocument.documentElement,n)){if(r!==null&&xd(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var s=n.textContent.length,i=Math.min(r.start,s);r=r.end===void 0?i:Math.min(r.end,s),!e.extend&&i>r&&(s=r,r=i,i=s),s=pm(n,i);var a=pm(n,r);s&&a&&(e.rangeCount!==1||e.anchorNode!==s.node||e.anchorOffset!==s.offset||e.focusNode!==a.node||e.focusOffset!==a.offset)&&(t=t.createRange(),t.setStart(s.node,s.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(a.node,a.offset)):(t.setEnd(a.node,a.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var ej=yn&&"documentMode"in document&&11>=document.documentMode,us=null,Xc=null,Io=null,Jc=!1;function hm(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Jc||us==null||us!==xa(r)||(r=us,"selectionStart"in r&&xd(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Io&&Ko(Io,r)||(Io=r,r=ba(Xc,"onSelect"),0<r.length&&(t=new md("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=us)))}function Oi(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var ds={animationend:Oi("Animation","AnimationEnd"),animationiteration:Oi("Animation","AnimationIteration"),animationstart:Oi("Animation","AnimationStart"),transitionend:Oi("Transition","TransitionEnd")},tc={},sx={};yn&&(sx=document.createElement("div").style,"AnimationEvent"in window||(delete ds.animationend.animation,delete ds.animationiteration.animation,delete ds.animationstart.animation),"TransitionEvent"in window||delete ds.transitionend.transition);function sl(e){if(tc[e])return tc[e];if(!ds[e])return e;var t=ds[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in sx)return tc[e]=t[n];return e}var ox=sl("animationend"),ix=sl("animationiteration"),ax=sl("animationstart"),lx=sl("transitionend"),cx=new Map,xm="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function mr(e,t){cx.set(e,t),Wr(t,[e])}for(var nc=0;nc<xm.length;nc++){var rc=xm[nc],tj=rc.toLowerCase(),nj=rc[0].toUpperCase()+rc.slice(1);mr(tj,"on"+nj)}mr(ox,"onAnimationEnd");mr(ix,"onAnimationIteration");mr(ax,"onAnimationStart");mr("dblclick","onDoubleClick");mr("focusin","onFocus");mr("focusout","onBlur");mr(lx,"onTransitionEnd");zs("onMouseEnter",["mouseout","mouseover"]);zs("onMouseLeave",["mouseout","mouseover"]);zs("onPointerEnter",["pointerout","pointerover"]);zs("onPointerLeave",["pointerout","pointerover"]);Wr("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Wr("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Wr("onBeforeInput",["compositionend","keypress","textInput","paste"]);Wr("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Wr("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Wr("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Eo="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),rj=new Set("cancel close invalid load scroll toggle".split(" ").concat(Eo));function gm(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,t1(r,t,void 0,e),e.currentTarget=null}function ux(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],s=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var a=r.length-1;0<=a;a--){var l=r[a],c=l.instance,u=l.currentTarget;if(l=l.listener,c!==i&&s.isPropagationStopped())break e;gm(s,l,u),i=c}else for(a=0;a<r.length;a++){if(l=r[a],c=l.instance,u=l.currentTarget,l=l.listener,c!==i&&s.isPropagationStopped())break e;gm(s,l,u),i=c}}}if(va)throw e=Gc,va=!1,Gc=null,e}function ye(e,t){var n=t[ru];n===void 0&&(n=t[ru]=new Set);var r=e+"__bubble";n.has(r)||(dx(t,e,2,!1),n.add(r))}function sc(e,t,n){var r=0;t&&(r|=4),dx(n,e,r,t)}var Di="_reactListening"+Math.random().toString(36).slice(2);function Go(e){if(!e[Di]){e[Di]=!0,vh.forEach(function(n){n!=="selectionchange"&&(rj.has(n)||sc(n,!1,e),sc(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Di]||(t[Di]=!0,sc("selectionchange",!1,t))}}function dx(e,t,n,r){switch(Qh(t)){case 1:var s=g1;break;case 4:s=v1;break;default:s=dd}n=s.bind(null,t,n,e),s=void 0,!Kc||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(s=!0),r?s!==void 0?e.addEventListener(t,n,{capture:!0,passive:s}):e.addEventListener(t,n,!0):s!==void 0?e.addEventListener(t,n,{passive:s}):e.addEventListener(t,n,!1)}function oc(e,t,n,r,s){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var a=r.tag;if(a===3||a===4){var l=r.stateNode.containerInfo;if(l===s||l.nodeType===8&&l.parentNode===s)break;if(a===4)for(a=r.return;a!==null;){var c=a.tag;if((c===3||c===4)&&(c=a.stateNode.containerInfo,c===s||c.nodeType===8&&c.parentNode===s))return;a=a.return}for(;l!==null;){if(a=Sr(l),a===null)return;if(c=a.tag,c===5||c===6){r=i=a;continue e}l=l.parentNode}}r=r.return}_h(function(){var u=i,p=ad(n),m=[];e:{var d=cx.get(e);if(d!==void 0){var v=md,w=e;switch(e){case"keypress":if(ra(n)===0)break e;case"keydown":case"keyup":v=I1;break;case"focusin":w="focus",v=Jl;break;case"focusout":w="blur",v=Jl;break;case"beforeblur":case"afterblur":v=Jl;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":v=om;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":v=j1;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":v=D1;break;case ox:case ix:case ax:v=S1;break;case lx:v=F1;break;case"scroll":v=y1;break;case"wheel":v=$1;break;case"copy":case"cut":case"paste":v=E1;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":v=am}var h=(t&4)!==0,j=!h&&e==="scroll",g=h?d!==null?d+"Capture":null:d;h=[];for(var x=u,y;x!==null;){y=x;var N=y.stateNode;if(y.tag===5&&N!==null&&(y=N,g!==null&&(N=Uo(x,g),N!=null&&h.push(Qo(x,N,y)))),j)break;x=x.return}0<h.length&&(d=new v(d,w,null,n,p),m.push({event:d,listeners:h}))}}if(!(t&7)){e:{if(d=e==="mouseover"||e==="pointerover",v=e==="mouseout"||e==="pointerout",d&&n!==Hc&&(w=n.relatedTarget||n.fromElement)&&(Sr(w)||w[wn]))break e;if((v||d)&&(d=p.window===p?p:(d=p.ownerDocument)?d.defaultView||d.parentWindow:window,v?(w=n.relatedTarget||n.toElement,v=u,w=w?Sr(w):null,w!==null&&(j=Kr(w),w!==j||w.tag!==5&&w.tag!==6)&&(w=null)):(v=null,w=u),v!==w)){if(h=om,N="onMouseLeave",g="onMouseEnter",x="mouse",(e==="pointerout"||e==="pointerover")&&(h=am,N="onPointerLeave",g="onPointerEnter",x="pointer"),j=v==null?d:fs(v),y=w==null?d:fs(w),d=new h(N,x+"leave",v,n,p),d.target=j,d.relatedTarget=y,N=null,Sr(p)===u&&(h=new h(g,x+"enter",w,n,p),h.target=y,h.relatedTarget=j,N=h),j=N,v&&w)t:{for(h=v,g=w,x=0,y=h;y;y=rs(y))x++;for(y=0,N=g;N;N=rs(N))y++;for(;0<x-y;)h=rs(h),x--;for(;0<y-x;)g=rs(g),y--;for(;x--;){if(h===g||g!==null&&h===g.alternate)break t;h=rs(h),g=rs(g)}h=null}else h=null;v!==null&&vm(m,d,v,h,!1),w!==null&&j!==null&&vm(m,j,w,h,!0)}}e:{if(d=u?fs(u):window,v=d.nodeName&&d.nodeName.toLowerCase(),v==="select"||v==="input"&&d.type==="file")var b=G1;else if(um(d))if(ex)b=X1;else{b=Y1;var E=Q1}else(v=d.nodeName)&&v.toLowerCase()==="input"&&(d.type==="checkbox"||d.type==="radio")&&(b=q1);if(b&&(b=b(e,u))){Zh(m,b,n,p);break e}E&&E(e,d,u),e==="focusout"&&(E=d._wrapperState)&&E.controlled&&d.type==="number"&&zc(d,"number",d.value)}switch(E=u?fs(u):window,e){case"focusin":(um(E)||E.contentEditable==="true")&&(us=E,Xc=u,Io=null);break;case"focusout":Io=Xc=us=null;break;case"mousedown":Jc=!0;break;case"contextmenu":case"mouseup":case"dragend":Jc=!1,hm(m,n,p);break;case"selectionchange":if(ej)break;case"keydown":case"keyup":hm(m,n,p)}var C;if(hd)e:{switch(e){case"compositionstart":var k="onCompositionStart";break e;case"compositionend":k="onCompositionEnd";break e;case"compositionupdate":k="onCompositionUpdate";break e}k=void 0}else cs?Xh(e,n)&&(k="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(k="onCompositionStart");k&&(qh&&n.locale!=="ko"&&(cs||k!=="onCompositionStart"?k==="onCompositionEnd"&&cs&&(C=Yh()):(Qn=p,fd="value"in Qn?Qn.value:Qn.textContent,cs=!0)),E=ba(u,k),0<E.length&&(k=new im(k,e,null,n,p),m.push({event:k,listeners:E}),C?k.data=C:(C=Jh(n),C!==null&&(k.data=C)))),(C=B1?V1(e,n):H1(e,n))&&(u=ba(u,"onBeforeInput"),0<u.length&&(p=new im("onBeforeInput","beforeinput",null,n,p),m.push({event:p,listeners:u}),p.data=C))}ux(m,t)})}function Qo(e,t,n){return{instance:e,listener:t,currentTarget:n}}function ba(e,t){for(var n=t+"Capture",r=[];e!==null;){var s=e,i=s.stateNode;s.tag===5&&i!==null&&(s=i,i=Uo(e,n),i!=null&&r.unshift(Qo(e,i,s)),i=Uo(e,t),i!=null&&r.push(Qo(e,i,s))),e=e.return}return r}function rs(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function vm(e,t,n,r,s){for(var i=t._reactName,a=[];n!==null&&n!==r;){var l=n,c=l.alternate,u=l.stateNode;if(c!==null&&c===r)break;l.tag===5&&u!==null&&(l=u,s?(c=Uo(n,i),c!=null&&a.unshift(Qo(n,c,l))):s||(c=Uo(n,i),c!=null&&a.push(Qo(n,c,l)))),n=n.return}a.length!==0&&e.push({event:t,listeners:a})}var sj=/\r\n?/g,oj=/\u0000|\uFFFD/g;function ym(e){return(typeof e=="string"?e:""+e).replace(sj,`
`).replace(oj,"")}function Li(e,t,n){if(t=ym(t),ym(e)!==t&&n)throw Error(R(425))}function Sa(){}var Zc=null,eu=null;function tu(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var nu=typeof setTimeout=="function"?setTimeout:void 0,ij=typeof clearTimeout=="function"?clearTimeout:void 0,wm=typeof Promise=="function"?Promise:void 0,aj=typeof queueMicrotask=="function"?queueMicrotask:typeof wm<"u"?function(e){return wm.resolve(null).then(e).catch(lj)}:nu;function lj(e){setTimeout(function(){throw e})}function ic(e,t){var n=t,r=0;do{var s=n.nextSibling;if(e.removeChild(n),s&&s.nodeType===8)if(n=s.data,n==="/$"){if(r===0){e.removeChild(s),Ho(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=s}while(n);Ho(t)}function er(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function jm(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Zs=Math.random().toString(36).slice(2),nn="__reactFiber$"+Zs,Yo="__reactProps$"+Zs,wn="__reactContainer$"+Zs,ru="__reactEvents$"+Zs,cj="__reactListeners$"+Zs,uj="__reactHandles$"+Zs;function Sr(e){var t=e[nn];if(t)return t;for(var n=e.parentNode;n;){if(t=n[wn]||n[nn]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=jm(e);e!==null;){if(n=e[nn])return n;e=jm(e)}return t}e=n,n=e.parentNode}return null}function xi(e){return e=e[nn]||e[wn],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function fs(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(R(33))}function ol(e){return e[Yo]||null}var su=[],ms=-1;function pr(e){return{current:e}}function we(e){0>ms||(e.current=su[ms],su[ms]=null,ms--)}function xe(e,t){ms++,su[ms]=e.current,e.current=t}var lr={},qe=pr(lr),ct=pr(!1),Or=lr;function $s(e,t){var n=e.type.contextTypes;if(!n)return lr;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var s={},i;for(i in n)s[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=s),s}function ut(e){return e=e.childContextTypes,e!=null}function Ca(){we(ct),we(qe)}function Nm(e,t,n){if(qe.current!==lr)throw Error(R(168));xe(qe,t),xe(ct,n)}function fx(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var s in r)if(!(s in t))throw Error(R(108,Qw(e)||"Unknown",s));return Ce({},n,r)}function Ea(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||lr,Or=qe.current,xe(qe,e),xe(ct,ct.current),!0}function bm(e,t,n){var r=e.stateNode;if(!r)throw Error(R(169));n?(e=fx(e,t,Or),r.__reactInternalMemoizedMergedChildContext=e,we(ct),we(qe),xe(qe,e)):we(ct),xe(ct,n)}var hn=null,il=!1,ac=!1;function mx(e){hn===null?hn=[e]:hn.push(e)}function dj(e){il=!0,mx(e)}function hr(){if(!ac&&hn!==null){ac=!0;var e=0,t=fe;try{var n=hn;for(fe=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}hn=null,il=!1}catch(s){throw hn!==null&&(hn=hn.slice(e+1)),Fh(ld,hr),s}finally{fe=t,ac=!1}}return null}var ps=[],hs=0,ka=null,Pa=0,kt=[],Pt=0,Dr=null,xn=1,gn="";function Nr(e,t){ps[hs++]=Pa,ps[hs++]=ka,ka=e,Pa=t}function px(e,t,n){kt[Pt++]=xn,kt[Pt++]=gn,kt[Pt++]=Dr,Dr=e;var r=xn;e=gn;var s=32-Vt(r)-1;r&=~(1<<s),n+=1;var i=32-Vt(t)+s;if(30<i){var a=s-s%5;i=(r&(1<<a)-1).toString(32),r>>=a,s-=a,xn=1<<32-Vt(t)+s|n<<s|r,gn=i+e}else xn=1<<i|n<<s|r,gn=e}function gd(e){e.return!==null&&(Nr(e,1),px(e,1,0))}function vd(e){for(;e===ka;)ka=ps[--hs],ps[hs]=null,Pa=ps[--hs],ps[hs]=null;for(;e===Dr;)Dr=kt[--Pt],kt[Pt]=null,gn=kt[--Pt],kt[Pt]=null,xn=kt[--Pt],kt[Pt]=null}var yt=null,vt=null,je=!1,Ut=null;function hx(e,t){var n=Rt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Sm(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,yt=e,vt=er(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,yt=e,vt=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Dr!==null?{id:xn,overflow:gn}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Rt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,yt=e,vt=null,!0):!1;default:return!1}}function ou(e){return(e.mode&1)!==0&&(e.flags&128)===0}function iu(e){if(je){var t=vt;if(t){var n=t;if(!Sm(e,t)){if(ou(e))throw Error(R(418));t=er(n.nextSibling);var r=yt;t&&Sm(e,t)?hx(r,n):(e.flags=e.flags&-4097|2,je=!1,yt=e)}}else{if(ou(e))throw Error(R(418));e.flags=e.flags&-4097|2,je=!1,yt=e}}}function Cm(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;yt=e}function Fi(e){if(e!==yt)return!1;if(!je)return Cm(e),je=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!tu(e.type,e.memoizedProps)),t&&(t=vt)){if(ou(e))throw xx(),Error(R(418));for(;t;)hx(e,t),t=er(t.nextSibling)}if(Cm(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(R(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){vt=er(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}vt=null}}else vt=yt?er(e.stateNode.nextSibling):null;return!0}function xx(){for(var e=vt;e;)e=er(e.nextSibling)}function Us(){vt=yt=null,je=!1}function yd(e){Ut===null?Ut=[e]:Ut.push(e)}var fj=Tn.ReactCurrentBatchConfig;function po(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(R(309));var r=n.stateNode}if(!r)throw Error(R(147,e));var s=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(a){var l=s.refs;a===null?delete l[i]:l[i]=a},t._stringRef=i,t)}if(typeof e!="string")throw Error(R(284));if(!n._owner)throw Error(R(290,e))}return e}function zi(e,t){throw e=Object.prototype.toString.call(t),Error(R(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Em(e){var t=e._init;return t(e._payload)}function gx(e){function t(g,x){if(e){var y=g.deletions;y===null?(g.deletions=[x],g.flags|=16):y.push(x)}}function n(g,x){if(!e)return null;for(;x!==null;)t(g,x),x=x.sibling;return null}function r(g,x){for(g=new Map;x!==null;)x.key!==null?g.set(x.key,x):g.set(x.index,x),x=x.sibling;return g}function s(g,x){return g=sr(g,x),g.index=0,g.sibling=null,g}function i(g,x,y){return g.index=y,e?(y=g.alternate,y!==null?(y=y.index,y<x?(g.flags|=2,x):y):(g.flags|=2,x)):(g.flags|=1048576,x)}function a(g){return e&&g.alternate===null&&(g.flags|=2),g}function l(g,x,y,N){return x===null||x.tag!==6?(x=pc(y,g.mode,N),x.return=g,x):(x=s(x,y),x.return=g,x)}function c(g,x,y,N){var b=y.type;return b===ls?p(g,x,y.props.children,N,y.key):x!==null&&(x.elementType===b||typeof b=="object"&&b!==null&&b.$$typeof===zn&&Em(b)===x.type)?(N=s(x,y.props),N.ref=po(g,x,y),N.return=g,N):(N=ua(y.type,y.key,y.props,null,g.mode,N),N.ref=po(g,x,y),N.return=g,N)}function u(g,x,y,N){return x===null||x.tag!==4||x.stateNode.containerInfo!==y.containerInfo||x.stateNode.implementation!==y.implementation?(x=hc(y,g.mode,N),x.return=g,x):(x=s(x,y.children||[]),x.return=g,x)}function p(g,x,y,N,b){return x===null||x.tag!==7?(x=_r(y,g.mode,N,b),x.return=g,x):(x=s(x,y),x.return=g,x)}function m(g,x,y){if(typeof x=="string"&&x!==""||typeof x=="number")return x=pc(""+x,g.mode,y),x.return=g,x;if(typeof x=="object"&&x!==null){switch(x.$$typeof){case Pi:return y=ua(x.type,x.key,x.props,null,g.mode,y),y.ref=po(g,null,x),y.return=g,y;case as:return x=hc(x,g.mode,y),x.return=g,x;case zn:var N=x._init;return m(g,N(x._payload),y)}if(So(x)||lo(x))return x=_r(x,g.mode,y,null),x.return=g,x;zi(g,x)}return null}function d(g,x,y,N){var b=x!==null?x.key:null;if(typeof y=="string"&&y!==""||typeof y=="number")return b!==null?null:l(g,x,""+y,N);if(typeof y=="object"&&y!==null){switch(y.$$typeof){case Pi:return y.key===b?c(g,x,y,N):null;case as:return y.key===b?u(g,x,y,N):null;case zn:return b=y._init,d(g,x,b(y._payload),N)}if(So(y)||lo(y))return b!==null?null:p(g,x,y,N,null);zi(g,y)}return null}function v(g,x,y,N,b){if(typeof N=="string"&&N!==""||typeof N=="number")return g=g.get(y)||null,l(x,g,""+N,b);if(typeof N=="object"&&N!==null){switch(N.$$typeof){case Pi:return g=g.get(N.key===null?y:N.key)||null,c(x,g,N,b);case as:return g=g.get(N.key===null?y:N.key)||null,u(x,g,N,b);case zn:var E=N._init;return v(g,x,y,E(N._payload),b)}if(So(N)||lo(N))return g=g.get(y)||null,p(x,g,N,b,null);zi(x,N)}return null}function w(g,x,y,N){for(var b=null,E=null,C=x,k=x=0,I=null;C!==null&&k<y.length;k++){C.index>k?(I=C,C=null):I=C.sibling;var M=d(g,C,y[k],N);if(M===null){C===null&&(C=I);break}e&&C&&M.alternate===null&&t(g,C),x=i(M,x,k),E===null?b=M:E.sibling=M,E=M,C=I}if(k===y.length)return n(g,C),je&&Nr(g,k),b;if(C===null){for(;k<y.length;k++)C=m(g,y[k],N),C!==null&&(x=i(C,x,k),E===null?b=C:E.sibling=C,E=C);return je&&Nr(g,k),b}for(C=r(g,C);k<y.length;k++)I=v(C,g,k,y[k],N),I!==null&&(e&&I.alternate!==null&&C.delete(I.key===null?k:I.key),x=i(I,x,k),E===null?b=I:E.sibling=I,E=I);return e&&C.forEach(function(U){return t(g,U)}),je&&Nr(g,k),b}function h(g,x,y,N){var b=lo(y);if(typeof b!="function")throw Error(R(150));if(y=b.call(y),y==null)throw Error(R(151));for(var E=b=null,C=x,k=x=0,I=null,M=y.next();C!==null&&!M.done;k++,M=y.next()){C.index>k?(I=C,C=null):I=C.sibling;var U=d(g,C,M.value,N);if(U===null){C===null&&(C=I);break}e&&C&&U.alternate===null&&t(g,C),x=i(U,x,k),E===null?b=U:E.sibling=U,E=U,C=I}if(M.done)return n(g,C),je&&Nr(g,k),b;if(C===null){for(;!M.done;k++,M=y.next())M=m(g,M.value,N),M!==null&&(x=i(M,x,k),E===null?b=M:E.sibling=M,E=M);return je&&Nr(g,k),b}for(C=r(g,C);!M.done;k++,M=y.next())M=v(C,g,k,M.value,N),M!==null&&(e&&M.alternate!==null&&C.delete(M.key===null?k:M.key),x=i(M,x,k),E===null?b=M:E.sibling=M,E=M);return e&&C.forEach(function(D){return t(g,D)}),je&&Nr(g,k),b}function j(g,x,y,N){if(typeof y=="object"&&y!==null&&y.type===ls&&y.key===null&&(y=y.props.children),typeof y=="object"&&y!==null){switch(y.$$typeof){case Pi:e:{for(var b=y.key,E=x;E!==null;){if(E.key===b){if(b=y.type,b===ls){if(E.tag===7){n(g,E.sibling),x=s(E,y.props.children),x.return=g,g=x;break e}}else if(E.elementType===b||typeof b=="object"&&b!==null&&b.$$typeof===zn&&Em(b)===E.type){n(g,E.sibling),x=s(E,y.props),x.ref=po(g,E,y),x.return=g,g=x;break e}n(g,E);break}else t(g,E);E=E.sibling}y.type===ls?(x=_r(y.props.children,g.mode,N,y.key),x.return=g,g=x):(N=ua(y.type,y.key,y.props,null,g.mode,N),N.ref=po(g,x,y),N.return=g,g=N)}return a(g);case as:e:{for(E=y.key;x!==null;){if(x.key===E)if(x.tag===4&&x.stateNode.containerInfo===y.containerInfo&&x.stateNode.implementation===y.implementation){n(g,x.sibling),x=s(x,y.children||[]),x.return=g,g=x;break e}else{n(g,x);break}else t(g,x);x=x.sibling}x=hc(y,g.mode,N),x.return=g,g=x}return a(g);case zn:return E=y._init,j(g,x,E(y._payload),N)}if(So(y))return w(g,x,y,N);if(lo(y))return h(g,x,y,N);zi(g,y)}return typeof y=="string"&&y!==""||typeof y=="number"?(y=""+y,x!==null&&x.tag===6?(n(g,x.sibling),x=s(x,y),x.return=g,g=x):(n(g,x),x=pc(y,g.mode,N),x.return=g,g=x),a(g)):n(g,x)}return j}var Bs=gx(!0),vx=gx(!1),Ta=pr(null),Ra=null,xs=null,wd=null;function jd(){wd=xs=Ra=null}function Nd(e){var t=Ta.current;we(Ta),e._currentValue=t}function au(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Ss(e,t){Ra=e,wd=xs=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(lt=!0),e.firstContext=null)}function It(e){var t=e._currentValue;if(wd!==e)if(e={context:e,memoizedValue:t,next:null},xs===null){if(Ra===null)throw Error(R(308));xs=e,Ra.dependencies={lanes:0,firstContext:e}}else xs=xs.next=e;return t}var Cr=null;function bd(e){Cr===null?Cr=[e]:Cr.push(e)}function yx(e,t,n,r){var s=t.interleaved;return s===null?(n.next=n,bd(t)):(n.next=s.next,s.next=n),t.interleaved=n,jn(e,r)}function jn(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var $n=!1;function Sd(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function wx(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function vn(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function tr(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,oe&2){var s=r.pending;return s===null?t.next=t:(t.next=s.next,s.next=t),r.pending=t,jn(e,n)}return s=r.interleaved,s===null?(t.next=t,bd(r)):(t.next=s.next,s.next=t),r.interleaved=t,jn(e,n)}function sa(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,cd(e,n)}}function km(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var s=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var a={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?s=i=a:i=i.next=a,n=n.next}while(n!==null);i===null?s=i=t:i=i.next=t}else s=i=t;n={baseState:r.baseState,firstBaseUpdate:s,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Ma(e,t,n,r){var s=e.updateQueue;$n=!1;var i=s.firstBaseUpdate,a=s.lastBaseUpdate,l=s.shared.pending;if(l!==null){s.shared.pending=null;var c=l,u=c.next;c.next=null,a===null?i=u:a.next=u,a=c;var p=e.alternate;p!==null&&(p=p.updateQueue,l=p.lastBaseUpdate,l!==a&&(l===null?p.firstBaseUpdate=u:l.next=u,p.lastBaseUpdate=c))}if(i!==null){var m=s.baseState;a=0,p=u=c=null,l=i;do{var d=l.lane,v=l.eventTime;if((r&d)===d){p!==null&&(p=p.next={eventTime:v,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var w=e,h=l;switch(d=t,v=n,h.tag){case 1:if(w=h.payload,typeof w=="function"){m=w.call(v,m,d);break e}m=w;break e;case 3:w.flags=w.flags&-65537|128;case 0:if(w=h.payload,d=typeof w=="function"?w.call(v,m,d):w,d==null)break e;m=Ce({},m,d);break e;case 2:$n=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,d=s.effects,d===null?s.effects=[l]:d.push(l))}else v={eventTime:v,lane:d,tag:l.tag,payload:l.payload,callback:l.callback,next:null},p===null?(u=p=v,c=m):p=p.next=v,a|=d;if(l=l.next,l===null){if(l=s.shared.pending,l===null)break;d=l,l=d.next,d.next=null,s.lastBaseUpdate=d,s.shared.pending=null}}while(!0);if(p===null&&(c=m),s.baseState=c,s.firstBaseUpdate=u,s.lastBaseUpdate=p,t=s.shared.interleaved,t!==null){s=t;do a|=s.lane,s=s.next;while(s!==t)}else i===null&&(s.shared.lanes=0);Fr|=a,e.lanes=a,e.memoizedState=m}}function Pm(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],s=r.callback;if(s!==null){if(r.callback=null,r=n,typeof s!="function")throw Error(R(191,s));s.call(r)}}}var gi={},on=pr(gi),qo=pr(gi),Xo=pr(gi);function Er(e){if(e===gi)throw Error(R(174));return e}function Cd(e,t){switch(xe(Xo,t),xe(qo,e),xe(on,gi),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Uc(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Uc(t,e)}we(on),xe(on,t)}function Vs(){we(on),we(qo),we(Xo)}function jx(e){Er(Xo.current);var t=Er(on.current),n=Uc(t,e.type);t!==n&&(xe(qo,e),xe(on,n))}function Ed(e){qo.current===e&&(we(on),we(qo))}var Ne=pr(0);function Aa(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var lc=[];function kd(){for(var e=0;e<lc.length;e++)lc[e]._workInProgressVersionPrimary=null;lc.length=0}var oa=Tn.ReactCurrentDispatcher,cc=Tn.ReactCurrentBatchConfig,Lr=0,Se=null,Ie=null,De=null,Ia=!1,_o=!1,Jo=0,mj=0;function Ke(){throw Error(R(321))}function Pd(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Wt(e[n],t[n]))return!1;return!0}function Td(e,t,n,r,s,i){if(Lr=i,Se=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,oa.current=e===null||e.memoizedState===null?gj:vj,e=n(r,s),_o){i=0;do{if(_o=!1,Jo=0,25<=i)throw Error(R(301));i+=1,De=Ie=null,t.updateQueue=null,oa.current=yj,e=n(r,s)}while(_o)}if(oa.current=_a,t=Ie!==null&&Ie.next!==null,Lr=0,De=Ie=Se=null,Ia=!1,t)throw Error(R(300));return e}function Rd(){var e=Jo!==0;return Jo=0,e}function Jt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return De===null?Se.memoizedState=De=e:De=De.next=e,De}function _t(){if(Ie===null){var e=Se.alternate;e=e!==null?e.memoizedState:null}else e=Ie.next;var t=De===null?Se.memoizedState:De.next;if(t!==null)De=t,Ie=e;else{if(e===null)throw Error(R(310));Ie=e,e={memoizedState:Ie.memoizedState,baseState:Ie.baseState,baseQueue:Ie.baseQueue,queue:Ie.queue,next:null},De===null?Se.memoizedState=De=e:De=De.next=e}return De}function Zo(e,t){return typeof t=="function"?t(e):t}function uc(e){var t=_t(),n=t.queue;if(n===null)throw Error(R(311));n.lastRenderedReducer=e;var r=Ie,s=r.baseQueue,i=n.pending;if(i!==null){if(s!==null){var a=s.next;s.next=i.next,i.next=a}r.baseQueue=s=i,n.pending=null}if(s!==null){i=s.next,r=r.baseState;var l=a=null,c=null,u=i;do{var p=u.lane;if((Lr&p)===p)c!==null&&(c=c.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var m={lane:p,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};c===null?(l=c=m,a=r):c=c.next=m,Se.lanes|=p,Fr|=p}u=u.next}while(u!==null&&u!==i);c===null?a=r:c.next=l,Wt(r,t.memoizedState)||(lt=!0),t.memoizedState=r,t.baseState=a,t.baseQueue=c,n.lastRenderedState=r}if(e=n.interleaved,e!==null){s=e;do i=s.lane,Se.lanes|=i,Fr|=i,s=s.next;while(s!==e)}else s===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function dc(e){var t=_t(),n=t.queue;if(n===null)throw Error(R(311));n.lastRenderedReducer=e;var r=n.dispatch,s=n.pending,i=t.memoizedState;if(s!==null){n.pending=null;var a=s=s.next;do i=e(i,a.action),a=a.next;while(a!==s);Wt(i,t.memoizedState)||(lt=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Nx(){}function bx(e,t){var n=Se,r=_t(),s=t(),i=!Wt(r.memoizedState,s);if(i&&(r.memoizedState=s,lt=!0),r=r.queue,Md(Ex.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||De!==null&&De.memoizedState.tag&1){if(n.flags|=2048,ei(9,Cx.bind(null,n,r,s,t),void 0,null),ze===null)throw Error(R(349));Lr&30||Sx(n,t,s)}return s}function Sx(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Se.updateQueue,t===null?(t={lastEffect:null,stores:null},Se.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Cx(e,t,n,r){t.value=n,t.getSnapshot=r,kx(t)&&Px(e)}function Ex(e,t,n){return n(function(){kx(t)&&Px(e)})}function kx(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Wt(e,n)}catch{return!0}}function Px(e){var t=jn(e,1);t!==null&&Ht(t,e,1,-1)}function Tm(e){var t=Jt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Zo,lastRenderedState:e},t.queue=e,e=e.dispatch=xj.bind(null,Se,e),[t.memoizedState,e]}function ei(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=Se.updateQueue,t===null?(t={lastEffect:null,stores:null},Se.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Tx(){return _t().memoizedState}function ia(e,t,n,r){var s=Jt();Se.flags|=e,s.memoizedState=ei(1|t,n,void 0,r===void 0?null:r)}function al(e,t,n,r){var s=_t();r=r===void 0?null:r;var i=void 0;if(Ie!==null){var a=Ie.memoizedState;if(i=a.destroy,r!==null&&Pd(r,a.deps)){s.memoizedState=ei(t,n,i,r);return}}Se.flags|=e,s.memoizedState=ei(1|t,n,i,r)}function Rm(e,t){return ia(8390656,8,e,t)}function Md(e,t){return al(2048,8,e,t)}function Rx(e,t){return al(4,2,e,t)}function Mx(e,t){return al(4,4,e,t)}function Ax(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Ix(e,t,n){return n=n!=null?n.concat([e]):null,al(4,4,Ax.bind(null,t,e),n)}function Ad(){}function _x(e,t){var n=_t();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Pd(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Ox(e,t){var n=_t();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Pd(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Dx(e,t,n){return Lr&21?(Wt(n,t)||(n=Uh(),Se.lanes|=n,Fr|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,lt=!0),e.memoizedState=n)}function pj(e,t){var n=fe;fe=n!==0&&4>n?n:4,e(!0);var r=cc.transition;cc.transition={};try{e(!1),t()}finally{fe=n,cc.transition=r}}function Lx(){return _t().memoizedState}function hj(e,t,n){var r=rr(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Fx(e))zx(t,n);else if(n=yx(e,t,n,r),n!==null){var s=rt();Ht(n,e,r,s),$x(n,t,r)}}function xj(e,t,n){var r=rr(e),s={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Fx(e))zx(t,s);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var a=t.lastRenderedState,l=i(a,n);if(s.hasEagerState=!0,s.eagerState=l,Wt(l,a)){var c=t.interleaved;c===null?(s.next=s,bd(t)):(s.next=c.next,c.next=s),t.interleaved=s;return}}catch{}finally{}n=yx(e,t,s,r),n!==null&&(s=rt(),Ht(n,e,r,s),$x(n,t,r))}}function Fx(e){var t=e.alternate;return e===Se||t!==null&&t===Se}function zx(e,t){_o=Ia=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function $x(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,cd(e,n)}}var _a={readContext:It,useCallback:Ke,useContext:Ke,useEffect:Ke,useImperativeHandle:Ke,useInsertionEffect:Ke,useLayoutEffect:Ke,useMemo:Ke,useReducer:Ke,useRef:Ke,useState:Ke,useDebugValue:Ke,useDeferredValue:Ke,useTransition:Ke,useMutableSource:Ke,useSyncExternalStore:Ke,useId:Ke,unstable_isNewReconciler:!1},gj={readContext:It,useCallback:function(e,t){return Jt().memoizedState=[e,t===void 0?null:t],e},useContext:It,useEffect:Rm,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,ia(4194308,4,Ax.bind(null,t,e),n)},useLayoutEffect:function(e,t){return ia(4194308,4,e,t)},useInsertionEffect:function(e,t){return ia(4,2,e,t)},useMemo:function(e,t){var n=Jt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Jt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=hj.bind(null,Se,e),[r.memoizedState,e]},useRef:function(e){var t=Jt();return e={current:e},t.memoizedState=e},useState:Tm,useDebugValue:Ad,useDeferredValue:function(e){return Jt().memoizedState=e},useTransition:function(){var e=Tm(!1),t=e[0];return e=pj.bind(null,e[1]),Jt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=Se,s=Jt();if(je){if(n===void 0)throw Error(R(407));n=n()}else{if(n=t(),ze===null)throw Error(R(349));Lr&30||Sx(r,t,n)}s.memoizedState=n;var i={value:n,getSnapshot:t};return s.queue=i,Rm(Ex.bind(null,r,i,e),[e]),r.flags|=2048,ei(9,Cx.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=Jt(),t=ze.identifierPrefix;if(je){var n=gn,r=xn;n=(r&~(1<<32-Vt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Jo++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=mj++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},vj={readContext:It,useCallback:_x,useContext:It,useEffect:Md,useImperativeHandle:Ix,useInsertionEffect:Rx,useLayoutEffect:Mx,useMemo:Ox,useReducer:uc,useRef:Tx,useState:function(){return uc(Zo)},useDebugValue:Ad,useDeferredValue:function(e){var t=_t();return Dx(t,Ie.memoizedState,e)},useTransition:function(){var e=uc(Zo)[0],t=_t().memoizedState;return[e,t]},useMutableSource:Nx,useSyncExternalStore:bx,useId:Lx,unstable_isNewReconciler:!1},yj={readContext:It,useCallback:_x,useContext:It,useEffect:Md,useImperativeHandle:Ix,useInsertionEffect:Rx,useLayoutEffect:Mx,useMemo:Ox,useReducer:dc,useRef:Tx,useState:function(){return dc(Zo)},useDebugValue:Ad,useDeferredValue:function(e){var t=_t();return Ie===null?t.memoizedState=e:Dx(t,Ie.memoizedState,e)},useTransition:function(){var e=dc(Zo)[0],t=_t().memoizedState;return[e,t]},useMutableSource:Nx,useSyncExternalStore:bx,useId:Lx,unstable_isNewReconciler:!1};function Lt(e,t){if(e&&e.defaultProps){t=Ce({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function lu(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:Ce({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var ll={isMounted:function(e){return(e=e._reactInternals)?Kr(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=rt(),s=rr(e),i=vn(r,s);i.payload=t,n!=null&&(i.callback=n),t=tr(e,i,s),t!==null&&(Ht(t,e,s,r),sa(t,e,s))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=rt(),s=rr(e),i=vn(r,s);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=tr(e,i,s),t!==null&&(Ht(t,e,s,r),sa(t,e,s))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=rt(),r=rr(e),s=vn(n,r);s.tag=2,t!=null&&(s.callback=t),t=tr(e,s,r),t!==null&&(Ht(t,e,r,n),sa(t,e,r))}};function Mm(e,t,n,r,s,i,a){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,a):t.prototype&&t.prototype.isPureReactComponent?!Ko(n,r)||!Ko(s,i):!0}function Ux(e,t,n){var r=!1,s=lr,i=t.contextType;return typeof i=="object"&&i!==null?i=It(i):(s=ut(t)?Or:qe.current,r=t.contextTypes,i=(r=r!=null)?$s(e,s):lr),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=ll,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=s,e.__reactInternalMemoizedMaskedChildContext=i),t}function Am(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ll.enqueueReplaceState(t,t.state,null)}function cu(e,t,n,r){var s=e.stateNode;s.props=n,s.state=e.memoizedState,s.refs={},Sd(e);var i=t.contextType;typeof i=="object"&&i!==null?s.context=It(i):(i=ut(t)?Or:qe.current,s.context=$s(e,i)),s.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(lu(e,t,i,n),s.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof s.getSnapshotBeforeUpdate=="function"||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(t=s.state,typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount(),t!==s.state&&ll.enqueueReplaceState(s,s.state,null),Ma(e,n,s,r),s.state=e.memoizedState),typeof s.componentDidMount=="function"&&(e.flags|=4194308)}function Hs(e,t){try{var n="",r=t;do n+=Gw(r),r=r.return;while(r);var s=n}catch(i){s=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:s,digest:null}}function fc(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function uu(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var wj=typeof WeakMap=="function"?WeakMap:Map;function Bx(e,t,n){n=vn(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Da||(Da=!0,wu=r),uu(e,t)},n}function Vx(e,t,n){n=vn(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var s=t.value;n.payload=function(){return r(s)},n.callback=function(){uu(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){uu(e,t),typeof r!="function"&&(nr===null?nr=new Set([this]):nr.add(this));var a=t.stack;this.componentDidCatch(t.value,{componentStack:a!==null?a:""})}),n}function Im(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new wj;var s=new Set;r.set(t,s)}else s=r.get(t),s===void 0&&(s=new Set,r.set(t,s));s.has(n)||(s.add(n),e=_j.bind(null,e,t,n),t.then(e,e))}function _m(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Om(e,t,n,r,s){return e.mode&1?(e.flags|=65536,e.lanes=s,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=vn(-1,1),t.tag=2,tr(n,t,1))),n.lanes|=1),e)}var jj=Tn.ReactCurrentOwner,lt=!1;function tt(e,t,n,r){t.child=e===null?vx(t,null,n,r):Bs(t,e.child,n,r)}function Dm(e,t,n,r,s){n=n.render;var i=t.ref;return Ss(t,s),r=Td(e,t,n,r,i,s),n=Rd(),e!==null&&!lt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,Nn(e,t,s)):(je&&n&&gd(t),t.flags|=1,tt(e,t,r,s),t.child)}function Lm(e,t,n,r,s){if(e===null){var i=n.type;return typeof i=="function"&&!$d(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,Hx(e,t,i,r,s)):(e=ua(n.type,null,r,t,t.mode,s),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&s)){var a=i.memoizedProps;if(n=n.compare,n=n!==null?n:Ko,n(a,r)&&e.ref===t.ref)return Nn(e,t,s)}return t.flags|=1,e=sr(i,r),e.ref=t.ref,e.return=t,t.child=e}function Hx(e,t,n,r,s){if(e!==null){var i=e.memoizedProps;if(Ko(i,r)&&e.ref===t.ref)if(lt=!1,t.pendingProps=r=i,(e.lanes&s)!==0)e.flags&131072&&(lt=!0);else return t.lanes=e.lanes,Nn(e,t,s)}return du(e,t,n,r,s)}function Wx(e,t,n){var r=t.pendingProps,s=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},xe(vs,xt),xt|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,xe(vs,xt),xt|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,xe(vs,xt),xt|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,xe(vs,xt),xt|=r;return tt(e,t,s,n),t.child}function Kx(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function du(e,t,n,r,s){var i=ut(n)?Or:qe.current;return i=$s(t,i),Ss(t,s),n=Td(e,t,n,r,i,s),r=Rd(),e!==null&&!lt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,Nn(e,t,s)):(je&&r&&gd(t),t.flags|=1,tt(e,t,n,s),t.child)}function Fm(e,t,n,r,s){if(ut(n)){var i=!0;Ea(t)}else i=!1;if(Ss(t,s),t.stateNode===null)aa(e,t),Ux(t,n,r),cu(t,n,r,s),r=!0;else if(e===null){var a=t.stateNode,l=t.memoizedProps;a.props=l;var c=a.context,u=n.contextType;typeof u=="object"&&u!==null?u=It(u):(u=ut(n)?Or:qe.current,u=$s(t,u));var p=n.getDerivedStateFromProps,m=typeof p=="function"||typeof a.getSnapshotBeforeUpdate=="function";m||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(l!==r||c!==u)&&Am(t,a,r,u),$n=!1;var d=t.memoizedState;a.state=d,Ma(t,r,a,s),c=t.memoizedState,l!==r||d!==c||ct.current||$n?(typeof p=="function"&&(lu(t,n,p,r),c=t.memoizedState),(l=$n||Mm(t,n,l,r,d,c,u))?(m||typeof a.UNSAFE_componentWillMount!="function"&&typeof a.componentWillMount!="function"||(typeof a.componentWillMount=="function"&&a.componentWillMount(),typeof a.UNSAFE_componentWillMount=="function"&&a.UNSAFE_componentWillMount()),typeof a.componentDidMount=="function"&&(t.flags|=4194308)):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=c),a.props=r,a.state=c,a.context=u,r=l):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{a=t.stateNode,wx(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:Lt(t.type,l),a.props=u,m=t.pendingProps,d=a.context,c=n.contextType,typeof c=="object"&&c!==null?c=It(c):(c=ut(n)?Or:qe.current,c=$s(t,c));var v=n.getDerivedStateFromProps;(p=typeof v=="function"||typeof a.getSnapshotBeforeUpdate=="function")||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(l!==m||d!==c)&&Am(t,a,r,c),$n=!1,d=t.memoizedState,a.state=d,Ma(t,r,a,s);var w=t.memoizedState;l!==m||d!==w||ct.current||$n?(typeof v=="function"&&(lu(t,n,v,r),w=t.memoizedState),(u=$n||Mm(t,n,u,r,d,w,c)||!1)?(p||typeof a.UNSAFE_componentWillUpdate!="function"&&typeof a.componentWillUpdate!="function"||(typeof a.componentWillUpdate=="function"&&a.componentWillUpdate(r,w,c),typeof a.UNSAFE_componentWillUpdate=="function"&&a.UNSAFE_componentWillUpdate(r,w,c)),typeof a.componentDidUpdate=="function"&&(t.flags|=4),typeof a.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof a.componentDidUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=w),a.props=r,a.state=w,a.context=c,r=u):(typeof a.componentDidUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),r=!1)}return fu(e,t,n,r,i,s)}function fu(e,t,n,r,s,i){Kx(e,t);var a=(t.flags&128)!==0;if(!r&&!a)return s&&bm(t,n,!1),Nn(e,t,i);r=t.stateNode,jj.current=t;var l=a&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&a?(t.child=Bs(t,e.child,null,i),t.child=Bs(t,null,l,i)):tt(e,t,l,i),t.memoizedState=r.state,s&&bm(t,n,!0),t.child}function Gx(e){var t=e.stateNode;t.pendingContext?Nm(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Nm(e,t.context,!1),Cd(e,t.containerInfo)}function zm(e,t,n,r,s){return Us(),yd(s),t.flags|=256,tt(e,t,n,r),t.child}var mu={dehydrated:null,treeContext:null,retryLane:0};function pu(e){return{baseLanes:e,cachePool:null,transitions:null}}function Qx(e,t,n){var r=t.pendingProps,s=Ne.current,i=!1,a=(t.flags&128)!==0,l;if((l=a)||(l=e!==null&&e.memoizedState===null?!1:(s&2)!==0),l?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(s|=1),xe(Ne,s&1),e===null)return iu(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(a=r.children,e=r.fallback,i?(r=t.mode,i=t.child,a={mode:"hidden",children:a},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=a):i=dl(a,r,0,null),e=_r(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=pu(n),t.memoizedState=mu,e):Id(t,a));if(s=e.memoizedState,s!==null&&(l=s.dehydrated,l!==null))return Nj(e,t,a,r,l,s,n);if(i){i=r.fallback,a=t.mode,s=e.child,l=s.sibling;var c={mode:"hidden",children:r.children};return!(a&1)&&t.child!==s?(r=t.child,r.childLanes=0,r.pendingProps=c,t.deletions=null):(r=sr(s,c),r.subtreeFlags=s.subtreeFlags&14680064),l!==null?i=sr(l,i):(i=_r(i,a,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,a=e.child.memoizedState,a=a===null?pu(n):{baseLanes:a.baseLanes|n,cachePool:null,transitions:a.transitions},i.memoizedState=a,i.childLanes=e.childLanes&~n,t.memoizedState=mu,r}return i=e.child,e=i.sibling,r=sr(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Id(e,t){return t=dl({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function $i(e,t,n,r){return r!==null&&yd(r),Bs(t,e.child,null,n),e=Id(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Nj(e,t,n,r,s,i,a){if(n)return t.flags&256?(t.flags&=-257,r=fc(Error(R(422))),$i(e,t,a,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,s=t.mode,r=dl({mode:"visible",children:r.children},s,0,null),i=_r(i,s,a,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&Bs(t,e.child,null,a),t.child.memoizedState=pu(a),t.memoizedState=mu,i);if(!(t.mode&1))return $i(e,t,a,null);if(s.data==="$!"){if(r=s.nextSibling&&s.nextSibling.dataset,r)var l=r.dgst;return r=l,i=Error(R(419)),r=fc(i,r,void 0),$i(e,t,a,r)}if(l=(a&e.childLanes)!==0,lt||l){if(r=ze,r!==null){switch(a&-a){case 4:s=2;break;case 16:s=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:s=32;break;case 536870912:s=268435456;break;default:s=0}s=s&(r.suspendedLanes|a)?0:s,s!==0&&s!==i.retryLane&&(i.retryLane=s,jn(e,s),Ht(r,e,s,-1))}return zd(),r=fc(Error(R(421))),$i(e,t,a,r)}return s.data==="$?"?(t.flags|=128,t.child=e.child,t=Oj.bind(null,e),s._reactRetry=t,null):(e=i.treeContext,vt=er(s.nextSibling),yt=t,je=!0,Ut=null,e!==null&&(kt[Pt++]=xn,kt[Pt++]=gn,kt[Pt++]=Dr,xn=e.id,gn=e.overflow,Dr=t),t=Id(t,r.children),t.flags|=4096,t)}function $m(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),au(e.return,t,n)}function mc(e,t,n,r,s){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:s}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=s)}function Yx(e,t,n){var r=t.pendingProps,s=r.revealOrder,i=r.tail;if(tt(e,t,r.children,n),r=Ne.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&$m(e,n,t);else if(e.tag===19)$m(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(xe(Ne,r),!(t.mode&1))t.memoizedState=null;else switch(s){case"forwards":for(n=t.child,s=null;n!==null;)e=n.alternate,e!==null&&Aa(e)===null&&(s=n),n=n.sibling;n=s,n===null?(s=t.child,t.child=null):(s=n.sibling,n.sibling=null),mc(t,!1,s,n,i);break;case"backwards":for(n=null,s=t.child,t.child=null;s!==null;){if(e=s.alternate,e!==null&&Aa(e)===null){t.child=s;break}e=s.sibling,s.sibling=n,n=s,s=e}mc(t,!0,n,null,i);break;case"together":mc(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function aa(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Nn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Fr|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(R(153));if(t.child!==null){for(e=t.child,n=sr(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=sr(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function bj(e,t,n){switch(t.tag){case 3:Gx(t),Us();break;case 5:jx(t);break;case 1:ut(t.type)&&Ea(t);break;case 4:Cd(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,s=t.memoizedProps.value;xe(Ta,r._currentValue),r._currentValue=s;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(xe(Ne,Ne.current&1),t.flags|=128,null):n&t.child.childLanes?Qx(e,t,n):(xe(Ne,Ne.current&1),e=Nn(e,t,n),e!==null?e.sibling:null);xe(Ne,Ne.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Yx(e,t,n);t.flags|=128}if(s=t.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),xe(Ne,Ne.current),r)break;return null;case 22:case 23:return t.lanes=0,Wx(e,t,n)}return Nn(e,t,n)}var qx,hu,Xx,Jx;qx=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};hu=function(){};Xx=function(e,t,n,r){var s=e.memoizedProps;if(s!==r){e=t.stateNode,Er(on.current);var i=null;switch(n){case"input":s=Lc(e,s),r=Lc(e,r),i=[];break;case"select":s=Ce({},s,{value:void 0}),r=Ce({},r,{value:void 0}),i=[];break;case"textarea":s=$c(e,s),r=$c(e,r),i=[];break;default:typeof s.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Sa)}Bc(n,r);var a;n=null;for(u in s)if(!r.hasOwnProperty(u)&&s.hasOwnProperty(u)&&s[u]!=null)if(u==="style"){var l=s[u];for(a in l)l.hasOwnProperty(a)&&(n||(n={}),n[a]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(zo.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var c=r[u];if(l=s!=null?s[u]:void 0,r.hasOwnProperty(u)&&c!==l&&(c!=null||l!=null))if(u==="style")if(l){for(a in l)!l.hasOwnProperty(a)||c&&c.hasOwnProperty(a)||(n||(n={}),n[a]="");for(a in c)c.hasOwnProperty(a)&&l[a]!==c[a]&&(n||(n={}),n[a]=c[a])}else n||(i||(i=[]),i.push(u,n)),n=c;else u==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,l=l?l.__html:void 0,c!=null&&l!==c&&(i=i||[]).push(u,c)):u==="children"?typeof c!="string"&&typeof c!="number"||(i=i||[]).push(u,""+c):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(zo.hasOwnProperty(u)?(c!=null&&u==="onScroll"&&ye("scroll",e),i||l===c||(i=[])):(i=i||[]).push(u,c))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};Jx=function(e,t,n,r){n!==r&&(t.flags|=4)};function ho(e,t){if(!je)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Ge(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags&14680064,r|=s.flags&14680064,s.return=e,s=s.sibling;else for(s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags,r|=s.flags,s.return=e,s=s.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Sj(e,t,n){var r=t.pendingProps;switch(vd(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ge(t),null;case 1:return ut(t.type)&&Ca(),Ge(t),null;case 3:return r=t.stateNode,Vs(),we(ct),we(qe),kd(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Fi(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Ut!==null&&(bu(Ut),Ut=null))),hu(e,t),Ge(t),null;case 5:Ed(t);var s=Er(Xo.current);if(n=t.type,e!==null&&t.stateNode!=null)Xx(e,t,n,r,s),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(R(166));return Ge(t),null}if(e=Er(on.current),Fi(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[nn]=t,r[Yo]=i,e=(t.mode&1)!==0,n){case"dialog":ye("cancel",r),ye("close",r);break;case"iframe":case"object":case"embed":ye("load",r);break;case"video":case"audio":for(s=0;s<Eo.length;s++)ye(Eo[s],r);break;case"source":ye("error",r);break;case"img":case"image":case"link":ye("error",r),ye("load",r);break;case"details":ye("toggle",r);break;case"input":Yf(r,i),ye("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},ye("invalid",r);break;case"textarea":Xf(r,i),ye("invalid",r)}Bc(n,i),s=null;for(var a in i)if(i.hasOwnProperty(a)){var l=i[a];a==="children"?typeof l=="string"?r.textContent!==l&&(i.suppressHydrationWarning!==!0&&Li(r.textContent,l,e),s=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(i.suppressHydrationWarning!==!0&&Li(r.textContent,l,e),s=["children",""+l]):zo.hasOwnProperty(a)&&l!=null&&a==="onScroll"&&ye("scroll",r)}switch(n){case"input":Ti(r),qf(r,i,!0);break;case"textarea":Ti(r),Jf(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=Sa)}r=s,t.updateQueue=r,r!==null&&(t.flags|=4)}else{a=s.nodeType===9?s:s.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Eh(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=a.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=a.createElement(n,{is:r.is}):(e=a.createElement(n),n==="select"&&(a=e,r.multiple?a.multiple=!0:r.size&&(a.size=r.size))):e=a.createElementNS(e,n),e[nn]=t,e[Yo]=r,qx(e,t,!1,!1),t.stateNode=e;e:{switch(a=Vc(n,r),n){case"dialog":ye("cancel",e),ye("close",e),s=r;break;case"iframe":case"object":case"embed":ye("load",e),s=r;break;case"video":case"audio":for(s=0;s<Eo.length;s++)ye(Eo[s],e);s=r;break;case"source":ye("error",e),s=r;break;case"img":case"image":case"link":ye("error",e),ye("load",e),s=r;break;case"details":ye("toggle",e),s=r;break;case"input":Yf(e,r),s=Lc(e,r),ye("invalid",e);break;case"option":s=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},s=Ce({},r,{value:void 0}),ye("invalid",e);break;case"textarea":Xf(e,r),s=$c(e,r),ye("invalid",e);break;default:s=r}Bc(n,s),l=s;for(i in l)if(l.hasOwnProperty(i)){var c=l[i];i==="style"?Th(e,c):i==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,c!=null&&kh(e,c)):i==="children"?typeof c=="string"?(n!=="textarea"||c!=="")&&$o(e,c):typeof c=="number"&&$o(e,""+c):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(zo.hasOwnProperty(i)?c!=null&&i==="onScroll"&&ye("scroll",e):c!=null&&rd(e,i,c,a))}switch(n){case"input":Ti(e),qf(e,r,!1);break;case"textarea":Ti(e),Jf(e);break;case"option":r.value!=null&&e.setAttribute("value",""+ar(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?ws(e,!!r.multiple,i,!1):r.defaultValue!=null&&ws(e,!!r.multiple,r.defaultValue,!0);break;default:typeof s.onClick=="function"&&(e.onclick=Sa)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Ge(t),null;case 6:if(e&&t.stateNode!=null)Jx(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(R(166));if(n=Er(Xo.current),Er(on.current),Fi(t)){if(r=t.stateNode,n=t.memoizedProps,r[nn]=t,(i=r.nodeValue!==n)&&(e=yt,e!==null))switch(e.tag){case 3:Li(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Li(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[nn]=t,t.stateNode=r}return Ge(t),null;case 13:if(we(Ne),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(je&&vt!==null&&t.mode&1&&!(t.flags&128))xx(),Us(),t.flags|=98560,i=!1;else if(i=Fi(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(R(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(R(317));i[nn]=t}else Us(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Ge(t),i=!1}else Ut!==null&&(bu(Ut),Ut=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||Ne.current&1?_e===0&&(_e=3):zd())),t.updateQueue!==null&&(t.flags|=4),Ge(t),null);case 4:return Vs(),hu(e,t),e===null&&Go(t.stateNode.containerInfo),Ge(t),null;case 10:return Nd(t.type._context),Ge(t),null;case 17:return ut(t.type)&&Ca(),Ge(t),null;case 19:if(we(Ne),i=t.memoizedState,i===null)return Ge(t),null;if(r=(t.flags&128)!==0,a=i.rendering,a===null)if(r)ho(i,!1);else{if(_e!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(a=Aa(e),a!==null){for(t.flags|=128,ho(i,!1),r=a.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,a=i.alternate,a===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=a.childLanes,i.lanes=a.lanes,i.child=a.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=a.memoizedProps,i.memoizedState=a.memoizedState,i.updateQueue=a.updateQueue,i.type=a.type,e=a.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return xe(Ne,Ne.current&1|2),t.child}e=e.sibling}i.tail!==null&&Te()>Ws&&(t.flags|=128,r=!0,ho(i,!1),t.lanes=4194304)}else{if(!r)if(e=Aa(a),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),ho(i,!0),i.tail===null&&i.tailMode==="hidden"&&!a.alternate&&!je)return Ge(t),null}else 2*Te()-i.renderingStartTime>Ws&&n!==1073741824&&(t.flags|=128,r=!0,ho(i,!1),t.lanes=4194304);i.isBackwards?(a.sibling=t.child,t.child=a):(n=i.last,n!==null?n.sibling=a:t.child=a,i.last=a)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Te(),t.sibling=null,n=Ne.current,xe(Ne,r?n&1|2:n&1),t):(Ge(t),null);case 22:case 23:return Fd(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?xt&1073741824&&(Ge(t),t.subtreeFlags&6&&(t.flags|=8192)):Ge(t),null;case 24:return null;case 25:return null}throw Error(R(156,t.tag))}function Cj(e,t){switch(vd(t),t.tag){case 1:return ut(t.type)&&Ca(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Vs(),we(ct),we(qe),kd(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Ed(t),null;case 13:if(we(Ne),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(R(340));Us()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return we(Ne),null;case 4:return Vs(),null;case 10:return Nd(t.type._context),null;case 22:case 23:return Fd(),null;case 24:return null;default:return null}}var Ui=!1,Ye=!1,Ej=typeof WeakSet=="function"?WeakSet:Set,$=null;function gs(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){ke(e,t,r)}else n.current=null}function xu(e,t,n){try{n()}catch(r){ke(e,t,r)}}var Um=!1;function kj(e,t){if(Zc=ja,e=rx(),xd(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var s=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var a=0,l=-1,c=-1,u=0,p=0,m=e,d=null;t:for(;;){for(var v;m!==n||s!==0&&m.nodeType!==3||(l=a+s),m!==i||r!==0&&m.nodeType!==3||(c=a+r),m.nodeType===3&&(a+=m.nodeValue.length),(v=m.firstChild)!==null;)d=m,m=v;for(;;){if(m===e)break t;if(d===n&&++u===s&&(l=a),d===i&&++p===r&&(c=a),(v=m.nextSibling)!==null)break;m=d,d=m.parentNode}m=v}n=l===-1||c===-1?null:{start:l,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(eu={focusedElem:e,selectionRange:n},ja=!1,$=t;$!==null;)if(t=$,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,$=e;else for(;$!==null;){t=$;try{var w=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(w!==null){var h=w.memoizedProps,j=w.memoizedState,g=t.stateNode,x=g.getSnapshotBeforeUpdate(t.elementType===t.type?h:Lt(t.type,h),j);g.__reactInternalSnapshotBeforeUpdate=x}break;case 3:var y=t.stateNode.containerInfo;y.nodeType===1?y.textContent="":y.nodeType===9&&y.documentElement&&y.removeChild(y.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(R(163))}}catch(N){ke(t,t.return,N)}if(e=t.sibling,e!==null){e.return=t.return,$=e;break}$=t.return}return w=Um,Um=!1,w}function Oo(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var s=r=r.next;do{if((s.tag&e)===e){var i=s.destroy;s.destroy=void 0,i!==void 0&&xu(t,n,i)}s=s.next}while(s!==r)}}function cl(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function gu(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Zx(e){var t=e.alternate;t!==null&&(e.alternate=null,Zx(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[nn],delete t[Yo],delete t[ru],delete t[cj],delete t[uj])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function eg(e){return e.tag===5||e.tag===3||e.tag===4}function Bm(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||eg(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function vu(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Sa));else if(r!==4&&(e=e.child,e!==null))for(vu(e,t,n),e=e.sibling;e!==null;)vu(e,t,n),e=e.sibling}function yu(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(yu(e,t,n),e=e.sibling;e!==null;)yu(e,t,n),e=e.sibling}var Ue=null,$t=!1;function _n(e,t,n){for(n=n.child;n!==null;)tg(e,t,n),n=n.sibling}function tg(e,t,n){if(sn&&typeof sn.onCommitFiberUnmount=="function")try{sn.onCommitFiberUnmount(tl,n)}catch{}switch(n.tag){case 5:Ye||gs(n,t);case 6:var r=Ue,s=$t;Ue=null,_n(e,t,n),Ue=r,$t=s,Ue!==null&&($t?(e=Ue,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Ue.removeChild(n.stateNode));break;case 18:Ue!==null&&($t?(e=Ue,n=n.stateNode,e.nodeType===8?ic(e.parentNode,n):e.nodeType===1&&ic(e,n),Ho(e)):ic(Ue,n.stateNode));break;case 4:r=Ue,s=$t,Ue=n.stateNode.containerInfo,$t=!0,_n(e,t,n),Ue=r,$t=s;break;case 0:case 11:case 14:case 15:if(!Ye&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){s=r=r.next;do{var i=s,a=i.destroy;i=i.tag,a!==void 0&&(i&2||i&4)&&xu(n,t,a),s=s.next}while(s!==r)}_n(e,t,n);break;case 1:if(!Ye&&(gs(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){ke(n,t,l)}_n(e,t,n);break;case 21:_n(e,t,n);break;case 22:n.mode&1?(Ye=(r=Ye)||n.memoizedState!==null,_n(e,t,n),Ye=r):_n(e,t,n);break;default:_n(e,t,n)}}function Vm(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Ej),t.forEach(function(r){var s=Dj.bind(null,e,r);n.has(r)||(n.add(r),r.then(s,s))})}}function Ot(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var s=n[r];try{var i=e,a=t,l=a;e:for(;l!==null;){switch(l.tag){case 5:Ue=l.stateNode,$t=!1;break e;case 3:Ue=l.stateNode.containerInfo,$t=!0;break e;case 4:Ue=l.stateNode.containerInfo,$t=!0;break e}l=l.return}if(Ue===null)throw Error(R(160));tg(i,a,s),Ue=null,$t=!1;var c=s.alternate;c!==null&&(c.return=null),s.return=null}catch(u){ke(s,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)ng(t,e),t=t.sibling}function ng(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Ot(t,e),qt(e),r&4){try{Oo(3,e,e.return),cl(3,e)}catch(h){ke(e,e.return,h)}try{Oo(5,e,e.return)}catch(h){ke(e,e.return,h)}}break;case 1:Ot(t,e),qt(e),r&512&&n!==null&&gs(n,n.return);break;case 5:if(Ot(t,e),qt(e),r&512&&n!==null&&gs(n,n.return),e.flags&32){var s=e.stateNode;try{$o(s,"")}catch(h){ke(e,e.return,h)}}if(r&4&&(s=e.stateNode,s!=null)){var i=e.memoizedProps,a=n!==null?n.memoizedProps:i,l=e.type,c=e.updateQueue;if(e.updateQueue=null,c!==null)try{l==="input"&&i.type==="radio"&&i.name!=null&&Sh(s,i),Vc(l,a);var u=Vc(l,i);for(a=0;a<c.length;a+=2){var p=c[a],m=c[a+1];p==="style"?Th(s,m):p==="dangerouslySetInnerHTML"?kh(s,m):p==="children"?$o(s,m):rd(s,p,m,u)}switch(l){case"input":Fc(s,i);break;case"textarea":Ch(s,i);break;case"select":var d=s._wrapperState.wasMultiple;s._wrapperState.wasMultiple=!!i.multiple;var v=i.value;v!=null?ws(s,!!i.multiple,v,!1):d!==!!i.multiple&&(i.defaultValue!=null?ws(s,!!i.multiple,i.defaultValue,!0):ws(s,!!i.multiple,i.multiple?[]:"",!1))}s[Yo]=i}catch(h){ke(e,e.return,h)}}break;case 6:if(Ot(t,e),qt(e),r&4){if(e.stateNode===null)throw Error(R(162));s=e.stateNode,i=e.memoizedProps;try{s.nodeValue=i}catch(h){ke(e,e.return,h)}}break;case 3:if(Ot(t,e),qt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Ho(t.containerInfo)}catch(h){ke(e,e.return,h)}break;case 4:Ot(t,e),qt(e);break;case 13:Ot(t,e),qt(e),s=e.child,s.flags&8192&&(i=s.memoizedState!==null,s.stateNode.isHidden=i,!i||s.alternate!==null&&s.alternate.memoizedState!==null||(Dd=Te())),r&4&&Vm(e);break;case 22:if(p=n!==null&&n.memoizedState!==null,e.mode&1?(Ye=(u=Ye)||p,Ot(t,e),Ye=u):Ot(t,e),qt(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!p&&e.mode&1)for($=e,p=e.child;p!==null;){for(m=$=p;$!==null;){switch(d=$,v=d.child,d.tag){case 0:case 11:case 14:case 15:Oo(4,d,d.return);break;case 1:gs(d,d.return);var w=d.stateNode;if(typeof w.componentWillUnmount=="function"){r=d,n=d.return;try{t=r,w.props=t.memoizedProps,w.state=t.memoizedState,w.componentWillUnmount()}catch(h){ke(r,n,h)}}break;case 5:gs(d,d.return);break;case 22:if(d.memoizedState!==null){Wm(m);continue}}v!==null?(v.return=d,$=v):Wm(m)}p=p.sibling}e:for(p=null,m=e;;){if(m.tag===5){if(p===null){p=m;try{s=m.stateNode,u?(i=s.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(l=m.stateNode,c=m.memoizedProps.style,a=c!=null&&c.hasOwnProperty("display")?c.display:null,l.style.display=Ph("display",a))}catch(h){ke(e,e.return,h)}}}else if(m.tag===6){if(p===null)try{m.stateNode.nodeValue=u?"":m.memoizedProps}catch(h){ke(e,e.return,h)}}else if((m.tag!==22&&m.tag!==23||m.memoizedState===null||m===e)&&m.child!==null){m.child.return=m,m=m.child;continue}if(m===e)break e;for(;m.sibling===null;){if(m.return===null||m.return===e)break e;p===m&&(p=null),m=m.return}p===m&&(p=null),m.sibling.return=m.return,m=m.sibling}}break;case 19:Ot(t,e),qt(e),r&4&&Vm(e);break;case 21:break;default:Ot(t,e),qt(e)}}function qt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(eg(n)){var r=n;break e}n=n.return}throw Error(R(160))}switch(r.tag){case 5:var s=r.stateNode;r.flags&32&&($o(s,""),r.flags&=-33);var i=Bm(e);yu(e,i,s);break;case 3:case 4:var a=r.stateNode.containerInfo,l=Bm(e);vu(e,l,a);break;default:throw Error(R(161))}}catch(c){ke(e,e.return,c)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Pj(e,t,n){$=e,rg(e)}function rg(e,t,n){for(var r=(e.mode&1)!==0;$!==null;){var s=$,i=s.child;if(s.tag===22&&r){var a=s.memoizedState!==null||Ui;if(!a){var l=s.alternate,c=l!==null&&l.memoizedState!==null||Ye;l=Ui;var u=Ye;if(Ui=a,(Ye=c)&&!u)for($=s;$!==null;)a=$,c=a.child,a.tag===22&&a.memoizedState!==null?Km(s):c!==null?(c.return=a,$=c):Km(s);for(;i!==null;)$=i,rg(i),i=i.sibling;$=s,Ui=l,Ye=u}Hm(e)}else s.subtreeFlags&8772&&i!==null?(i.return=s,$=i):Hm(e)}}function Hm(e){for(;$!==null;){var t=$;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Ye||cl(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Ye)if(n===null)r.componentDidMount();else{var s=t.elementType===t.type?n.memoizedProps:Lt(t.type,n.memoizedProps);r.componentDidUpdate(s,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&Pm(t,i,r);break;case 3:var a=t.updateQueue;if(a!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Pm(t,a,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var c=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":c.autoFocus&&n.focus();break;case"img":c.src&&(n.src=c.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var p=u.memoizedState;if(p!==null){var m=p.dehydrated;m!==null&&Ho(m)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(R(163))}Ye||t.flags&512&&gu(t)}catch(d){ke(t,t.return,d)}}if(t===e){$=null;break}if(n=t.sibling,n!==null){n.return=t.return,$=n;break}$=t.return}}function Wm(e){for(;$!==null;){var t=$;if(t===e){$=null;break}var n=t.sibling;if(n!==null){n.return=t.return,$=n;break}$=t.return}}function Km(e){for(;$!==null;){var t=$;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{cl(4,t)}catch(c){ke(t,n,c)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var s=t.return;try{r.componentDidMount()}catch(c){ke(t,s,c)}}var i=t.return;try{gu(t)}catch(c){ke(t,i,c)}break;case 5:var a=t.return;try{gu(t)}catch(c){ke(t,a,c)}}}catch(c){ke(t,t.return,c)}if(t===e){$=null;break}var l=t.sibling;if(l!==null){l.return=t.return,$=l;break}$=t.return}}var Tj=Math.ceil,Oa=Tn.ReactCurrentDispatcher,_d=Tn.ReactCurrentOwner,Mt=Tn.ReactCurrentBatchConfig,oe=0,ze=null,Ae=null,Ve=0,xt=0,vs=pr(0),_e=0,ti=null,Fr=0,ul=0,Od=0,Do=null,at=null,Dd=0,Ws=1/0,pn=null,Da=!1,wu=null,nr=null,Bi=!1,Yn=null,La=0,Lo=0,ju=null,la=-1,ca=0;function rt(){return oe&6?Te():la!==-1?la:la=Te()}function rr(e){return e.mode&1?oe&2&&Ve!==0?Ve&-Ve:fj.transition!==null?(ca===0&&(ca=Uh()),ca):(e=fe,e!==0||(e=window.event,e=e===void 0?16:Qh(e.type)),e):1}function Ht(e,t,n,r){if(50<Lo)throw Lo=0,ju=null,Error(R(185));pi(e,n,r),(!(oe&2)||e!==ze)&&(e===ze&&(!(oe&2)&&(ul|=n),_e===4&&Bn(e,Ve)),dt(e,r),n===1&&oe===0&&!(t.mode&1)&&(Ws=Te()+500,il&&hr()))}function dt(e,t){var n=e.callbackNode;f1(e,t);var r=wa(e,e===ze?Ve:0);if(r===0)n!==null&&tm(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&tm(n),t===1)e.tag===0?dj(Gm.bind(null,e)):mx(Gm.bind(null,e)),aj(function(){!(oe&6)&&hr()}),n=null;else{switch(Bh(r)){case 1:n=ld;break;case 4:n=zh;break;case 16:n=ya;break;case 536870912:n=$h;break;default:n=ya}n=dg(n,sg.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function sg(e,t){if(la=-1,ca=0,oe&6)throw Error(R(327));var n=e.callbackNode;if(Cs()&&e.callbackNode!==n)return null;var r=wa(e,e===ze?Ve:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Fa(e,r);else{t=r;var s=oe;oe|=2;var i=ig();(ze!==e||Ve!==t)&&(pn=null,Ws=Te()+500,Ir(e,t));do try{Aj();break}catch(l){og(e,l)}while(!0);jd(),Oa.current=i,oe=s,Ae!==null?t=0:(ze=null,Ve=0,t=_e)}if(t!==0){if(t===2&&(s=Qc(e),s!==0&&(r=s,t=Nu(e,s))),t===1)throw n=ti,Ir(e,0),Bn(e,r),dt(e,Te()),n;if(t===6)Bn(e,r);else{if(s=e.current.alternate,!(r&30)&&!Rj(s)&&(t=Fa(e,r),t===2&&(i=Qc(e),i!==0&&(r=i,t=Nu(e,i))),t===1))throw n=ti,Ir(e,0),Bn(e,r),dt(e,Te()),n;switch(e.finishedWork=s,e.finishedLanes=r,t){case 0:case 1:throw Error(R(345));case 2:br(e,at,pn);break;case 3:if(Bn(e,r),(r&130023424)===r&&(t=Dd+500-Te(),10<t)){if(wa(e,0)!==0)break;if(s=e.suspendedLanes,(s&r)!==r){rt(),e.pingedLanes|=e.suspendedLanes&s;break}e.timeoutHandle=nu(br.bind(null,e,at,pn),t);break}br(e,at,pn);break;case 4:if(Bn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,s=-1;0<r;){var a=31-Vt(r);i=1<<a,a=t[a],a>s&&(s=a),r&=~i}if(r=s,r=Te()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Tj(r/1960))-r,10<r){e.timeoutHandle=nu(br.bind(null,e,at,pn),r);break}br(e,at,pn);break;case 5:br(e,at,pn);break;default:throw Error(R(329))}}}return dt(e,Te()),e.callbackNode===n?sg.bind(null,e):null}function Nu(e,t){var n=Do;return e.current.memoizedState.isDehydrated&&(Ir(e,t).flags|=256),e=Fa(e,t),e!==2&&(t=at,at=n,t!==null&&bu(t)),e}function bu(e){at===null?at=e:at.push.apply(at,e)}function Rj(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var s=n[r],i=s.getSnapshot;s=s.value;try{if(!Wt(i(),s))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Bn(e,t){for(t&=~Od,t&=~ul,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Vt(t),r=1<<n;e[n]=-1,t&=~r}}function Gm(e){if(oe&6)throw Error(R(327));Cs();var t=wa(e,0);if(!(t&1))return dt(e,Te()),null;var n=Fa(e,t);if(e.tag!==0&&n===2){var r=Qc(e);r!==0&&(t=r,n=Nu(e,r))}if(n===1)throw n=ti,Ir(e,0),Bn(e,t),dt(e,Te()),n;if(n===6)throw Error(R(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,br(e,at,pn),dt(e,Te()),null}function Ld(e,t){var n=oe;oe|=1;try{return e(t)}finally{oe=n,oe===0&&(Ws=Te()+500,il&&hr())}}function zr(e){Yn!==null&&Yn.tag===0&&!(oe&6)&&Cs();var t=oe;oe|=1;var n=Mt.transition,r=fe;try{if(Mt.transition=null,fe=1,e)return e()}finally{fe=r,Mt.transition=n,oe=t,!(oe&6)&&hr()}}function Fd(){xt=vs.current,we(vs)}function Ir(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,ij(n)),Ae!==null)for(n=Ae.return;n!==null;){var r=n;switch(vd(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Ca();break;case 3:Vs(),we(ct),we(qe),kd();break;case 5:Ed(r);break;case 4:Vs();break;case 13:we(Ne);break;case 19:we(Ne);break;case 10:Nd(r.type._context);break;case 22:case 23:Fd()}n=n.return}if(ze=e,Ae=e=sr(e.current,null),Ve=xt=t,_e=0,ti=null,Od=ul=Fr=0,at=Do=null,Cr!==null){for(t=0;t<Cr.length;t++)if(n=Cr[t],r=n.interleaved,r!==null){n.interleaved=null;var s=r.next,i=n.pending;if(i!==null){var a=i.next;i.next=s,r.next=a}n.pending=r}Cr=null}return e}function og(e,t){do{var n=Ae;try{if(jd(),oa.current=_a,Ia){for(var r=Se.memoizedState;r!==null;){var s=r.queue;s!==null&&(s.pending=null),r=r.next}Ia=!1}if(Lr=0,De=Ie=Se=null,_o=!1,Jo=0,_d.current=null,n===null||n.return===null){_e=1,ti=t,Ae=null;break}e:{var i=e,a=n.return,l=n,c=t;if(t=Ve,l.flags|=32768,c!==null&&typeof c=="object"&&typeof c.then=="function"){var u=c,p=l,m=p.tag;if(!(p.mode&1)&&(m===0||m===11||m===15)){var d=p.alternate;d?(p.updateQueue=d.updateQueue,p.memoizedState=d.memoizedState,p.lanes=d.lanes):(p.updateQueue=null,p.memoizedState=null)}var v=_m(a);if(v!==null){v.flags&=-257,Om(v,a,l,i,t),v.mode&1&&Im(i,u,t),t=v,c=u;var w=t.updateQueue;if(w===null){var h=new Set;h.add(c),t.updateQueue=h}else w.add(c);break e}else{if(!(t&1)){Im(i,u,t),zd();break e}c=Error(R(426))}}else if(je&&l.mode&1){var j=_m(a);if(j!==null){!(j.flags&65536)&&(j.flags|=256),Om(j,a,l,i,t),yd(Hs(c,l));break e}}i=c=Hs(c,l),_e!==4&&(_e=2),Do===null?Do=[i]:Do.push(i),i=a;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var g=Bx(i,c,t);km(i,g);break e;case 1:l=c;var x=i.type,y=i.stateNode;if(!(i.flags&128)&&(typeof x.getDerivedStateFromError=="function"||y!==null&&typeof y.componentDidCatch=="function"&&(nr===null||!nr.has(y)))){i.flags|=65536,t&=-t,i.lanes|=t;var N=Vx(i,l,t);km(i,N);break e}}i=i.return}while(i!==null)}lg(n)}catch(b){t=b,Ae===n&&n!==null&&(Ae=n=n.return);continue}break}while(!0)}function ig(){var e=Oa.current;return Oa.current=_a,e===null?_a:e}function zd(){(_e===0||_e===3||_e===2)&&(_e=4),ze===null||!(Fr&268435455)&&!(ul&268435455)||Bn(ze,Ve)}function Fa(e,t){var n=oe;oe|=2;var r=ig();(ze!==e||Ve!==t)&&(pn=null,Ir(e,t));do try{Mj();break}catch(s){og(e,s)}while(!0);if(jd(),oe=n,Oa.current=r,Ae!==null)throw Error(R(261));return ze=null,Ve=0,_e}function Mj(){for(;Ae!==null;)ag(Ae)}function Aj(){for(;Ae!==null&&!r1();)ag(Ae)}function ag(e){var t=ug(e.alternate,e,xt);e.memoizedProps=e.pendingProps,t===null?lg(e):Ae=t,_d.current=null}function lg(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Cj(n,t),n!==null){n.flags&=32767,Ae=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{_e=6,Ae=null;return}}else if(n=Sj(n,t,xt),n!==null){Ae=n;return}if(t=t.sibling,t!==null){Ae=t;return}Ae=t=e}while(t!==null);_e===0&&(_e=5)}function br(e,t,n){var r=fe,s=Mt.transition;try{Mt.transition=null,fe=1,Ij(e,t,n,r)}finally{Mt.transition=s,fe=r}return null}function Ij(e,t,n,r){do Cs();while(Yn!==null);if(oe&6)throw Error(R(327));n=e.finishedWork;var s=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(R(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(m1(e,i),e===ze&&(Ae=ze=null,Ve=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Bi||(Bi=!0,dg(ya,function(){return Cs(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=Mt.transition,Mt.transition=null;var a=fe;fe=1;var l=oe;oe|=4,_d.current=null,kj(e,n),ng(n,e),Z1(eu),ja=!!Zc,eu=Zc=null,e.current=n,Pj(n),s1(),oe=l,fe=a,Mt.transition=i}else e.current=n;if(Bi&&(Bi=!1,Yn=e,La=s),i=e.pendingLanes,i===0&&(nr=null),a1(n.stateNode),dt(e,Te()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)s=t[n],r(s.value,{componentStack:s.stack,digest:s.digest});if(Da)throw Da=!1,e=wu,wu=null,e;return La&1&&e.tag!==0&&Cs(),i=e.pendingLanes,i&1?e===ju?Lo++:(Lo=0,ju=e):Lo=0,hr(),null}function Cs(){if(Yn!==null){var e=Bh(La),t=Mt.transition,n=fe;try{if(Mt.transition=null,fe=16>e?16:e,Yn===null)var r=!1;else{if(e=Yn,Yn=null,La=0,oe&6)throw Error(R(331));var s=oe;for(oe|=4,$=e.current;$!==null;){var i=$,a=i.child;if($.flags&16){var l=i.deletions;if(l!==null){for(var c=0;c<l.length;c++){var u=l[c];for($=u;$!==null;){var p=$;switch(p.tag){case 0:case 11:case 15:Oo(8,p,i)}var m=p.child;if(m!==null)m.return=p,$=m;else for(;$!==null;){p=$;var d=p.sibling,v=p.return;if(Zx(p),p===u){$=null;break}if(d!==null){d.return=v,$=d;break}$=v}}}var w=i.alternate;if(w!==null){var h=w.child;if(h!==null){w.child=null;do{var j=h.sibling;h.sibling=null,h=j}while(h!==null)}}$=i}}if(i.subtreeFlags&2064&&a!==null)a.return=i,$=a;else e:for(;$!==null;){if(i=$,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Oo(9,i,i.return)}var g=i.sibling;if(g!==null){g.return=i.return,$=g;break e}$=i.return}}var x=e.current;for($=x;$!==null;){a=$;var y=a.child;if(a.subtreeFlags&2064&&y!==null)y.return=a,$=y;else e:for(a=x;$!==null;){if(l=$,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:cl(9,l)}}catch(b){ke(l,l.return,b)}if(l===a){$=null;break e}var N=l.sibling;if(N!==null){N.return=l.return,$=N;break e}$=l.return}}if(oe=s,hr(),sn&&typeof sn.onPostCommitFiberRoot=="function")try{sn.onPostCommitFiberRoot(tl,e)}catch{}r=!0}return r}finally{fe=n,Mt.transition=t}}return!1}function Qm(e,t,n){t=Hs(n,t),t=Bx(e,t,1),e=tr(e,t,1),t=rt(),e!==null&&(pi(e,1,t),dt(e,t))}function ke(e,t,n){if(e.tag===3)Qm(e,e,n);else for(;t!==null;){if(t.tag===3){Qm(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(nr===null||!nr.has(r))){e=Hs(n,e),e=Vx(t,e,1),t=tr(t,e,1),e=rt(),t!==null&&(pi(t,1,e),dt(t,e));break}}t=t.return}}function _j(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=rt(),e.pingedLanes|=e.suspendedLanes&n,ze===e&&(Ve&n)===n&&(_e===4||_e===3&&(Ve&130023424)===Ve&&500>Te()-Dd?Ir(e,0):Od|=n),dt(e,t)}function cg(e,t){t===0&&(e.mode&1?(t=Ai,Ai<<=1,!(Ai&130023424)&&(Ai=4194304)):t=1);var n=rt();e=jn(e,t),e!==null&&(pi(e,t,n),dt(e,n))}function Oj(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),cg(e,n)}function Dj(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,s=e.memoizedState;s!==null&&(n=s.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(R(314))}r!==null&&r.delete(t),cg(e,n)}var ug;ug=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||ct.current)lt=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return lt=!1,bj(e,t,n);lt=!!(e.flags&131072)}else lt=!1,je&&t.flags&1048576&&px(t,Pa,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;aa(e,t),e=t.pendingProps;var s=$s(t,qe.current);Ss(t,n),s=Td(null,t,r,e,s,n);var i=Rd();return t.flags|=1,typeof s=="object"&&s!==null&&typeof s.render=="function"&&s.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,ut(r)?(i=!0,Ea(t)):i=!1,t.memoizedState=s.state!==null&&s.state!==void 0?s.state:null,Sd(t),s.updater=ll,t.stateNode=s,s._reactInternals=t,cu(t,r,e,n),t=fu(null,t,r,!0,i,n)):(t.tag=0,je&&i&&gd(t),tt(null,t,s,n),t=t.child),t;case 16:r=t.elementType;e:{switch(aa(e,t),e=t.pendingProps,s=r._init,r=s(r._payload),t.type=r,s=t.tag=Fj(r),e=Lt(r,e),s){case 0:t=du(null,t,r,e,n);break e;case 1:t=Fm(null,t,r,e,n);break e;case 11:t=Dm(null,t,r,e,n);break e;case 14:t=Lm(null,t,r,Lt(r.type,e),n);break e}throw Error(R(306,r,""))}return t;case 0:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Lt(r,s),du(e,t,r,s,n);case 1:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Lt(r,s),Fm(e,t,r,s,n);case 3:e:{if(Gx(t),e===null)throw Error(R(387));r=t.pendingProps,i=t.memoizedState,s=i.element,wx(e,t),Ma(t,r,null,n);var a=t.memoizedState;if(r=a.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:a.cache,pendingSuspenseBoundaries:a.pendingSuspenseBoundaries,transitions:a.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){s=Hs(Error(R(423)),t),t=zm(e,t,r,n,s);break e}else if(r!==s){s=Hs(Error(R(424)),t),t=zm(e,t,r,n,s);break e}else for(vt=er(t.stateNode.containerInfo.firstChild),yt=t,je=!0,Ut=null,n=vx(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Us(),r===s){t=Nn(e,t,n);break e}tt(e,t,r,n)}t=t.child}return t;case 5:return jx(t),e===null&&iu(t),r=t.type,s=t.pendingProps,i=e!==null?e.memoizedProps:null,a=s.children,tu(r,s)?a=null:i!==null&&tu(r,i)&&(t.flags|=32),Kx(e,t),tt(e,t,a,n),t.child;case 6:return e===null&&iu(t),null;case 13:return Qx(e,t,n);case 4:return Cd(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Bs(t,null,r,n):tt(e,t,r,n),t.child;case 11:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Lt(r,s),Dm(e,t,r,s,n);case 7:return tt(e,t,t.pendingProps,n),t.child;case 8:return tt(e,t,t.pendingProps.children,n),t.child;case 12:return tt(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,s=t.pendingProps,i=t.memoizedProps,a=s.value,xe(Ta,r._currentValue),r._currentValue=a,i!==null)if(Wt(i.value,a)){if(i.children===s.children&&!ct.current){t=Nn(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var l=i.dependencies;if(l!==null){a=i.child;for(var c=l.firstContext;c!==null;){if(c.context===r){if(i.tag===1){c=vn(-1,n&-n),c.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var p=u.pending;p===null?c.next=c:(c.next=p.next,p.next=c),u.pending=c}}i.lanes|=n,c=i.alternate,c!==null&&(c.lanes|=n),au(i.return,n,t),l.lanes|=n;break}c=c.next}}else if(i.tag===10)a=i.type===t.type?null:i.child;else if(i.tag===18){if(a=i.return,a===null)throw Error(R(341));a.lanes|=n,l=a.alternate,l!==null&&(l.lanes|=n),au(a,n,t),a=i.sibling}else a=i.child;if(a!==null)a.return=i;else for(a=i;a!==null;){if(a===t){a=null;break}if(i=a.sibling,i!==null){i.return=a.return,a=i;break}a=a.return}i=a}tt(e,t,s.children,n),t=t.child}return t;case 9:return s=t.type,r=t.pendingProps.children,Ss(t,n),s=It(s),r=r(s),t.flags|=1,tt(e,t,r,n),t.child;case 14:return r=t.type,s=Lt(r,t.pendingProps),s=Lt(r.type,s),Lm(e,t,r,s,n);case 15:return Hx(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Lt(r,s),aa(e,t),t.tag=1,ut(r)?(e=!0,Ea(t)):e=!1,Ss(t,n),Ux(t,r,s),cu(t,r,s,n),fu(null,t,r,!0,e,n);case 19:return Yx(e,t,n);case 22:return Wx(e,t,n)}throw Error(R(156,t.tag))};function dg(e,t){return Fh(e,t)}function Lj(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Rt(e,t,n,r){return new Lj(e,t,n,r)}function $d(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Fj(e){if(typeof e=="function")return $d(e)?1:0;if(e!=null){if(e=e.$$typeof,e===od)return 11;if(e===id)return 14}return 2}function sr(e,t){var n=e.alternate;return n===null?(n=Rt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function ua(e,t,n,r,s,i){var a=2;if(r=e,typeof e=="function")$d(e)&&(a=1);else if(typeof e=="string")a=5;else e:switch(e){case ls:return _r(n.children,s,i,t);case sd:a=8,s|=8;break;case Ic:return e=Rt(12,n,t,s|2),e.elementType=Ic,e.lanes=i,e;case _c:return e=Rt(13,n,t,s),e.elementType=_c,e.lanes=i,e;case Oc:return e=Rt(19,n,t,s),e.elementType=Oc,e.lanes=i,e;case jh:return dl(n,s,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case yh:a=10;break e;case wh:a=9;break e;case od:a=11;break e;case id:a=14;break e;case zn:a=16,r=null;break e}throw Error(R(130,e==null?e:typeof e,""))}return t=Rt(a,n,t,s),t.elementType=e,t.type=r,t.lanes=i,t}function _r(e,t,n,r){return e=Rt(7,e,r,t),e.lanes=n,e}function dl(e,t,n,r){return e=Rt(22,e,r,t),e.elementType=jh,e.lanes=n,e.stateNode={isHidden:!1},e}function pc(e,t,n){return e=Rt(6,e,null,t),e.lanes=n,e}function hc(e,t,n){return t=Rt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function zj(e,t,n,r,s){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Yl(0),this.expirationTimes=Yl(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Yl(0),this.identifierPrefix=r,this.onRecoverableError=s,this.mutableSourceEagerHydrationData=null}function Ud(e,t,n,r,s,i,a,l,c){return e=new zj(e,t,n,l,c),t===1?(t=1,i===!0&&(t|=8)):t=0,i=Rt(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Sd(i),e}function $j(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:as,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function fg(e){if(!e)return lr;e=e._reactInternals;e:{if(Kr(e)!==e||e.tag!==1)throw Error(R(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(ut(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(R(171))}if(e.tag===1){var n=e.type;if(ut(n))return fx(e,n,t)}return t}function mg(e,t,n,r,s,i,a,l,c){return e=Ud(n,r,!0,e,s,i,a,l,c),e.context=fg(null),n=e.current,r=rt(),s=rr(n),i=vn(r,s),i.callback=t??null,tr(n,i,s),e.current.lanes=s,pi(e,s,r),dt(e,r),e}function fl(e,t,n,r){var s=t.current,i=rt(),a=rr(s);return n=fg(n),t.context===null?t.context=n:t.pendingContext=n,t=vn(i,a),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=tr(s,t,a),e!==null&&(Ht(e,s,a,i),sa(e,s,a)),a}function za(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Ym(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Bd(e,t){Ym(e,t),(e=e.alternate)&&Ym(e,t)}function Uj(){return null}var pg=typeof reportError=="function"?reportError:function(e){console.error(e)};function Vd(e){this._internalRoot=e}ml.prototype.render=Vd.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(R(409));fl(e,t,null,null)};ml.prototype.unmount=Vd.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;zr(function(){fl(null,e,null,null)}),t[wn]=null}};function ml(e){this._internalRoot=e}ml.prototype.unstable_scheduleHydration=function(e){if(e){var t=Wh();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Un.length&&t!==0&&t<Un[n].priority;n++);Un.splice(n,0,e),n===0&&Gh(e)}};function Hd(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function pl(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function qm(){}function Bj(e,t,n,r,s){if(s){if(typeof r=="function"){var i=r;r=function(){var u=za(a);i.call(u)}}var a=mg(t,r,e,0,null,!1,!1,"",qm);return e._reactRootContainer=a,e[wn]=a.current,Go(e.nodeType===8?e.parentNode:e),zr(),a}for(;s=e.lastChild;)e.removeChild(s);if(typeof r=="function"){var l=r;r=function(){var u=za(c);l.call(u)}}var c=Ud(e,0,!1,null,null,!1,!1,"",qm);return e._reactRootContainer=c,e[wn]=c.current,Go(e.nodeType===8?e.parentNode:e),zr(function(){fl(t,c,n,r)}),c}function hl(e,t,n,r,s){var i=n._reactRootContainer;if(i){var a=i;if(typeof s=="function"){var l=s;s=function(){var c=za(a);l.call(c)}}fl(t,a,e,s)}else a=Bj(n,t,e,s,r);return za(a)}Vh=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Co(t.pendingLanes);n!==0&&(cd(t,n|1),dt(t,Te()),!(oe&6)&&(Ws=Te()+500,hr()))}break;case 13:zr(function(){var r=jn(e,1);if(r!==null){var s=rt();Ht(r,e,1,s)}}),Bd(e,1)}};ud=function(e){if(e.tag===13){var t=jn(e,134217728);if(t!==null){var n=rt();Ht(t,e,134217728,n)}Bd(e,134217728)}};Hh=function(e){if(e.tag===13){var t=rr(e),n=jn(e,t);if(n!==null){var r=rt();Ht(n,e,t,r)}Bd(e,t)}};Wh=function(){return fe};Kh=function(e,t){var n=fe;try{return fe=e,t()}finally{fe=n}};Wc=function(e,t,n){switch(t){case"input":if(Fc(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var s=ol(r);if(!s)throw Error(R(90));bh(r),Fc(r,s)}}}break;case"textarea":Ch(e,n);break;case"select":t=n.value,t!=null&&ws(e,!!n.multiple,t,!1)}};Ah=Ld;Ih=zr;var Vj={usingClientEntryPoint:!1,Events:[xi,fs,ol,Rh,Mh,Ld]},xo={findFiberByHostInstance:Sr,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Hj={bundleType:xo.bundleType,version:xo.version,rendererPackageName:xo.rendererPackageName,rendererConfig:xo.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Tn.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Dh(e),e===null?null:e.stateNode},findFiberByHostInstance:xo.findFiberByHostInstance||Uj,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Vi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Vi.isDisabled&&Vi.supportsFiber)try{tl=Vi.inject(Hj),sn=Vi}catch{}}Nt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Vj;Nt.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Hd(t))throw Error(R(200));return $j(e,t,null,n)};Nt.createRoot=function(e,t){if(!Hd(e))throw Error(R(299));var n=!1,r="",s=pg;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(s=t.onRecoverableError)),t=Ud(e,1,!1,null,null,n,!1,r,s),e[wn]=t.current,Go(e.nodeType===8?e.parentNode:e),new Vd(t)};Nt.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(R(188)):(e=Object.keys(e).join(","),Error(R(268,e)));return e=Dh(t),e=e===null?null:e.stateNode,e};Nt.flushSync=function(e){return zr(e)};Nt.hydrate=function(e,t,n){if(!pl(t))throw Error(R(200));return hl(null,e,t,!0,n)};Nt.hydrateRoot=function(e,t,n){if(!Hd(e))throw Error(R(405));var r=n!=null&&n.hydratedSources||null,s=!1,i="",a=pg;if(n!=null&&(n.unstable_strictMode===!0&&(s=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(a=n.onRecoverableError)),t=mg(t,null,e,1,n??null,s,!1,i,a),e[wn]=t.current,Go(e),r)for(e=0;e<r.length;e++)n=r[e],s=n._getVersion,s=s(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,s]:t.mutableSourceEagerHydrationData.push(n,s);return new ml(t)};Nt.render=function(e,t,n){if(!pl(t))throw Error(R(200));return hl(null,e,t,!1,n)};Nt.unmountComponentAtNode=function(e){if(!pl(e))throw Error(R(40));return e._reactRootContainer?(zr(function(){hl(null,null,e,!1,function(){e._reactRootContainer=null,e[wn]=null})}),!0):!1};Nt.unstable_batchedUpdates=Ld;Nt.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!pl(n))throw Error(R(200));if(e==null||e._reactInternals===void 0)throw Error(R(38));return hl(e,t,n,!1,r)};Nt.version="18.3.1-next-f1338f8080-20240426";function hg(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(hg)}catch(e){console.error(e)}}hg(),hh.exports=Nt;var Gr=hh.exports;const xg=nh(Gr);var gg,Xm=Gr;gg=Xm.createRoot,Xm.hydrateRoot;const Wj=1,Kj=1e6;let xc=0;function Gj(){return xc=(xc+1)%Number.MAX_SAFE_INTEGER,xc.toString()}const gc=new Map,Jm=e=>{if(gc.has(e))return;const t=setTimeout(()=>{gc.delete(e),Fo({type:"REMOVE_TOAST",toastId:e})},Kj);gc.set(e,t)},Qj=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,Wj)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(n=>n.id===t.toast.id?{...n,...t.toast}:n)};case"DISMISS_TOAST":{const{toastId:n}=t;return n?Jm(n):e.toasts.forEach(r=>{Jm(r.id)}),{...e,toasts:e.toasts.map(r=>r.id===n||n===void 0?{...r,open:!1}:r)}}case"REMOVE_TOAST":return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(n=>n.id!==t.toastId)}}},da=[];let fa={toasts:[]};function Fo(e){fa=Qj(fa,e),da.forEach(t=>{t(fa)})}function Yj({...e}){const t=Gj(),n=s=>Fo({type:"UPDATE_TOAST",toast:{...s,id:t}}),r=()=>Fo({type:"DISMISS_TOAST",toastId:t});return Fo({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:s=>{s||r()}}}),{id:t,dismiss:r,update:n}}function Qr(){const[e,t]=f.useState(fa);return f.useEffect(()=>(da.push(t),()=>{const n=da.indexOf(t);n>-1&&da.splice(n,1)}),[e]),{...e,toast:Yj,dismiss:n=>Fo({type:"DISMISS_TOAST",toastId:n})}}function z(e,t,{checkForDefaultPrevented:n=!0}={}){return function(s){if(e==null||e(s),n===!1||!s.defaultPrevented)return t==null?void 0:t(s)}}function qj(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function xl(...e){return t=>e.forEach(n=>qj(n,t))}function me(...e){return f.useCallback(xl(...e),e)}function Xj(e,t=[]){let n=[];function r(i,a){const l=f.createContext(a),c=n.length;n=[...n,a];function u(m){const{scope:d,children:v,...w}=m,h=(d==null?void 0:d[e][c])||l,j=f.useMemo(()=>w,Object.values(w));return o.jsx(h.Provider,{value:j,children:v})}function p(m,d){const v=(d==null?void 0:d[e][c])||l,w=f.useContext(v);if(w)return w;if(a!==void 0)return a;throw new Error(`\`${m}\` must be used within \`${i}\``)}return u.displayName=i+"Provider",[u,p]}const s=()=>{const i=n.map(a=>f.createContext(a));return function(l){const c=(l==null?void 0:l[e])||i;return f.useMemo(()=>({[`__scope${e}`]:{...l,[e]:c}}),[l,c])}};return s.scopeName=e,[r,Jj(s,...t)]}function Jj(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(s=>({useScope:s(),scopeName:s.scopeName}));return function(i){const a=r.reduce((l,{useScope:c,scopeName:u})=>{const m=c(i)[`__scope${u}`];return{...l,...m}},{});return f.useMemo(()=>({[`__scope${t.scopeName}`]:a}),[a])}};return n.scopeName=t.scopeName,n}var $r=f.forwardRef((e,t)=>{const{children:n,...r}=e,s=f.Children.toArray(n),i=s.find(Zj);if(i){const a=i.props.children,l=s.map(c=>c===i?f.Children.count(a)>1?f.Children.only(null):f.isValidElement(a)?a.props.children:null:c);return o.jsx(Su,{...r,ref:t,children:f.isValidElement(a)?f.cloneElement(a,void 0,l):null})}return o.jsx(Su,{...r,ref:t,children:n})});$r.displayName="Slot";var Su=f.forwardRef((e,t)=>{const{children:n,...r}=e;if(f.isValidElement(n)){const s=tN(n);return f.cloneElement(n,{...eN(r,n.props),ref:t?xl(t,s):s})}return f.Children.count(n)>1?f.Children.only(null):null});Su.displayName="SlotClone";var vg=({children:e})=>o.jsx(o.Fragment,{children:e});function Zj(e){return f.isValidElement(e)&&e.type===vg}function eN(e,t){const n={...t};for(const r in t){const s=e[r],i=t[r];/^on[A-Z]/.test(r)?s&&i?n[r]=(...l)=>{i(...l),s(...l)}:s&&(n[r]=s):r==="style"?n[r]={...s,...i}:r==="className"&&(n[r]=[s,i].filter(Boolean).join(" "))}return{...e,...n}}function tN(e){var r,s;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(s=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:s.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function gl(e){const t=e+"CollectionProvider",[n,r]=Xj(t),[s,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),a=v=>{const{scope:w,children:h}=v,j=O.useRef(null),g=O.useRef(new Map).current;return o.jsx(s,{scope:w,itemMap:g,collectionRef:j,children:h})};a.displayName=t;const l=e+"CollectionSlot",c=O.forwardRef((v,w)=>{const{scope:h,children:j}=v,g=i(l,h),x=me(w,g.collectionRef);return o.jsx($r,{ref:x,children:j})});c.displayName=l;const u=e+"CollectionItemSlot",p="data-radix-collection-item",m=O.forwardRef((v,w)=>{const{scope:h,children:j,...g}=v,x=O.useRef(null),y=me(w,x),N=i(u,h);return O.useEffect(()=>(N.itemMap.set(x,{ref:x,...g}),()=>void N.itemMap.delete(x))),o.jsx($r,{[p]:"",ref:y,children:j})});m.displayName=u;function d(v){const w=i(e+"CollectionConsumer",v);return O.useCallback(()=>{const j=w.collectionRef.current;if(!j)return[];const g=Array.from(j.querySelectorAll(`[${p}]`));return Array.from(w.itemMap.values()).sort((N,b)=>g.indexOf(N.ref.current)-g.indexOf(b.ref.current))},[w.collectionRef,w.itemMap])}return[{Provider:a,Slot:c,ItemSlot:m},d,r]}function eo(e,t=[]){let n=[];function r(i,a){const l=f.createContext(a),c=n.length;n=[...n,a];const u=m=>{var g;const{scope:d,children:v,...w}=m,h=((g=d==null?void 0:d[e])==null?void 0:g[c])||l,j=f.useMemo(()=>w,Object.values(w));return o.jsx(h.Provider,{value:j,children:v})};u.displayName=i+"Provider";function p(m,d){var h;const v=((h=d==null?void 0:d[e])==null?void 0:h[c])||l,w=f.useContext(v);if(w)return w;if(a!==void 0)return a;throw new Error(`\`${m}\` must be used within \`${i}\``)}return[u,p]}const s=()=>{const i=n.map(a=>f.createContext(a));return function(l){const c=(l==null?void 0:l[e])||i;return f.useMemo(()=>({[`__scope${e}`]:{...l,[e]:c}}),[l,c])}};return s.scopeName=e,[r,nN(s,...t)]}function nN(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(s=>({useScope:s(),scopeName:s.scopeName}));return function(i){const a=r.reduce((l,{useScope:c,scopeName:u})=>{const m=c(i)[`__scope${u}`];return{...l,...m}},{});return f.useMemo(()=>({[`__scope${t.scopeName}`]:a}),[a])}};return n.scopeName=t.scopeName,n}var rN=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],Y=rN.reduce((e,t)=>{const n=f.forwardRef((r,s)=>{const{asChild:i,...a}=r,l=i?$r:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),o.jsx(l,{...a,ref:s})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function Wd(e,t){e&&Gr.flushSync(()=>e.dispatchEvent(t))}function Xe(e){const t=f.useRef(e);return f.useEffect(()=>{t.current=e}),f.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function sN(e,t=globalThis==null?void 0:globalThis.document){const n=Xe(e);f.useEffect(()=>{const r=s=>{s.key==="Escape"&&n(s)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var oN="DismissableLayer",Cu="dismissableLayer.update",iN="dismissableLayer.pointerDownOutside",aN="dismissableLayer.focusOutside",Zm,yg=f.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),vi=f.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:s,onFocusOutside:i,onInteractOutside:a,onDismiss:l,...c}=e,u=f.useContext(yg),[p,m]=f.useState(null),d=(p==null?void 0:p.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,v]=f.useState({}),w=me(t,C=>m(C)),h=Array.from(u.layers),[j]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),g=h.indexOf(j),x=p?h.indexOf(p):-1,y=u.layersWithOutsidePointerEventsDisabled.size>0,N=x>=g,b=cN(C=>{const k=C.target,I=[...u.branches].some(M=>M.contains(k));!N||I||(s==null||s(C),a==null||a(C),C.defaultPrevented||l==null||l())},d),E=uN(C=>{const k=C.target;[...u.branches].some(M=>M.contains(k))||(i==null||i(C),a==null||a(C),C.defaultPrevented||l==null||l())},d);return sN(C=>{x===u.layers.size-1&&(r==null||r(C),!C.defaultPrevented&&l&&(C.preventDefault(),l()))},d),f.useEffect(()=>{if(p)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(Zm=d.body.style.pointerEvents,d.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(p)),u.layers.add(p),ep(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(d.body.style.pointerEvents=Zm)}},[p,d,n,u]),f.useEffect(()=>()=>{p&&(u.layers.delete(p),u.layersWithOutsidePointerEventsDisabled.delete(p),ep())},[p,u]),f.useEffect(()=>{const C=()=>v({});return document.addEventListener(Cu,C),()=>document.removeEventListener(Cu,C)},[]),o.jsx(Y.div,{...c,ref:w,style:{pointerEvents:y?N?"auto":"none":void 0,...e.style},onFocusCapture:z(e.onFocusCapture,E.onFocusCapture),onBlurCapture:z(e.onBlurCapture,E.onBlurCapture),onPointerDownCapture:z(e.onPointerDownCapture,b.onPointerDownCapture)})});vi.displayName=oN;var lN="DismissableLayerBranch",wg=f.forwardRef((e,t)=>{const n=f.useContext(yg),r=f.useRef(null),s=me(t,r);return f.useEffect(()=>{const i=r.current;if(i)return n.branches.add(i),()=>{n.branches.delete(i)}},[n.branches]),o.jsx(Y.div,{...e,ref:s})});wg.displayName=lN;function cN(e,t=globalThis==null?void 0:globalThis.document){const n=Xe(e),r=f.useRef(!1),s=f.useRef(()=>{});return f.useEffect(()=>{const i=l=>{if(l.target&&!r.current){let c=function(){jg(iN,n,u,{discrete:!0})};const u={originalEvent:l};l.pointerType==="touch"?(t.removeEventListener("click",s.current),s.current=c,t.addEventListener("click",s.current,{once:!0})):c()}else t.removeEventListener("click",s.current);r.current=!1},a=window.setTimeout(()=>{t.addEventListener("pointerdown",i)},0);return()=>{window.clearTimeout(a),t.removeEventListener("pointerdown",i),t.removeEventListener("click",s.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function uN(e,t=globalThis==null?void 0:globalThis.document){const n=Xe(e),r=f.useRef(!1);return f.useEffect(()=>{const s=i=>{i.target&&!r.current&&jg(aN,n,{originalEvent:i},{discrete:!1})};return t.addEventListener("focusin",s),()=>t.removeEventListener("focusin",s)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function ep(){const e=new CustomEvent(Cu);document.dispatchEvent(e)}function jg(e,t,n,{discrete:r}){const s=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&s.addEventListener(e,t,{once:!0}),r?Wd(s,i):s.dispatchEvent(i)}var dN=vi,fN=wg,Je=globalThis!=null&&globalThis.document?f.useLayoutEffect:()=>{},mN="Portal",vl=f.forwardRef((e,t)=>{var l;const{container:n,...r}=e,[s,i]=f.useState(!1);Je(()=>i(!0),[]);const a=n||s&&((l=globalThis==null?void 0:globalThis.document)==null?void 0:l.body);return a?xg.createPortal(o.jsx(Y.div,{...r,ref:t}),a):null});vl.displayName=mN;function pN(e,t){return f.useReducer((n,r)=>t[n][r]??n,e)}var xr=e=>{const{present:t,children:n}=e,r=hN(t),s=typeof n=="function"?n({present:r.isPresent}):f.Children.only(n),i=me(r.ref,xN(s));return typeof n=="function"||r.isPresent?f.cloneElement(s,{ref:i}):null};xr.displayName="Presence";function hN(e){const[t,n]=f.useState(),r=f.useRef({}),s=f.useRef(e),i=f.useRef("none"),a=e?"mounted":"unmounted",[l,c]=pN(a,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return f.useEffect(()=>{const u=Hi(r.current);i.current=l==="mounted"?u:"none"},[l]),Je(()=>{const u=r.current,p=s.current;if(p!==e){const d=i.current,v=Hi(u);e?c("MOUNT"):v==="none"||(u==null?void 0:u.display)==="none"?c("UNMOUNT"):c(p&&d!==v?"ANIMATION_OUT":"UNMOUNT"),s.current=e}},[e,c]),Je(()=>{if(t){let u;const p=t.ownerDocument.defaultView??window,m=v=>{const h=Hi(r.current).includes(v.animationName);if(v.target===t&&h&&(c("ANIMATION_END"),!s.current)){const j=t.style.animationFillMode;t.style.animationFillMode="forwards",u=p.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=j)})}},d=v=>{v.target===t&&(i.current=Hi(r.current))};return t.addEventListener("animationstart",d),t.addEventListener("animationcancel",m),t.addEventListener("animationend",m),()=>{p.clearTimeout(u),t.removeEventListener("animationstart",d),t.removeEventListener("animationcancel",m),t.removeEventListener("animationend",m)}}else c("ANIMATION_END")},[t,c]),{isPresent:["mounted","unmountSuspended"].includes(l),ref:f.useCallback(u=>{u&&(r.current=getComputedStyle(u)),n(u)},[])}}function Hi(e){return(e==null?void 0:e.animationName)||"none"}function xN(e){var r,s;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(s=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:s.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function Ks({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,s]=gN({defaultProp:t,onChange:n}),i=e!==void 0,a=i?e:r,l=Xe(n),c=f.useCallback(u=>{if(i){const m=typeof u=="function"?u(e):u;m!==e&&l(m)}else s(u)},[i,e,s,l]);return[a,c]}function gN({defaultProp:e,onChange:t}){const n=f.useState(e),[r]=n,s=f.useRef(r),i=Xe(t);return f.useEffect(()=>{s.current!==r&&(i(r),s.current=r)},[r,s,i]),n}var vN="VisuallyHidden",yi=f.forwardRef((e,t)=>o.jsx(Y.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));yi.displayName=vN;var yN=yi,Kd="ToastProvider",[Gd,wN,jN]=gl("Toast"),[Ng,O5]=eo("Toast",[jN]),[NN,yl]=Ng(Kd),bg=e=>{const{__scopeToast:t,label:n="Notification",duration:r=5e3,swipeDirection:s="right",swipeThreshold:i=50,children:a}=e,[l,c]=f.useState(null),[u,p]=f.useState(0),m=f.useRef(!1),d=f.useRef(!1);return n.trim()||console.error(`Invalid prop \`label\` supplied to \`${Kd}\`. Expected non-empty \`string\`.`),o.jsx(Gd.Provider,{scope:t,children:o.jsx(NN,{scope:t,label:n,duration:r,swipeDirection:s,swipeThreshold:i,toastCount:u,viewport:l,onViewportChange:c,onToastAdd:f.useCallback(()=>p(v=>v+1),[]),onToastRemove:f.useCallback(()=>p(v=>v-1),[]),isFocusedToastEscapeKeyDownRef:m,isClosePausedRef:d,children:a})})};bg.displayName=Kd;var Sg="ToastViewport",bN=["F8"],Eu="toast.viewportPause",ku="toast.viewportResume",Cg=f.forwardRef((e,t)=>{const{__scopeToast:n,hotkey:r=bN,label:s="Notifications ({hotkey})",...i}=e,a=yl(Sg,n),l=wN(n),c=f.useRef(null),u=f.useRef(null),p=f.useRef(null),m=f.useRef(null),d=me(t,m,a.onViewportChange),v=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),w=a.toastCount>0;f.useEffect(()=>{const j=g=>{var y;r.length!==0&&r.every(N=>g[N]||g.code===N)&&((y=m.current)==null||y.focus())};return document.addEventListener("keydown",j),()=>document.removeEventListener("keydown",j)},[r]),f.useEffect(()=>{const j=c.current,g=m.current;if(w&&j&&g){const x=()=>{if(!a.isClosePausedRef.current){const E=new CustomEvent(Eu);g.dispatchEvent(E),a.isClosePausedRef.current=!0}},y=()=>{if(a.isClosePausedRef.current){const E=new CustomEvent(ku);g.dispatchEvent(E),a.isClosePausedRef.current=!1}},N=E=>{!j.contains(E.relatedTarget)&&y()},b=()=>{j.contains(document.activeElement)||y()};return j.addEventListener("focusin",x),j.addEventListener("focusout",N),j.addEventListener("pointermove",x),j.addEventListener("pointerleave",b),window.addEventListener("blur",x),window.addEventListener("focus",y),()=>{j.removeEventListener("focusin",x),j.removeEventListener("focusout",N),j.removeEventListener("pointermove",x),j.removeEventListener("pointerleave",b),window.removeEventListener("blur",x),window.removeEventListener("focus",y)}}},[w,a.isClosePausedRef]);const h=f.useCallback(({tabbingDirection:j})=>{const x=l().map(y=>{const N=y.ref.current,b=[N,...DN(N)];return j==="forwards"?b:b.reverse()});return(j==="forwards"?x.reverse():x).flat()},[l]);return f.useEffect(()=>{const j=m.current;if(j){const g=x=>{var b,E,C;const y=x.altKey||x.ctrlKey||x.metaKey;if(x.key==="Tab"&&!y){const k=document.activeElement,I=x.shiftKey;if(x.target===j&&I){(b=u.current)==null||b.focus();return}const D=h({tabbingDirection:I?"backwards":"forwards"}),W=D.findIndex(A=>A===k);vc(D.slice(W+1))?x.preventDefault():I?(E=u.current)==null||E.focus():(C=p.current)==null||C.focus()}};return j.addEventListener("keydown",g),()=>j.removeEventListener("keydown",g)}},[l,h]),o.jsxs(fN,{ref:c,role:"region","aria-label":s.replace("{hotkey}",v),tabIndex:-1,style:{pointerEvents:w?void 0:"none"},children:[w&&o.jsx(Pu,{ref:u,onFocusFromOutsideViewport:()=>{const j=h({tabbingDirection:"forwards"});vc(j)}}),o.jsx(Gd.Slot,{scope:n,children:o.jsx(Y.ol,{tabIndex:-1,...i,ref:d})}),w&&o.jsx(Pu,{ref:p,onFocusFromOutsideViewport:()=>{const j=h({tabbingDirection:"backwards"});vc(j)}})]})});Cg.displayName=Sg;var Eg="ToastFocusProxy",Pu=f.forwardRef((e,t)=>{const{__scopeToast:n,onFocusFromOutsideViewport:r,...s}=e,i=yl(Eg,n);return o.jsx(yi,{"aria-hidden":!0,tabIndex:0,...s,ref:t,style:{position:"fixed"},onFocus:a=>{var u;const l=a.relatedTarget;!((u=i.viewport)!=null&&u.contains(l))&&r()}})});Pu.displayName=Eg;var wl="Toast",SN="toast.swipeStart",CN="toast.swipeMove",EN="toast.swipeCancel",kN="toast.swipeEnd",kg=f.forwardRef((e,t)=>{const{forceMount:n,open:r,defaultOpen:s,onOpenChange:i,...a}=e,[l=!0,c]=Ks({prop:r,defaultProp:s,onChange:i});return o.jsx(xr,{present:n||l,children:o.jsx(RN,{open:l,...a,ref:t,onClose:()=>c(!1),onPause:Xe(e.onPause),onResume:Xe(e.onResume),onSwipeStart:z(e.onSwipeStart,u=>{u.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:z(e.onSwipeMove,u=>{const{x:p,y:m}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","move"),u.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${p}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${m}px`)}),onSwipeCancel:z(e.onSwipeCancel,u=>{u.currentTarget.setAttribute("data-swipe","cancel"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:z(e.onSwipeEnd,u=>{const{x:p,y:m}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","end"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${p}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${m}px`),c(!1)})})})});kg.displayName=wl;var[PN,TN]=Ng(wl,{onClose(){}}),RN=f.forwardRef((e,t)=>{const{__scopeToast:n,type:r="foreground",duration:s,open:i,onClose:a,onEscapeKeyDown:l,onPause:c,onResume:u,onSwipeStart:p,onSwipeMove:m,onSwipeCancel:d,onSwipeEnd:v,...w}=e,h=yl(wl,n),[j,g]=f.useState(null),x=me(t,A=>g(A)),y=f.useRef(null),N=f.useRef(null),b=s||h.duration,E=f.useRef(0),C=f.useRef(b),k=f.useRef(0),{onToastAdd:I,onToastRemove:M}=h,U=Xe(()=>{var K;(j==null?void 0:j.contains(document.activeElement))&&((K=h.viewport)==null||K.focus()),a()}),D=f.useCallback(A=>{!A||A===1/0||(window.clearTimeout(k.current),E.current=new Date().getTime(),k.current=window.setTimeout(U,A))},[U]);f.useEffect(()=>{const A=h.viewport;if(A){const K=()=>{D(C.current),u==null||u()},F=()=>{const G=new Date().getTime()-E.current;C.current=C.current-G,window.clearTimeout(k.current),c==null||c()};return A.addEventListener(Eu,F),A.addEventListener(ku,K),()=>{A.removeEventListener(Eu,F),A.removeEventListener(ku,K)}}},[h.viewport,b,c,u,D]),f.useEffect(()=>{i&&!h.isClosePausedRef.current&&D(b)},[i,b,h.isClosePausedRef,D]),f.useEffect(()=>(I(),()=>M()),[I,M]);const W=f.useMemo(()=>j?_g(j):null,[j]);return h.viewport?o.jsxs(o.Fragment,{children:[W&&o.jsx(MN,{__scopeToast:n,role:"status","aria-live":r==="foreground"?"assertive":"polite","aria-atomic":!0,children:W}),o.jsx(PN,{scope:n,onClose:U,children:Gr.createPortal(o.jsx(Gd.ItemSlot,{scope:n,children:o.jsx(dN,{asChild:!0,onEscapeKeyDown:z(l,()=>{h.isFocusedToastEscapeKeyDownRef.current||U(),h.isFocusedToastEscapeKeyDownRef.current=!1}),children:o.jsx(Y.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":i?"open":"closed","data-swipe-direction":h.swipeDirection,...w,ref:x,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:z(e.onKeyDown,A=>{A.key==="Escape"&&(l==null||l(A.nativeEvent),A.nativeEvent.defaultPrevented||(h.isFocusedToastEscapeKeyDownRef.current=!0,U()))}),onPointerDown:z(e.onPointerDown,A=>{A.button===0&&(y.current={x:A.clientX,y:A.clientY})}),onPointerMove:z(e.onPointerMove,A=>{if(!y.current)return;const K=A.clientX-y.current.x,F=A.clientY-y.current.y,G=!!N.current,S=["left","right"].includes(h.swipeDirection),P=["left","up"].includes(h.swipeDirection)?Math.min:Math.max,L=S?P(0,K):0,_=S?0:P(0,F),B=A.pointerType==="touch"?10:2,Q={x:L,y:_},re={originalEvent:A,delta:Q};G?(N.current=Q,Wi(CN,m,re,{discrete:!1})):tp(Q,h.swipeDirection,B)?(N.current=Q,Wi(SN,p,re,{discrete:!1}),A.target.setPointerCapture(A.pointerId)):(Math.abs(K)>B||Math.abs(F)>B)&&(y.current=null)}),onPointerUp:z(e.onPointerUp,A=>{const K=N.current,F=A.target;if(F.hasPointerCapture(A.pointerId)&&F.releasePointerCapture(A.pointerId),N.current=null,y.current=null,K){const G=A.currentTarget,S={originalEvent:A,delta:K};tp(K,h.swipeDirection,h.swipeThreshold)?Wi(kN,v,S,{discrete:!0}):Wi(EN,d,S,{discrete:!0}),G.addEventListener("click",P=>P.preventDefault(),{once:!0})}})})})}),h.viewport)})]}):null}),MN=e=>{const{__scopeToast:t,children:n,...r}=e,s=yl(wl,t),[i,a]=f.useState(!1),[l,c]=f.useState(!1);return _N(()=>a(!0)),f.useEffect(()=>{const u=window.setTimeout(()=>c(!0),1e3);return()=>window.clearTimeout(u)},[]),l?null:o.jsx(vl,{asChild:!0,children:o.jsx(yi,{...r,children:i&&o.jsxs(o.Fragment,{children:[s.label," ",n]})})})},AN="ToastTitle",Pg=f.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return o.jsx(Y.div,{...r,ref:t})});Pg.displayName=AN;var IN="ToastDescription",Tg=f.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return o.jsx(Y.div,{...r,ref:t})});Tg.displayName=IN;var Rg="ToastAction",Mg=f.forwardRef((e,t)=>{const{altText:n,...r}=e;return n.trim()?o.jsx(Ig,{altText:n,asChild:!0,children:o.jsx(Qd,{...r,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${Rg}\`. Expected non-empty \`string\`.`),null)});Mg.displayName=Rg;var Ag="ToastClose",Qd=f.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e,s=TN(Ag,n);return o.jsx(Ig,{asChild:!0,children:o.jsx(Y.button,{type:"button",...r,ref:t,onClick:z(e.onClick,s.onClose)})})});Qd.displayName=Ag;var Ig=f.forwardRef((e,t)=>{const{__scopeToast:n,altText:r,...s}=e;return o.jsx(Y.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...s,ref:t})});function _g(e){const t=[];return Array.from(e.childNodes).forEach(r=>{if(r.nodeType===r.TEXT_NODE&&r.textContent&&t.push(r.textContent),ON(r)){const s=r.ariaHidden||r.hidden||r.style.display==="none",i=r.dataset.radixToastAnnounceExclude==="";if(!s)if(i){const a=r.dataset.radixToastAnnounceAlt;a&&t.push(a)}else t.push(..._g(r))}}),t}function Wi(e,t,n,{discrete:r}){const s=n.originalEvent.currentTarget,i=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&s.addEventListener(e,t,{once:!0}),r?Wd(s,i):s.dispatchEvent(i)}var tp=(e,t,n=0)=>{const r=Math.abs(e.x),s=Math.abs(e.y),i=r>s;return t==="left"||t==="right"?i&&r>n:!i&&s>n};function _N(e=()=>{}){const t=Xe(e);Je(()=>{let n=0,r=0;return n=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(n),window.cancelAnimationFrame(r)}},[t])}function ON(e){return e.nodeType===e.ELEMENT_NODE}function DN(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const s=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||s?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function vc(e){const t=document.activeElement;return e.some(n=>n===t?!0:(n.focus(),document.activeElement!==t))}var LN=bg,Og=Cg,Dg=kg,Lg=Pg,Fg=Tg,zg=Mg,$g=Qd;function Ug(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var s=e.length;for(t=0;t<s;t++)e[t]&&(n=Ug(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function Bg(){for(var e,t,n=0,r="",s=arguments.length;n<s;n++)(e=arguments[n])&&(t=Ug(e))&&(r&&(r+=" "),r+=t);return r}const np=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,rp=Bg,Yd=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return rp(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:s,defaultVariants:i}=t,a=Object.keys(s).map(u=>{const p=n==null?void 0:n[u],m=i==null?void 0:i[u];if(p===null)return null;const d=np(p)||np(m);return s[u][d]}),l=n&&Object.entries(n).reduce((u,p)=>{let[m,d]=p;return d===void 0||(u[m]=d),u},{}),c=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((u,p)=>{let{class:m,className:d,...v}=p;return Object.entries(v).every(w=>{let[h,j]=w;return Array.isArray(j)?j.includes({...i,...l}[h]):{...i,...l}[h]===j})?[...u,m,d]:u},[]);return rp(e,a,c,n==null?void 0:n.class,n==null?void 0:n.className)};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const FN=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Vg=(...e)=>e.filter((t,n,r)=>!!t&&t.trim()!==""&&r.indexOf(t)===n).join(" ").trim();/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var zN={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $N=f.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:s="",children:i,iconNode:a,...l},c)=>f.createElement("svg",{ref:c,...zN,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:Vg("lucide",s),...l},[...a.map(([u,p])=>f.createElement(u,p)),...Array.isArray(i)?i:[i]]));/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pe=(e,t)=>{const n=f.forwardRef(({className:r,...s},i)=>f.createElement($N,{ref:i,iconNode:t,className:Vg(`lucide-${FN(e)}`,r),...s}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const UN=pe("Activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jl=pe("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nl=pe("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hg=pe("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wg=pe("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const BN=pe("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const VN=pe("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tu=pe("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const HN=pe("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sp=pe("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yr=pe("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qd=pe("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kg=pe("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const WN=pe("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gs=pe("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bn=pe("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const KN=pe("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $a=pe("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const op=pe("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gr=pe("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xd=pe("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const GN=pe("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const QN=pe("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gg=pe("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jd=pe("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ua=pe("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zd=pe("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),ef="-",YN=e=>{const t=XN(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:a=>{const l=a.split(ef);return l[0]===""&&l.length!==1&&l.shift(),Qg(l,t)||qN(a)},getConflictingClassGroupIds:(a,l)=>{const c=n[a]||[];return l&&r[a]?[...c,...r[a]]:c}}},Qg=(e,t)=>{var a;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),s=r?Qg(e.slice(1),r):void 0;if(s)return s;if(t.validators.length===0)return;const i=e.join(ef);return(a=t.validators.find(({validator:l})=>l(i)))==null?void 0:a.classGroupId},ip=/^\[(.+)\]$/,qN=e=>{if(ip.test(e)){const t=ip.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},XN=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return ZN(Object.entries(e.classGroups),n).forEach(([i,a])=>{Ru(a,r,i,t)}),r},Ru=(e,t,n,r)=>{e.forEach(s=>{if(typeof s=="string"){const i=s===""?t:ap(t,s);i.classGroupId=n;return}if(typeof s=="function"){if(JN(s)){Ru(s(r),t,n,r);return}t.validators.push({validator:s,classGroupId:n});return}Object.entries(s).forEach(([i,a])=>{Ru(a,ap(t,i),n,r)})})},ap=(e,t)=>{let n=e;return t.split(ef).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},JN=e=>e.isThemeGetter,ZN=(e,t)=>t?e.map(([n,r])=>{const s=r.map(i=>typeof i=="string"?t+i:typeof i=="object"?Object.fromEntries(Object.entries(i).map(([a,l])=>[t+a,l])):i);return[n,s]}):e,eb=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const s=(i,a)=>{n.set(i,a),t++,t>e&&(t=0,r=n,n=new Map)};return{get(i){let a=n.get(i);if(a!==void 0)return a;if((a=r.get(i))!==void 0)return s(i,a),a},set(i,a){n.has(i)?n.set(i,a):s(i,a)}}},Yg="!",tb=e=>{const{separator:t,experimentalParseClassName:n}=e,r=t.length===1,s=t[0],i=t.length,a=l=>{const c=[];let u=0,p=0,m;for(let j=0;j<l.length;j++){let g=l[j];if(u===0){if(g===s&&(r||l.slice(j,j+i)===t)){c.push(l.slice(p,j)),p=j+i;continue}if(g==="/"){m=j;continue}}g==="["?u++:g==="]"&&u--}const d=c.length===0?l:l.substring(p),v=d.startsWith(Yg),w=v?d.substring(1):d,h=m&&m>p?m-p:void 0;return{modifiers:c,hasImportantModifier:v,baseClassName:w,maybePostfixModifierPosition:h}};return n?l=>n({className:l,parseClassName:a}):a},nb=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t},rb=e=>({cache:eb(e.cacheSize),parseClassName:tb(e),...YN(e)}),sb=/\s+/,ob=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:s}=t,i=[],a=e.trim().split(sb);let l="";for(let c=a.length-1;c>=0;c-=1){const u=a[c],{modifiers:p,hasImportantModifier:m,baseClassName:d,maybePostfixModifierPosition:v}=n(u);let w=!!v,h=r(w?d.substring(0,v):d);if(!h){if(!w){l=u+(l.length>0?" "+l:l);continue}if(h=r(d),!h){l=u+(l.length>0?" "+l:l);continue}w=!1}const j=nb(p).join(":"),g=m?j+Yg:j,x=g+h;if(i.includes(x))continue;i.push(x);const y=s(h,w);for(let N=0;N<y.length;++N){const b=y[N];i.push(g+b)}l=u+(l.length>0?" "+l:l)}return l};function ib(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=qg(t))&&(r&&(r+=" "),r+=n);return r}const qg=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=qg(e[r]))&&(n&&(n+=" "),n+=t);return n};function ab(e,...t){let n,r,s,i=a;function a(c){const u=t.reduce((p,m)=>m(p),e());return n=rb(u),r=n.cache.get,s=n.cache.set,i=l,l(c)}function l(c){const u=r(c);if(u)return u;const p=ob(c,n);return s(c,p),p}return function(){return i(ib.apply(null,arguments))}}const ve=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},Xg=/^\[(?:([a-z-]+):)?(.+)\]$/i,lb=/^\d+\/\d+$/,cb=new Set(["px","full","screen"]),ub=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,db=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,fb=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,mb=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,pb=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,dn=e=>Es(e)||cb.has(e)||lb.test(e),On=e=>to(e,"length",Nb),Es=e=>!!e&&!Number.isNaN(Number(e)),yc=e=>to(e,"number",Es),go=e=>!!e&&Number.isInteger(Number(e)),hb=e=>e.endsWith("%")&&Es(e.slice(0,-1)),J=e=>Xg.test(e),Dn=e=>ub.test(e),xb=new Set(["length","size","percentage"]),gb=e=>to(e,xb,Jg),vb=e=>to(e,"position",Jg),yb=new Set(["image","url"]),wb=e=>to(e,yb,Sb),jb=e=>to(e,"",bb),vo=()=>!0,to=(e,t,n)=>{const r=Xg.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1},Nb=e=>db.test(e)&&!fb.test(e),Jg=()=>!1,bb=e=>mb.test(e),Sb=e=>pb.test(e),Cb=()=>{const e=ve("colors"),t=ve("spacing"),n=ve("blur"),r=ve("brightness"),s=ve("borderColor"),i=ve("borderRadius"),a=ve("borderSpacing"),l=ve("borderWidth"),c=ve("contrast"),u=ve("grayscale"),p=ve("hueRotate"),m=ve("invert"),d=ve("gap"),v=ve("gradientColorStops"),w=ve("gradientColorStopPositions"),h=ve("inset"),j=ve("margin"),g=ve("opacity"),x=ve("padding"),y=ve("saturate"),N=ve("scale"),b=ve("sepia"),E=ve("skew"),C=ve("space"),k=ve("translate"),I=()=>["auto","contain","none"],M=()=>["auto","hidden","clip","visible","scroll"],U=()=>["auto",J,t],D=()=>[J,t],W=()=>["",dn,On],A=()=>["auto",Es,J],K=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],F=()=>["solid","dashed","dotted","double","none"],G=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],S=()=>["start","end","center","between","around","evenly","stretch"],P=()=>["","0",J],L=()=>["auto","avoid","all","avoid-page","page","left","right","column"],_=()=>[Es,J];return{cacheSize:500,separator:":",theme:{colors:[vo],spacing:[dn,On],blur:["none","",Dn,J],brightness:_(),borderColor:[e],borderRadius:["none","","full",Dn,J],borderSpacing:D(),borderWidth:W(),contrast:_(),grayscale:P(),hueRotate:_(),invert:P(),gap:D(),gradientColorStops:[e],gradientColorStopPositions:[hb,On],inset:U(),margin:U(),opacity:_(),padding:D(),saturate:_(),scale:_(),sepia:P(),skew:_(),space:D(),translate:D()},classGroups:{aspect:[{aspect:["auto","square","video",J]}],container:["container"],columns:[{columns:[Dn]}],"break-after":[{"break-after":L()}],"break-before":[{"break-before":L()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...K(),J]}],overflow:[{overflow:M()}],"overflow-x":[{"overflow-x":M()}],"overflow-y":[{"overflow-y":M()}],overscroll:[{overscroll:I()}],"overscroll-x":[{"overscroll-x":I()}],"overscroll-y":[{"overscroll-y":I()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[h]}],"inset-x":[{"inset-x":[h]}],"inset-y":[{"inset-y":[h]}],start:[{start:[h]}],end:[{end:[h]}],top:[{top:[h]}],right:[{right:[h]}],bottom:[{bottom:[h]}],left:[{left:[h]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",go,J]}],basis:[{basis:U()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",J]}],grow:[{grow:P()}],shrink:[{shrink:P()}],order:[{order:["first","last","none",go,J]}],"grid-cols":[{"grid-cols":[vo]}],"col-start-end":[{col:["auto",{span:["full",go,J]},J]}],"col-start":[{"col-start":A()}],"col-end":[{"col-end":A()}],"grid-rows":[{"grid-rows":[vo]}],"row-start-end":[{row:["auto",{span:[go,J]},J]}],"row-start":[{"row-start":A()}],"row-end":[{"row-end":A()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",J]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",J]}],gap:[{gap:[d]}],"gap-x":[{"gap-x":[d]}],"gap-y":[{"gap-y":[d]}],"justify-content":[{justify:["normal",...S()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...S(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...S(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[x]}],px:[{px:[x]}],py:[{py:[x]}],ps:[{ps:[x]}],pe:[{pe:[x]}],pt:[{pt:[x]}],pr:[{pr:[x]}],pb:[{pb:[x]}],pl:[{pl:[x]}],m:[{m:[j]}],mx:[{mx:[j]}],my:[{my:[j]}],ms:[{ms:[j]}],me:[{me:[j]}],mt:[{mt:[j]}],mr:[{mr:[j]}],mb:[{mb:[j]}],ml:[{ml:[j]}],"space-x":[{"space-x":[C]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[C]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",J,t]}],"min-w":[{"min-w":[J,t,"min","max","fit"]}],"max-w":[{"max-w":[J,t,"none","full","min","max","fit","prose",{screen:[Dn]},Dn]}],h:[{h:[J,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[J,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[J,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[J,t,"auto","min","max","fit"]}],"font-size":[{text:["base",Dn,On]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",yc]}],"font-family":[{font:[vo]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",J]}],"line-clamp":[{"line-clamp":["none",Es,yc]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",dn,J]}],"list-image":[{"list-image":["none",J]}],"list-style-type":[{list:["none","disc","decimal",J]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[g]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[g]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...F(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",dn,On]}],"underline-offset":[{"underline-offset":["auto",dn,J]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:D()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",J]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",J]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[g]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...K(),vb]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",gb]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},wb]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[w]}],"gradient-via-pos":[{via:[w]}],"gradient-to-pos":[{to:[w]}],"gradient-from":[{from:[v]}],"gradient-via":[{via:[v]}],"gradient-to":[{to:[v]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[l]}],"border-w-x":[{"border-x":[l]}],"border-w-y":[{"border-y":[l]}],"border-w-s":[{"border-s":[l]}],"border-w-e":[{"border-e":[l]}],"border-w-t":[{"border-t":[l]}],"border-w-r":[{"border-r":[l]}],"border-w-b":[{"border-b":[l]}],"border-w-l":[{"border-l":[l]}],"border-opacity":[{"border-opacity":[g]}],"border-style":[{border:[...F(),"hidden"]}],"divide-x":[{"divide-x":[l]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[l]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[g]}],"divide-style":[{divide:F()}],"border-color":[{border:[s]}],"border-color-x":[{"border-x":[s]}],"border-color-y":[{"border-y":[s]}],"border-color-s":[{"border-s":[s]}],"border-color-e":[{"border-e":[s]}],"border-color-t":[{"border-t":[s]}],"border-color-r":[{"border-r":[s]}],"border-color-b":[{"border-b":[s]}],"border-color-l":[{"border-l":[s]}],"divide-color":[{divide:[s]}],"outline-style":[{outline:["",...F()]}],"outline-offset":[{"outline-offset":[dn,J]}],"outline-w":[{outline:[dn,On]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:W()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[g]}],"ring-offset-w":[{"ring-offset":[dn,On]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",Dn,jb]}],"shadow-color":[{shadow:[vo]}],opacity:[{opacity:[g]}],"mix-blend":[{"mix-blend":[...G(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":G()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[c]}],"drop-shadow":[{"drop-shadow":["","none",Dn,J]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[p]}],invert:[{invert:[m]}],saturate:[{saturate:[y]}],sepia:[{sepia:[b]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[c]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[p]}],"backdrop-invert":[{"backdrop-invert":[m]}],"backdrop-opacity":[{"backdrop-opacity":[g]}],"backdrop-saturate":[{"backdrop-saturate":[y]}],"backdrop-sepia":[{"backdrop-sepia":[b]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[a]}],"border-spacing-x":[{"border-spacing-x":[a]}],"border-spacing-y":[{"border-spacing-y":[a]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",J]}],duration:[{duration:_()}],ease:[{ease:["linear","in","out","in-out",J]}],delay:[{delay:_()}],animate:[{animate:["none","spin","ping","pulse","bounce",J]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[N]}],"scale-x":[{"scale-x":[N]}],"scale-y":[{"scale-y":[N]}],rotate:[{rotate:[go,J]}],"translate-x":[{"translate-x":[k]}],"translate-y":[{"translate-y":[k]}],"skew-x":[{"skew-x":[E]}],"skew-y":[{"skew-y":[E]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",J]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",J]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":D()}],"scroll-mx":[{"scroll-mx":D()}],"scroll-my":[{"scroll-my":D()}],"scroll-ms":[{"scroll-ms":D()}],"scroll-me":[{"scroll-me":D()}],"scroll-mt":[{"scroll-mt":D()}],"scroll-mr":[{"scroll-mr":D()}],"scroll-mb":[{"scroll-mb":D()}],"scroll-ml":[{"scroll-ml":D()}],"scroll-p":[{"scroll-p":D()}],"scroll-px":[{"scroll-px":D()}],"scroll-py":[{"scroll-py":D()}],"scroll-ps":[{"scroll-ps":D()}],"scroll-pe":[{"scroll-pe":D()}],"scroll-pt":[{"scroll-pt":D()}],"scroll-pr":[{"scroll-pr":D()}],"scroll-pb":[{"scroll-pb":D()}],"scroll-pl":[{"scroll-pl":D()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",J]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[dn,On,yc]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},Eb=ab(Cb);function ee(...e){return Eb(Bg(e))}const kb=LN,Zg=f.forwardRef(({className:e,...t},n)=>o.jsx(Og,{ref:n,className:ee("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));Zg.displayName=Og.displayName;const Pb=Yd("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),ev=f.forwardRef(({className:e,variant:t,...n},r)=>o.jsx(Dg,{ref:r,className:ee(Pb({variant:t}),e),...n}));ev.displayName=Dg.displayName;const Tb=f.forwardRef(({className:e,...t},n)=>o.jsx(zg,{ref:n,className:ee("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t}));Tb.displayName=zg.displayName;const tv=f.forwardRef(({className:e,...t},n)=>o.jsx($g,{ref:n,className:ee("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:o.jsx(Zd,{className:"h-4 w-4"})}));tv.displayName=$g.displayName;const nv=f.forwardRef(({className:e,...t},n)=>o.jsx(Lg,{ref:n,className:ee("text-sm font-semibold",e),...t}));nv.displayName=Lg.displayName;const rv=f.forwardRef(({className:e,...t},n)=>o.jsx(Fg,{ref:n,className:ee("text-sm opacity-90",e),...t}));rv.displayName=Fg.displayName;function Rb(){const{toasts:e}=Qr();return o.jsxs(kb,{children:[e.map(function({id:t,title:n,description:r,action:s,...i}){return o.jsxs(ev,{...i,children:[o.jsxs("div",{className:"grid gap-1",children:[n&&o.jsx(nv,{children:n}),r&&o.jsx(rv,{children:r})]}),s,o.jsx(tv,{})]},t)}),o.jsx(Zg,{})]})}var Mb=e=>{switch(e){case"success":return _b;case"info":return Db;case"warning":return Ob;case"error":return Lb;default:return null}},Ab=Array(12).fill(0),Ib=({visible:e})=>O.createElement("div",{className:"sonner-loading-wrapper","data-visible":e},O.createElement("div",{className:"sonner-spinner"},Ab.map((t,n)=>O.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${n}`})))),_b=O.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},O.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),Ob=O.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},O.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),Db=O.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},O.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),Lb=O.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},O.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),Fb=()=>{let[e,t]=O.useState(document.hidden);return O.useEffect(()=>{let n=()=>{t(document.hidden)};return document.addEventListener("visibilitychange",n),()=>window.removeEventListener("visibilitychange",n)},[]),e},Mu=1,zb=class{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)}),this.publish=e=>{this.subscribers.forEach(t=>t(e))},this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]},this.create=e=>{var t;let{message:n,...r}=e,s=typeof(e==null?void 0:e.id)=="number"||((t=e.id)==null?void 0:t.length)>0?e.id:Mu++,i=this.toasts.find(l=>l.id===s),a=e.dismissible===void 0?!0:e.dismissible;return i?this.toasts=this.toasts.map(l=>l.id===s?(this.publish({...l,...e,id:s,title:n}),{...l,...e,id:s,dismissible:a,title:n}):l):this.addToast({title:n,...r,dismissible:a,id:s}),s},this.dismiss=e=>(e||this.toasts.forEach(t=>{this.subscribers.forEach(n=>n({id:t.id,dismiss:!0}))}),this.subscribers.forEach(t=>t({id:e,dismiss:!0})),e),this.message=(e,t)=>this.create({...t,message:e}),this.error=(e,t)=>this.create({...t,message:e,type:"error"}),this.success=(e,t)=>this.create({...t,type:"success",message:e}),this.info=(e,t)=>this.create({...t,type:"info",message:e}),this.warning=(e,t)=>this.create({...t,type:"warning",message:e}),this.loading=(e,t)=>this.create({...t,type:"loading",message:e}),this.promise=(e,t)=>{if(!t)return;let n;t.loading!==void 0&&(n=this.create({...t,promise:e,type:"loading",message:t.loading,description:typeof t.description!="function"?t.description:void 0}));let r=e instanceof Promise?e:e(),s=n!==void 0;return r.then(async i=>{if(Ub(i)&&!i.ok){s=!1;let a=typeof t.error=="function"?await t.error(`HTTP error! status: ${i.status}`):t.error,l=typeof t.description=="function"?await t.description(`HTTP error! status: ${i.status}`):t.description;this.create({id:n,type:"error",message:a,description:l})}else if(t.success!==void 0){s=!1;let a=typeof t.success=="function"?await t.success(i):t.success,l=typeof t.description=="function"?await t.description(i):t.description;this.create({id:n,type:"success",message:a,description:l})}}).catch(async i=>{if(t.error!==void 0){s=!1;let a=typeof t.error=="function"?await t.error(i):t.error,l=typeof t.description=="function"?await t.description(i):t.description;this.create({id:n,type:"error",message:a,description:l})}}).finally(()=>{var i;s&&(this.dismiss(n),n=void 0),(i=t.finally)==null||i.call(t)}),n},this.custom=(e,t)=>{let n=(t==null?void 0:t.id)||Mu++;return this.create({jsx:e(n),id:n,...t}),n},this.subscribers=[],this.toasts=[]}},ht=new zb,$b=(e,t)=>{let n=(t==null?void 0:t.id)||Mu++;return ht.addToast({title:e,...t,id:n}),n},Ub=e=>e&&typeof e=="object"&&"ok"in e&&typeof e.ok=="boolean"&&"status"in e&&typeof e.status=="number",Bb=$b,Vb=()=>ht.toasts;Object.assign(Bb,{success:ht.success,info:ht.info,warning:ht.warning,error:ht.error,custom:ht.custom,message:ht.message,promise:ht.promise,dismiss:ht.dismiss,loading:ht.loading},{getHistory:Vb});function Hb(e,{insertAt:t}={}){if(typeof document>"u")return;let n=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css",t==="top"&&n.firstChild?n.insertBefore(r,n.firstChild):n.appendChild(r),r.styleSheet?r.styleSheet.cssText=e:r.appendChild(document.createTextNode(e))}Hb(`:where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999}:where([data-sonner-toaster][data-x-position="right"]){right:max(var(--offset),env(safe-area-inset-right))}:where([data-sonner-toaster][data-x-position="left"]){left:max(var(--offset),env(safe-area-inset-left))}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:max(var(--offset),env(safe-area-inset-top))}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:max(var(--offset),env(safe-area-inset-bottom))}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;background:var(--gray1);color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:0;right:0;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount, 0px));transition:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation:swipe-out .2s ease-out forwards}@keyframes swipe-out{0%{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount)));opacity:1}to{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount) + var(--lift) * -100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;--mobile-offset: 16px;right:var(--mobile-offset);left:var(--mobile-offset);width:100%}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset)}[data-sonner-toaster][data-y-position=bottom]{bottom:20px}[data-sonner-toaster][data-y-position=top]{top:20px}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset);right:var(--mobile-offset);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}
`);function Ki(e){return e.label!==void 0}var Wb=3,Kb="32px",Gb=4e3,Qb=356,Yb=14,qb=20,Xb=200;function Jb(...e){return e.filter(Boolean).join(" ")}var Zb=e=>{var t,n,r,s,i,a,l,c,u,p;let{invert:m,toast:d,unstyled:v,interacting:w,setHeights:h,visibleToasts:j,heights:g,index:x,toasts:y,expanded:N,removeToast:b,defaultRichColors:E,closeButton:C,style:k,cancelButtonStyle:I,actionButtonStyle:M,className:U="",descriptionClassName:D="",duration:W,position:A,gap:K,loadingIcon:F,expandByDefault:G,classNames:S,icons:P,closeButtonAriaLabel:L="Close toast",pauseWhenPageIsHidden:_,cn:B}=e,[Q,re]=O.useState(!1),[Pe,q]=O.useState(!1),[H,se]=O.useState(!1),[ie,ne]=O.useState(!1),[ue,ae]=O.useState(0),[Ze,mt]=O.useState(0),An=O.useRef(null),St=O.useRef(null),jr=x===0,$l=x+1<=j,$e=d.type,es=d.dismissible!==!1,fw=d.className||"",mw=d.descriptionClassName||"",Si=O.useMemo(()=>g.findIndex(X=>X.toastId===d.id)||0,[g,d.id]),pw=O.useMemo(()=>{var X;return(X=d.closeButton)!=null?X:C},[d.closeButton,C]),Lf=O.useMemo(()=>d.duration||W||Gb,[d.duration,W]),Ul=O.useRef(0),ts=O.useRef(0),Ff=O.useRef(0),ns=O.useRef(null),[zf,hw]=A.split("-"),$f=O.useMemo(()=>g.reduce((X,ge,he)=>he>=Si?X:X+ge.height,0),[g,Si]),Uf=Fb(),xw=d.invert||m,Bl=$e==="loading";ts.current=O.useMemo(()=>Si*K+$f,[Si,$f]),O.useEffect(()=>{re(!0)},[]),O.useLayoutEffect(()=>{if(!Q)return;let X=St.current,ge=X.style.height;X.style.height="auto";let he=X.getBoundingClientRect().height;X.style.height=ge,mt(he),h(Qt=>Qt.find(Yt=>Yt.toastId===d.id)?Qt.map(Yt=>Yt.toastId===d.id?{...Yt,height:he}:Yt):[{toastId:d.id,height:he,position:d.position},...Qt])},[Q,d.title,d.description,h,d.id]);let In=O.useCallback(()=>{q(!0),ae(ts.current),h(X=>X.filter(ge=>ge.toastId!==d.id)),setTimeout(()=>{b(d)},Xb)},[d,b,h,ts]);O.useEffect(()=>{if(d.promise&&$e==="loading"||d.duration===1/0||d.type==="loading")return;let X,ge=Lf;return N||w||_&&Uf?(()=>{if(Ff.current<Ul.current){let he=new Date().getTime()-Ul.current;ge=ge-he}Ff.current=new Date().getTime()})():ge!==1/0&&(Ul.current=new Date().getTime(),X=setTimeout(()=>{var he;(he=d.onAutoClose)==null||he.call(d,d),In()},ge)),()=>clearTimeout(X)},[N,w,G,d,Lf,In,d.promise,$e,_,Uf]),O.useEffect(()=>{let X=St.current;if(X){let ge=X.getBoundingClientRect().height;return mt(ge),h(he=>[{toastId:d.id,height:ge,position:d.position},...he]),()=>h(he=>he.filter(Qt=>Qt.toastId!==d.id))}},[h,d.id]),O.useEffect(()=>{d.delete&&In()},[In,d.delete]);function gw(){return P!=null&&P.loading?O.createElement("div",{className:"sonner-loader","data-visible":$e==="loading"},P.loading):F?O.createElement("div",{className:"sonner-loader","data-visible":$e==="loading"},F):O.createElement(Ib,{visible:$e==="loading"})}return O.createElement("li",{"aria-live":d.important?"assertive":"polite","aria-atomic":"true",role:"status",tabIndex:0,ref:St,className:B(U,fw,S==null?void 0:S.toast,(t=d==null?void 0:d.classNames)==null?void 0:t.toast,S==null?void 0:S.default,S==null?void 0:S[$e],(n=d==null?void 0:d.classNames)==null?void 0:n[$e]),"data-sonner-toast":"","data-rich-colors":(r=d.richColors)!=null?r:E,"data-styled":!(d.jsx||d.unstyled||v),"data-mounted":Q,"data-promise":!!d.promise,"data-removed":Pe,"data-visible":$l,"data-y-position":zf,"data-x-position":hw,"data-index":x,"data-front":jr,"data-swiping":H,"data-dismissible":es,"data-type":$e,"data-invert":xw,"data-swipe-out":ie,"data-expanded":!!(N||G&&Q),style:{"--index":x,"--toasts-before":x,"--z-index":y.length-x,"--offset":`${Pe?ue:ts.current}px`,"--initial-height":G?"auto":`${Ze}px`,...k,...d.style},onPointerDown:X=>{Bl||!es||(An.current=new Date,ae(ts.current),X.target.setPointerCapture(X.pointerId),X.target.tagName!=="BUTTON"&&(se(!0),ns.current={x:X.clientX,y:X.clientY}))},onPointerUp:()=>{var X,ge,he,Qt;if(ie||!es)return;ns.current=null;let Yt=Number(((X=St.current)==null?void 0:X.style.getPropertyValue("--swipe-amount").replace("px",""))||0),Ci=new Date().getTime()-((ge=An.current)==null?void 0:ge.getTime()),vw=Math.abs(Yt)/Ci;if(Math.abs(Yt)>=qb||vw>.11){ae(ts.current),(he=d.onDismiss)==null||he.call(d,d),In(),ne(!0);return}(Qt=St.current)==null||Qt.style.setProperty("--swipe-amount","0px"),se(!1)},onPointerMove:X=>{var ge;if(!ns.current||!es)return;let he=X.clientY-ns.current.y,Qt=X.clientX-ns.current.x,Yt=(zf==="top"?Math.min:Math.max)(0,he),Ci=X.pointerType==="touch"?10:2;Math.abs(Yt)>Ci?(ge=St.current)==null||ge.style.setProperty("--swipe-amount",`${he}px`):Math.abs(Qt)>Ci&&(ns.current=null)}},pw&&!d.jsx?O.createElement("button",{"aria-label":L,"data-disabled":Bl,"data-close-button":!0,onClick:Bl||!es?()=>{}:()=>{var X;In(),(X=d.onDismiss)==null||X.call(d,d)},className:B(S==null?void 0:S.closeButton,(s=d==null?void 0:d.classNames)==null?void 0:s.closeButton)},O.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},O.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),O.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"}))):null,d.jsx||O.isValidElement(d.title)?d.jsx||d.title:O.createElement(O.Fragment,null,$e||d.icon||d.promise?O.createElement("div",{"data-icon":"",className:B(S==null?void 0:S.icon,(i=d==null?void 0:d.classNames)==null?void 0:i.icon)},d.promise||d.type==="loading"&&!d.icon?d.icon||gw():null,d.type!=="loading"?d.icon||(P==null?void 0:P[$e])||Mb($e):null):null,O.createElement("div",{"data-content":"",className:B(S==null?void 0:S.content,(a=d==null?void 0:d.classNames)==null?void 0:a.content)},O.createElement("div",{"data-title":"",className:B(S==null?void 0:S.title,(l=d==null?void 0:d.classNames)==null?void 0:l.title)},d.title),d.description?O.createElement("div",{"data-description":"",className:B(D,mw,S==null?void 0:S.description,(c=d==null?void 0:d.classNames)==null?void 0:c.description)},d.description):null),O.isValidElement(d.cancel)?d.cancel:d.cancel&&Ki(d.cancel)?O.createElement("button",{"data-button":!0,"data-cancel":!0,style:d.cancelButtonStyle||I,onClick:X=>{var ge,he;Ki(d.cancel)&&es&&((he=(ge=d.cancel).onClick)==null||he.call(ge,X),In())},className:B(S==null?void 0:S.cancelButton,(u=d==null?void 0:d.classNames)==null?void 0:u.cancelButton)},d.cancel.label):null,O.isValidElement(d.action)?d.action:d.action&&Ki(d.action)?O.createElement("button",{"data-button":!0,"data-action":!0,style:d.actionButtonStyle||M,onClick:X=>{var ge,he;Ki(d.action)&&(X.defaultPrevented||((he=(ge=d.action).onClick)==null||he.call(ge,X),In()))},className:B(S==null?void 0:S.actionButton,(p=d==null?void 0:d.classNames)==null?void 0:p.actionButton)},d.action.label):null))};function lp(){if(typeof window>"u"||typeof document>"u")return"ltr";let e=document.documentElement.getAttribute("dir");return e==="auto"||!e?window.getComputedStyle(document.documentElement).direction:e}var e2=e=>{let{invert:t,position:n="bottom-right",hotkey:r=["altKey","KeyT"],expand:s,closeButton:i,className:a,offset:l,theme:c="light",richColors:u,duration:p,style:m,visibleToasts:d=Wb,toastOptions:v,dir:w=lp(),gap:h=Yb,loadingIcon:j,icons:g,containerAriaLabel:x="Notifications",pauseWhenPageIsHidden:y,cn:N=Jb}=e,[b,E]=O.useState([]),C=O.useMemo(()=>Array.from(new Set([n].concat(b.filter(_=>_.position).map(_=>_.position)))),[b,n]),[k,I]=O.useState([]),[M,U]=O.useState(!1),[D,W]=O.useState(!1),[A,K]=O.useState(c!=="system"?c:typeof window<"u"&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),F=O.useRef(null),G=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),S=O.useRef(null),P=O.useRef(!1),L=O.useCallback(_=>{var B;(B=b.find(Q=>Q.id===_.id))!=null&&B.delete||ht.dismiss(_.id),E(Q=>Q.filter(({id:re})=>re!==_.id))},[b]);return O.useEffect(()=>ht.subscribe(_=>{if(_.dismiss){E(B=>B.map(Q=>Q.id===_.id?{...Q,delete:!0}:Q));return}setTimeout(()=>{xg.flushSync(()=>{E(B=>{let Q=B.findIndex(re=>re.id===_.id);return Q!==-1?[...B.slice(0,Q),{...B[Q],..._},...B.slice(Q+1)]:[_,...B]})})})}),[]),O.useEffect(()=>{if(c!=="system"){K(c);return}c==="system"&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?K("dark"):K("light")),typeof window<"u"&&window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",({matches:_})=>{K(_?"dark":"light")})},[c]),O.useEffect(()=>{b.length<=1&&U(!1)},[b]),O.useEffect(()=>{let _=B=>{var Q,re;r.every(Pe=>B[Pe]||B.code===Pe)&&(U(!0),(Q=F.current)==null||Q.focus()),B.code==="Escape"&&(document.activeElement===F.current||(re=F.current)!=null&&re.contains(document.activeElement))&&U(!1)};return document.addEventListener("keydown",_),()=>document.removeEventListener("keydown",_)},[r]),O.useEffect(()=>{if(F.current)return()=>{S.current&&(S.current.focus({preventScroll:!0}),S.current=null,P.current=!1)}},[F.current]),b.length?O.createElement("section",{"aria-label":`${x} ${G}`,tabIndex:-1},C.map((_,B)=>{var Q;let[re,Pe]=_.split("-");return O.createElement("ol",{key:_,dir:w==="auto"?lp():w,tabIndex:-1,ref:F,className:a,"data-sonner-toaster":!0,"data-theme":A,"data-y-position":re,"data-x-position":Pe,style:{"--front-toast-height":`${((Q=k[0])==null?void 0:Q.height)||0}px`,"--offset":typeof l=="number"?`${l}px`:l||Kb,"--width":`${Qb}px`,"--gap":`${h}px`,...m},onBlur:q=>{P.current&&!q.currentTarget.contains(q.relatedTarget)&&(P.current=!1,S.current&&(S.current.focus({preventScroll:!0}),S.current=null))},onFocus:q=>{q.target instanceof HTMLElement&&q.target.dataset.dismissible==="false"||P.current||(P.current=!0,S.current=q.relatedTarget)},onMouseEnter:()=>U(!0),onMouseMove:()=>U(!0),onMouseLeave:()=>{D||U(!1)},onPointerDown:q=>{q.target instanceof HTMLElement&&q.target.dataset.dismissible==="false"||W(!0)},onPointerUp:()=>W(!1)},b.filter(q=>!q.position&&B===0||q.position===_).map((q,H)=>{var se,ie;return O.createElement(Zb,{key:q.id,icons:g,index:H,toast:q,defaultRichColors:u,duration:(se=v==null?void 0:v.duration)!=null?se:p,className:v==null?void 0:v.className,descriptionClassName:v==null?void 0:v.descriptionClassName,invert:t,visibleToasts:d,closeButton:(ie=v==null?void 0:v.closeButton)!=null?ie:i,interacting:D,position:_,style:v==null?void 0:v.style,unstyled:v==null?void 0:v.unstyled,classNames:v==null?void 0:v.classNames,cancelButtonStyle:v==null?void 0:v.cancelButtonStyle,actionButtonStyle:v==null?void 0:v.actionButtonStyle,removeToast:L,toasts:b.filter(ne=>ne.position==q.position),heights:k.filter(ne=>ne.position==q.position),setHeights:I,expandByDefault:s,gap:h,loadingIcon:j,expanded:M,pauseWhenPageIsHidden:y,cn:N})}))})):null};const t2=({...e})=>o.jsx(e2,{theme:"system",className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...e});var n2=mh.useId||(()=>{}),r2=0;function Ur(e){const[t,n]=f.useState(n2());return Je(()=>{e||n(r=>r??String(r2++))},[e]),e||(t?`radix-${t}`:"")}const s2=["top","right","bottom","left"],cr=Math.min,gt=Math.max,Ba=Math.round,Gi=Math.floor,ur=e=>({x:e,y:e}),o2={left:"right",right:"left",bottom:"top",top:"bottom"},i2={start:"end",end:"start"};function Au(e,t,n){return gt(e,cr(t,n))}function Sn(e,t){return typeof e=="function"?e(t):e}function Cn(e){return e.split("-")[0]}function no(e){return e.split("-")[1]}function tf(e){return e==="x"?"y":"x"}function nf(e){return e==="y"?"height":"width"}function dr(e){return["top","bottom"].includes(Cn(e))?"y":"x"}function rf(e){return tf(dr(e))}function a2(e,t,n){n===void 0&&(n=!1);const r=no(e),s=rf(e),i=nf(s);let a=s==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=Va(a)),[a,Va(a)]}function l2(e){const t=Va(e);return[Iu(e),t,Iu(t)]}function Iu(e){return e.replace(/start|end/g,t=>i2[t])}function c2(e,t,n){const r=["left","right"],s=["right","left"],i=["top","bottom"],a=["bottom","top"];switch(e){case"top":case"bottom":return n?t?s:r:t?r:s;case"left":case"right":return t?i:a;default:return[]}}function u2(e,t,n,r){const s=no(e);let i=c2(Cn(e),n==="start",r);return s&&(i=i.map(a=>a+"-"+s),t&&(i=i.concat(i.map(Iu)))),i}function Va(e){return e.replace(/left|right|bottom|top/g,t=>o2[t])}function d2(e){return{top:0,right:0,bottom:0,left:0,...e}}function sv(e){return typeof e!="number"?d2(e):{top:e,right:e,bottom:e,left:e}}function Ha(e){const{x:t,y:n,width:r,height:s}=e;return{width:r,height:s,top:n,left:t,right:t+r,bottom:n+s,x:t,y:n}}function cp(e,t,n){let{reference:r,floating:s}=e;const i=dr(t),a=rf(t),l=nf(a),c=Cn(t),u=i==="y",p=r.x+r.width/2-s.width/2,m=r.y+r.height/2-s.height/2,d=r[l]/2-s[l]/2;let v;switch(c){case"top":v={x:p,y:r.y-s.height};break;case"bottom":v={x:p,y:r.y+r.height};break;case"right":v={x:r.x+r.width,y:m};break;case"left":v={x:r.x-s.width,y:m};break;default:v={x:r.x,y:r.y}}switch(no(t)){case"start":v[a]-=d*(n&&u?-1:1);break;case"end":v[a]+=d*(n&&u?-1:1);break}return v}const f2=async(e,t,n)=>{const{placement:r="bottom",strategy:s="absolute",middleware:i=[],platform:a}=n,l=i.filter(Boolean),c=await(a.isRTL==null?void 0:a.isRTL(t));let u=await a.getElementRects({reference:e,floating:t,strategy:s}),{x:p,y:m}=cp(u,r,c),d=r,v={},w=0;for(let h=0;h<l.length;h++){const{name:j,fn:g}=l[h],{x,y,data:N,reset:b}=await g({x:p,y:m,initialPlacement:r,placement:d,strategy:s,middlewareData:v,rects:u,platform:a,elements:{reference:e,floating:t}});p=x??p,m=y??m,v={...v,[j]:{...v[j],...N}},b&&w<=50&&(w++,typeof b=="object"&&(b.placement&&(d=b.placement),b.rects&&(u=b.rects===!0?await a.getElementRects({reference:e,floating:t,strategy:s}):b.rects),{x:p,y:m}=cp(u,d,c)),h=-1)}return{x:p,y:m,placement:d,strategy:s,middlewareData:v}};async function ni(e,t){var n;t===void 0&&(t={});const{x:r,y:s,platform:i,rects:a,elements:l,strategy:c}=e,{boundary:u="clippingAncestors",rootBoundary:p="viewport",elementContext:m="floating",altBoundary:d=!1,padding:v=0}=Sn(t,e),w=sv(v),j=l[d?m==="floating"?"reference":"floating":m],g=Ha(await i.getClippingRect({element:(n=await(i.isElement==null?void 0:i.isElement(j)))==null||n?j:j.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(l.floating)),boundary:u,rootBoundary:p,strategy:c})),x=m==="floating"?{x:r,y:s,width:a.floating.width,height:a.floating.height}:a.reference,y=await(i.getOffsetParent==null?void 0:i.getOffsetParent(l.floating)),N=await(i.isElement==null?void 0:i.isElement(y))?await(i.getScale==null?void 0:i.getScale(y))||{x:1,y:1}:{x:1,y:1},b=Ha(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:x,offsetParent:y,strategy:c}):x);return{top:(g.top-b.top+w.top)/N.y,bottom:(b.bottom-g.bottom+w.bottom)/N.y,left:(g.left-b.left+w.left)/N.x,right:(b.right-g.right+w.right)/N.x}}const m2=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:s,rects:i,platform:a,elements:l,middlewareData:c}=t,{element:u,padding:p=0}=Sn(e,t)||{};if(u==null)return{};const m=sv(p),d={x:n,y:r},v=rf(s),w=nf(v),h=await a.getDimensions(u),j=v==="y",g=j?"top":"left",x=j?"bottom":"right",y=j?"clientHeight":"clientWidth",N=i.reference[w]+i.reference[v]-d[v]-i.floating[w],b=d[v]-i.reference[v],E=await(a.getOffsetParent==null?void 0:a.getOffsetParent(u));let C=E?E[y]:0;(!C||!await(a.isElement==null?void 0:a.isElement(E)))&&(C=l.floating[y]||i.floating[w]);const k=N/2-b/2,I=C/2-h[w]/2-1,M=cr(m[g],I),U=cr(m[x],I),D=M,W=C-h[w]-U,A=C/2-h[w]/2+k,K=Au(D,A,W),F=!c.arrow&&no(s)!=null&&A!==K&&i.reference[w]/2-(A<D?M:U)-h[w]/2<0,G=F?A<D?A-D:A-W:0;return{[v]:d[v]+G,data:{[v]:K,centerOffset:A-K-G,...F&&{alignmentOffset:G}},reset:F}}}),p2=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:s,middlewareData:i,rects:a,initialPlacement:l,platform:c,elements:u}=t,{mainAxis:p=!0,crossAxis:m=!0,fallbackPlacements:d,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:w="none",flipAlignment:h=!0,...j}=Sn(e,t);if((n=i.arrow)!=null&&n.alignmentOffset)return{};const g=Cn(s),x=dr(l),y=Cn(l)===l,N=await(c.isRTL==null?void 0:c.isRTL(u.floating)),b=d||(y||!h?[Va(l)]:l2(l)),E=w!=="none";!d&&E&&b.push(...u2(l,h,w,N));const C=[l,...b],k=await ni(t,j),I=[];let M=((r=i.flip)==null?void 0:r.overflows)||[];if(p&&I.push(k[g]),m){const A=a2(s,a,N);I.push(k[A[0]],k[A[1]])}if(M=[...M,{placement:s,overflows:I}],!I.every(A=>A<=0)){var U,D;const A=(((U=i.flip)==null?void 0:U.index)||0)+1,K=C[A];if(K)return{data:{index:A,overflows:M},reset:{placement:K}};let F=(D=M.filter(G=>G.overflows[0]<=0).sort((G,S)=>G.overflows[1]-S.overflows[1])[0])==null?void 0:D.placement;if(!F)switch(v){case"bestFit":{var W;const G=(W=M.filter(S=>{if(E){const P=dr(S.placement);return P===x||P==="y"}return!0}).map(S=>[S.placement,S.overflows.filter(P=>P>0).reduce((P,L)=>P+L,0)]).sort((S,P)=>S[1]-P[1])[0])==null?void 0:W[0];G&&(F=G);break}case"initialPlacement":F=l;break}if(s!==F)return{reset:{placement:F}}}return{}}}};function up(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function dp(e){return s2.some(t=>e[t]>=0)}const h2=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...s}=Sn(e,t);switch(r){case"referenceHidden":{const i=await ni(t,{...s,elementContext:"reference"}),a=up(i,n.reference);return{data:{referenceHiddenOffsets:a,referenceHidden:dp(a)}}}case"escaped":{const i=await ni(t,{...s,altBoundary:!0}),a=up(i,n.floating);return{data:{escapedOffsets:a,escaped:dp(a)}}}default:return{}}}}};async function x2(e,t){const{placement:n,platform:r,elements:s}=e,i=await(r.isRTL==null?void 0:r.isRTL(s.floating)),a=Cn(n),l=no(n),c=dr(n)==="y",u=["left","top"].includes(a)?-1:1,p=i&&c?-1:1,m=Sn(t,e);let{mainAxis:d,crossAxis:v,alignmentAxis:w}=typeof m=="number"?{mainAxis:m,crossAxis:0,alignmentAxis:null}:{mainAxis:m.mainAxis||0,crossAxis:m.crossAxis||0,alignmentAxis:m.alignmentAxis};return l&&typeof w=="number"&&(v=l==="end"?w*-1:w),c?{x:v*p,y:d*u}:{x:d*u,y:v*p}}const g2=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:s,y:i,placement:a,middlewareData:l}=t,c=await x2(t,e);return a===((n=l.offset)==null?void 0:n.placement)&&(r=l.arrow)!=null&&r.alignmentOffset?{}:{x:s+c.x,y:i+c.y,data:{...c,placement:a}}}}},v2=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:s}=t,{mainAxis:i=!0,crossAxis:a=!1,limiter:l={fn:j=>{let{x:g,y:x}=j;return{x:g,y:x}}},...c}=Sn(e,t),u={x:n,y:r},p=await ni(t,c),m=dr(Cn(s)),d=tf(m);let v=u[d],w=u[m];if(i){const j=d==="y"?"top":"left",g=d==="y"?"bottom":"right",x=v+p[j],y=v-p[g];v=Au(x,v,y)}if(a){const j=m==="y"?"top":"left",g=m==="y"?"bottom":"right",x=w+p[j],y=w-p[g];w=Au(x,w,y)}const h=l.fn({...t,[d]:v,[m]:w});return{...h,data:{x:h.x-n,y:h.y-r,enabled:{[d]:i,[m]:a}}}}}},y2=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:s,rects:i,middlewareData:a}=t,{offset:l=0,mainAxis:c=!0,crossAxis:u=!0}=Sn(e,t),p={x:n,y:r},m=dr(s),d=tf(m);let v=p[d],w=p[m];const h=Sn(l,t),j=typeof h=="number"?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(c){const y=d==="y"?"height":"width",N=i.reference[d]-i.floating[y]+j.mainAxis,b=i.reference[d]+i.reference[y]-j.mainAxis;v<N?v=N:v>b&&(v=b)}if(u){var g,x;const y=d==="y"?"width":"height",N=["top","left"].includes(Cn(s)),b=i.reference[m]-i.floating[y]+(N&&((g=a.offset)==null?void 0:g[m])||0)+(N?0:j.crossAxis),E=i.reference[m]+i.reference[y]+(N?0:((x=a.offset)==null?void 0:x[m])||0)-(N?j.crossAxis:0);w<b?w=b:w>E&&(w=E)}return{[d]:v,[m]:w}}}},w2=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:s,rects:i,platform:a,elements:l}=t,{apply:c=()=>{},...u}=Sn(e,t),p=await ni(t,u),m=Cn(s),d=no(s),v=dr(s)==="y",{width:w,height:h}=i.floating;let j,g;m==="top"||m==="bottom"?(j=m,g=d===(await(a.isRTL==null?void 0:a.isRTL(l.floating))?"start":"end")?"left":"right"):(g=m,j=d==="end"?"top":"bottom");const x=h-p.top-p.bottom,y=w-p.left-p.right,N=cr(h-p[j],x),b=cr(w-p[g],y),E=!t.middlewareData.shift;let C=N,k=b;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(k=y),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(C=x),E&&!d){const M=gt(p.left,0),U=gt(p.right,0),D=gt(p.top,0),W=gt(p.bottom,0);v?k=w-2*(M!==0||U!==0?M+U:gt(p.left,p.right)):C=h-2*(D!==0||W!==0?D+W:gt(p.top,p.bottom))}await c({...t,availableWidth:k,availableHeight:C});const I=await a.getDimensions(l.floating);return w!==I.width||h!==I.height?{reset:{rects:!0}}:{}}}};function bl(){return typeof window<"u"}function ro(e){return ov(e)?(e.nodeName||"").toLowerCase():"#document"}function wt(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function un(e){var t;return(t=(ov(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function ov(e){return bl()?e instanceof Node||e instanceof wt(e).Node:!1}function Kt(e){return bl()?e instanceof Element||e instanceof wt(e).Element:!1}function an(e){return bl()?e instanceof HTMLElement||e instanceof wt(e).HTMLElement:!1}function fp(e){return!bl()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof wt(e).ShadowRoot}function wi(e){const{overflow:t,overflowX:n,overflowY:r,display:s}=Gt(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(s)}function j2(e){return["table","td","th"].includes(ro(e))}function Sl(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function sf(e){const t=of(),n=Kt(e)?Gt(e):e;return n.transform!=="none"||n.perspective!=="none"||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function N2(e){let t=fr(e);for(;an(t)&&!Qs(t);){if(sf(t))return t;if(Sl(t))return null;t=fr(t)}return null}function of(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Qs(e){return["html","body","#document"].includes(ro(e))}function Gt(e){return wt(e).getComputedStyle(e)}function Cl(e){return Kt(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function fr(e){if(ro(e)==="html")return e;const t=e.assignedSlot||e.parentNode||fp(e)&&e.host||un(e);return fp(t)?t.host:t}function iv(e){const t=fr(e);return Qs(t)?e.ownerDocument?e.ownerDocument.body:e.body:an(t)&&wi(t)?t:iv(t)}function ri(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const s=iv(e),i=s===((r=e.ownerDocument)==null?void 0:r.body),a=wt(s);if(i){const l=_u(a);return t.concat(a,a.visualViewport||[],wi(s)?s:[],l&&n?ri(l):[])}return t.concat(s,ri(s,[],n))}function _u(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function av(e){const t=Gt(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const s=an(e),i=s?e.offsetWidth:n,a=s?e.offsetHeight:r,l=Ba(n)!==i||Ba(r)!==a;return l&&(n=i,r=a),{width:n,height:r,$:l}}function af(e){return Kt(e)?e:e.contextElement}function ks(e){const t=af(e);if(!an(t))return ur(1);const n=t.getBoundingClientRect(),{width:r,height:s,$:i}=av(t);let a=(i?Ba(n.width):n.width)/r,l=(i?Ba(n.height):n.height)/s;return(!a||!Number.isFinite(a))&&(a=1),(!l||!Number.isFinite(l))&&(l=1),{x:a,y:l}}const b2=ur(0);function lv(e){const t=wt(e);return!of()||!t.visualViewport?b2:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function S2(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==wt(e)?!1:t}function Br(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const s=e.getBoundingClientRect(),i=af(e);let a=ur(1);t&&(r?Kt(r)&&(a=ks(r)):a=ks(e));const l=S2(i,n,r)?lv(i):ur(0);let c=(s.left+l.x)/a.x,u=(s.top+l.y)/a.y,p=s.width/a.x,m=s.height/a.y;if(i){const d=wt(i),v=r&&Kt(r)?wt(r):r;let w=d,h=_u(w);for(;h&&r&&v!==w;){const j=ks(h),g=h.getBoundingClientRect(),x=Gt(h),y=g.left+(h.clientLeft+parseFloat(x.paddingLeft))*j.x,N=g.top+(h.clientTop+parseFloat(x.paddingTop))*j.y;c*=j.x,u*=j.y,p*=j.x,m*=j.y,c+=y,u+=N,w=wt(h),h=_u(w)}}return Ha({width:p,height:m,x:c,y:u})}function C2(e){let{elements:t,rect:n,offsetParent:r,strategy:s}=e;const i=s==="fixed",a=un(r),l=t?Sl(t.floating):!1;if(r===a||l&&i)return n;let c={scrollLeft:0,scrollTop:0},u=ur(1);const p=ur(0),m=an(r);if((m||!m&&!i)&&((ro(r)!=="body"||wi(a))&&(c=Cl(r)),an(r))){const d=Br(r);u=ks(r),p.x=d.x+r.clientLeft,p.y=d.y+r.clientTop}return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-c.scrollLeft*u.x+p.x,y:n.y*u.y-c.scrollTop*u.y+p.y}}function E2(e){return Array.from(e.getClientRects())}function Ou(e,t){const n=Cl(e).scrollLeft;return t?t.left+n:Br(un(e)).left+n}function k2(e){const t=un(e),n=Cl(e),r=e.ownerDocument.body,s=gt(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=gt(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let a=-n.scrollLeft+Ou(e);const l=-n.scrollTop;return Gt(r).direction==="rtl"&&(a+=gt(t.clientWidth,r.clientWidth)-s),{width:s,height:i,x:a,y:l}}function P2(e,t){const n=wt(e),r=un(e),s=n.visualViewport;let i=r.clientWidth,a=r.clientHeight,l=0,c=0;if(s){i=s.width,a=s.height;const u=of();(!u||u&&t==="fixed")&&(l=s.offsetLeft,c=s.offsetTop)}return{width:i,height:a,x:l,y:c}}function T2(e,t){const n=Br(e,!0,t==="fixed"),r=n.top+e.clientTop,s=n.left+e.clientLeft,i=an(e)?ks(e):ur(1),a=e.clientWidth*i.x,l=e.clientHeight*i.y,c=s*i.x,u=r*i.y;return{width:a,height:l,x:c,y:u}}function mp(e,t,n){let r;if(t==="viewport")r=P2(e,n);else if(t==="document")r=k2(un(e));else if(Kt(t))r=T2(t,n);else{const s=lv(e);r={...t,x:t.x-s.x,y:t.y-s.y}}return Ha(r)}function cv(e,t){const n=fr(e);return n===t||!Kt(n)||Qs(n)?!1:Gt(n).position==="fixed"||cv(n,t)}function R2(e,t){const n=t.get(e);if(n)return n;let r=ri(e,[],!1).filter(l=>Kt(l)&&ro(l)!=="body"),s=null;const i=Gt(e).position==="fixed";let a=i?fr(e):e;for(;Kt(a)&&!Qs(a);){const l=Gt(a),c=sf(a);!c&&l.position==="fixed"&&(s=null),(i?!c&&!s:!c&&l.position==="static"&&!!s&&["absolute","fixed"].includes(s.position)||wi(a)&&!c&&cv(e,a))?r=r.filter(p=>p!==a):s=l,a=fr(a)}return t.set(e,r),r}function M2(e){let{element:t,boundary:n,rootBoundary:r,strategy:s}=e;const a=[...n==="clippingAncestors"?Sl(t)?[]:R2(t,this._c):[].concat(n),r],l=a[0],c=a.reduce((u,p)=>{const m=mp(t,p,s);return u.top=gt(m.top,u.top),u.right=cr(m.right,u.right),u.bottom=cr(m.bottom,u.bottom),u.left=gt(m.left,u.left),u},mp(t,l,s));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}}function A2(e){const{width:t,height:n}=av(e);return{width:t,height:n}}function I2(e,t,n){const r=an(t),s=un(t),i=n==="fixed",a=Br(e,!0,i,t);let l={scrollLeft:0,scrollTop:0};const c=ur(0);if(r||!r&&!i)if((ro(t)!=="body"||wi(s))&&(l=Cl(t)),r){const v=Br(t,!0,i,t);c.x=v.x+t.clientLeft,c.y=v.y+t.clientTop}else s&&(c.x=Ou(s));let u=0,p=0;if(s&&!r&&!i){const v=s.getBoundingClientRect();p=v.top+l.scrollTop,u=v.left+l.scrollLeft-Ou(s,v)}const m=a.left+l.scrollLeft-c.x-u,d=a.top+l.scrollTop-c.y-p;return{x:m,y:d,width:a.width,height:a.height}}function wc(e){return Gt(e).position==="static"}function pp(e,t){if(!an(e)||Gt(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return un(e)===n&&(n=n.ownerDocument.body),n}function uv(e,t){const n=wt(e);if(Sl(e))return n;if(!an(e)){let s=fr(e);for(;s&&!Qs(s);){if(Kt(s)&&!wc(s))return s;s=fr(s)}return n}let r=pp(e,t);for(;r&&j2(r)&&wc(r);)r=pp(r,t);return r&&Qs(r)&&wc(r)&&!sf(r)?n:r||N2(e)||n}const _2=async function(e){const t=this.getOffsetParent||uv,n=this.getDimensions,r=await n(e.floating);return{reference:I2(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function O2(e){return Gt(e).direction==="rtl"}const D2={convertOffsetParentRelativeRectToViewportRelativeRect:C2,getDocumentElement:un,getClippingRect:M2,getOffsetParent:uv,getElementRects:_2,getClientRects:E2,getDimensions:A2,getScale:ks,isElement:Kt,isRTL:O2};function L2(e,t){let n=null,r;const s=un(e);function i(){var l;clearTimeout(r),(l=n)==null||l.disconnect(),n=null}function a(l,c){l===void 0&&(l=!1),c===void 0&&(c=1),i();const{left:u,top:p,width:m,height:d}=e.getBoundingClientRect();if(l||t(),!m||!d)return;const v=Gi(p),w=Gi(s.clientWidth-(u+m)),h=Gi(s.clientHeight-(p+d)),j=Gi(u),x={rootMargin:-v+"px "+-w+"px "+-h+"px "+-j+"px",threshold:gt(0,cr(1,c))||1};let y=!0;function N(b){const E=b[0].intersectionRatio;if(E!==c){if(!y)return a();E?a(!1,E):r=setTimeout(()=>{a(!1,1e-7)},1e3)}y=!1}try{n=new IntersectionObserver(N,{...x,root:s.ownerDocument})}catch{n=new IntersectionObserver(N,x)}n.observe(e)}return a(!0),i}function F2(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:s=!0,ancestorResize:i=!0,elementResize:a=typeof ResizeObserver=="function",layoutShift:l=typeof IntersectionObserver=="function",animationFrame:c=!1}=r,u=af(e),p=s||i?[...u?ri(u):[],...ri(t)]:[];p.forEach(g=>{s&&g.addEventListener("scroll",n,{passive:!0}),i&&g.addEventListener("resize",n)});const m=u&&l?L2(u,n):null;let d=-1,v=null;a&&(v=new ResizeObserver(g=>{let[x]=g;x&&x.target===u&&v&&(v.unobserve(t),cancelAnimationFrame(d),d=requestAnimationFrame(()=>{var y;(y=v)==null||y.observe(t)})),n()}),u&&!c&&v.observe(u),v.observe(t));let w,h=c?Br(e):null;c&&j();function j(){const g=Br(e);h&&(g.x!==h.x||g.y!==h.y||g.width!==h.width||g.height!==h.height)&&n(),h=g,w=requestAnimationFrame(j)}return n(),()=>{var g;p.forEach(x=>{s&&x.removeEventListener("scroll",n),i&&x.removeEventListener("resize",n)}),m==null||m(),(g=v)==null||g.disconnect(),v=null,c&&cancelAnimationFrame(w)}}const z2=g2,$2=v2,U2=p2,B2=w2,V2=h2,hp=m2,H2=y2,W2=(e,t,n)=>{const r=new Map,s={platform:D2,...n},i={...s.platform,_c:r};return f2(e,t,{...s,platform:i})};var ma=typeof document<"u"?f.useLayoutEffect:f.useEffect;function Wa(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,s;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!Wa(e[r],t[r]))return!1;return!0}if(s=Object.keys(e),n=s.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,s[r]))return!1;for(r=n;r--!==0;){const i=s[r];if(!(i==="_owner"&&e.$$typeof)&&!Wa(e[i],t[i]))return!1}return!0}return e!==e&&t!==t}function dv(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function xp(e,t){const n=dv(e);return Math.round(t*n)/n}function jc(e){const t=f.useRef(e);return ma(()=>{t.current=e}),t}function K2(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:s,elements:{reference:i,floating:a}={},transform:l=!0,whileElementsMounted:c,open:u}=e,[p,m]=f.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[d,v]=f.useState(r);Wa(d,r)||v(r);const[w,h]=f.useState(null),[j,g]=f.useState(null),x=f.useCallback(S=>{S!==E.current&&(E.current=S,h(S))},[]),y=f.useCallback(S=>{S!==C.current&&(C.current=S,g(S))},[]),N=i||w,b=a||j,E=f.useRef(null),C=f.useRef(null),k=f.useRef(p),I=c!=null,M=jc(c),U=jc(s),D=jc(u),W=f.useCallback(()=>{if(!E.current||!C.current)return;const S={placement:t,strategy:n,middleware:d};U.current&&(S.platform=U.current),W2(E.current,C.current,S).then(P=>{const L={...P,isPositioned:D.current!==!1};A.current&&!Wa(k.current,L)&&(k.current=L,Gr.flushSync(()=>{m(L)}))})},[d,t,n,U,D]);ma(()=>{u===!1&&k.current.isPositioned&&(k.current.isPositioned=!1,m(S=>({...S,isPositioned:!1})))},[u]);const A=f.useRef(!1);ma(()=>(A.current=!0,()=>{A.current=!1}),[]),ma(()=>{if(N&&(E.current=N),b&&(C.current=b),N&&b){if(M.current)return M.current(N,b,W);W()}},[N,b,W,M,I]);const K=f.useMemo(()=>({reference:E,floating:C,setReference:x,setFloating:y}),[x,y]),F=f.useMemo(()=>({reference:N,floating:b}),[N,b]),G=f.useMemo(()=>{const S={position:n,left:0,top:0};if(!F.floating)return S;const P=xp(F.floating,p.x),L=xp(F.floating,p.y);return l?{...S,transform:"translate("+P+"px, "+L+"px)",...dv(F.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:P,top:L}},[n,l,F.floating,p.x,p.y]);return f.useMemo(()=>({...p,update:W,refs:K,elements:F,floatingStyles:G}),[p,W,K,F,G])}const G2=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:s}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?hp({element:r.current,padding:s}).fn(n):{}:r?hp({element:r,padding:s}).fn(n):{}}}},Q2=(e,t)=>({...z2(e),options:[e,t]}),Y2=(e,t)=>({...$2(e),options:[e,t]}),q2=(e,t)=>({...H2(e),options:[e,t]}),X2=(e,t)=>({...U2(e),options:[e,t]}),J2=(e,t)=>({...B2(e),options:[e,t]}),Z2=(e,t)=>({...V2(e),options:[e,t]}),eS=(e,t)=>({...G2(e),options:[e,t]});var tS="Arrow",fv=f.forwardRef((e,t)=>{const{children:n,width:r=10,height:s=5,...i}=e;return o.jsx(Y.svg,{...i,ref:t,width:r,height:s,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:o.jsx("polygon",{points:"0,0 30,0 15,10"})})});fv.displayName=tS;var nS=fv;function rS(e,t=[]){let n=[];function r(i,a){const l=f.createContext(a),c=n.length;n=[...n,a];function u(m){const{scope:d,children:v,...w}=m,h=(d==null?void 0:d[e][c])||l,j=f.useMemo(()=>w,Object.values(w));return o.jsx(h.Provider,{value:j,children:v})}function p(m,d){const v=(d==null?void 0:d[e][c])||l,w=f.useContext(v);if(w)return w;if(a!==void 0)return a;throw new Error(`\`${m}\` must be used within \`${i}\``)}return u.displayName=i+"Provider",[u,p]}const s=()=>{const i=n.map(a=>f.createContext(a));return function(l){const c=(l==null?void 0:l[e])||i;return f.useMemo(()=>({[`__scope${e}`]:{...l,[e]:c}}),[l,c])}};return s.scopeName=e,[r,sS(s,...t)]}function sS(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(s=>({useScope:s(),scopeName:s.scopeName}));return function(i){const a=r.reduce((l,{useScope:c,scopeName:u})=>{const m=c(i)[`__scope${u}`];return{...l,...m}},{});return f.useMemo(()=>({[`__scope${t.scopeName}`]:a}),[a])}};return n.scopeName=t.scopeName,n}function oS(e){const[t,n]=f.useState(void 0);return Je(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(s=>{if(!Array.isArray(s)||!s.length)return;const i=s[0];let a,l;if("borderBoxSize"in i){const c=i.borderBoxSize,u=Array.isArray(c)?c[0]:c;a=u.inlineSize,l=u.blockSize}else a=e.offsetWidth,l=e.offsetHeight;n({width:a,height:l})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var lf="Popper",[mv,so]=rS(lf),[iS,pv]=mv(lf),hv=e=>{const{__scopePopper:t,children:n}=e,[r,s]=f.useState(null);return o.jsx(iS,{scope:t,anchor:r,onAnchorChange:s,children:n})};hv.displayName=lf;var xv="PopperAnchor",gv=f.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...s}=e,i=pv(xv,n),a=f.useRef(null),l=me(t,a);return f.useEffect(()=>{i.onAnchorChange((r==null?void 0:r.current)||a.current)}),r?null:o.jsx(Y.div,{...s,ref:l})});gv.displayName=xv;var cf="PopperContent",[aS,lS]=mv(cf),vv=f.forwardRef((e,t)=>{var H,se,ie,ne,ue,ae;const{__scopePopper:n,side:r="bottom",sideOffset:s=0,align:i="center",alignOffset:a=0,arrowPadding:l=0,avoidCollisions:c=!0,collisionBoundary:u=[],collisionPadding:p=0,sticky:m="partial",hideWhenDetached:d=!1,updatePositionStrategy:v="optimized",onPlaced:w,...h}=e,j=pv(cf,n),[g,x]=f.useState(null),y=me(t,Ze=>x(Ze)),[N,b]=f.useState(null),E=oS(N),C=(E==null?void 0:E.width)??0,k=(E==null?void 0:E.height)??0,I=r+(i!=="center"?"-"+i:""),M=typeof p=="number"?p:{top:0,right:0,bottom:0,left:0,...p},U=Array.isArray(u)?u:[u],D=U.length>0,W={padding:M,boundary:U.filter(uS),altBoundary:D},{refs:A,floatingStyles:K,placement:F,isPositioned:G,middlewareData:S}=K2({strategy:"fixed",placement:I,whileElementsMounted:(...Ze)=>F2(...Ze,{animationFrame:v==="always"}),elements:{reference:j.anchor},middleware:[Q2({mainAxis:s+k,alignmentAxis:a}),c&&Y2({mainAxis:!0,crossAxis:!1,limiter:m==="partial"?q2():void 0,...W}),c&&X2({...W}),J2({...W,apply:({elements:Ze,rects:mt,availableWidth:An,availableHeight:St})=>{const{width:jr,height:$l}=mt.reference,$e=Ze.floating.style;$e.setProperty("--radix-popper-available-width",`${An}px`),$e.setProperty("--radix-popper-available-height",`${St}px`),$e.setProperty("--radix-popper-anchor-width",`${jr}px`),$e.setProperty("--radix-popper-anchor-height",`${$l}px`)}}),N&&eS({element:N,padding:l}),dS({arrowWidth:C,arrowHeight:k}),d&&Z2({strategy:"referenceHidden",...W})]}),[P,L]=jv(F),_=Xe(w);Je(()=>{G&&(_==null||_())},[G,_]);const B=(H=S.arrow)==null?void 0:H.x,Q=(se=S.arrow)==null?void 0:se.y,re=((ie=S.arrow)==null?void 0:ie.centerOffset)!==0,[Pe,q]=f.useState();return Je(()=>{g&&q(window.getComputedStyle(g).zIndex)},[g]),o.jsx("div",{ref:A.setFloating,"data-radix-popper-content-wrapper":"",style:{...K,transform:G?K.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Pe,"--radix-popper-transform-origin":[(ne=S.transformOrigin)==null?void 0:ne.x,(ue=S.transformOrigin)==null?void 0:ue.y].join(" "),...((ae=S.hide)==null?void 0:ae.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:o.jsx(aS,{scope:n,placedSide:P,onArrowChange:b,arrowX:B,arrowY:Q,shouldHideArrow:re,children:o.jsx(Y.div,{"data-side":P,"data-align":L,...h,ref:y,style:{...h.style,animation:G?void 0:"none"}})})})});vv.displayName=cf;var yv="PopperArrow",cS={top:"bottom",right:"left",bottom:"top",left:"right"},wv=f.forwardRef(function(t,n){const{__scopePopper:r,...s}=t,i=lS(yv,r),a=cS[i.placedSide];return o.jsx("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[a]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:o.jsx(nS,{...s,ref:n,style:{...s.style,display:"block"}})})});wv.displayName=yv;function uS(e){return e!==null}var dS=e=>({name:"transformOrigin",options:e,fn(t){var j,g,x;const{placement:n,rects:r,middlewareData:s}=t,a=((j=s.arrow)==null?void 0:j.centerOffset)!==0,l=a?0:e.arrowWidth,c=a?0:e.arrowHeight,[u,p]=jv(n),m={start:"0%",center:"50%",end:"100%"}[p],d=(((g=s.arrow)==null?void 0:g.x)??0)+l/2,v=(((x=s.arrow)==null?void 0:x.y)??0)+c/2;let w="",h="";return u==="bottom"?(w=a?m:`${d}px`,h=`${-c}px`):u==="top"?(w=a?m:`${d}px`,h=`${r.floating.height+c}px`):u==="right"?(w=`${-c}px`,h=a?m:`${v}px`):u==="left"&&(w=`${r.floating.width+c}px`,h=a?m:`${v}px`),{data:{x:w,y:h}}}});function jv(e){const[t,n="center"]=e.split("-");return[t,n]}var Nv=hv,uf=gv,df=vv,ff=wv,[El,D5]=eo("Tooltip",[so]),mf=so(),bv="TooltipProvider",fS=700,gp="tooltip.open",[mS,Sv]=El(bv),Cv=e=>{const{__scopeTooltip:t,delayDuration:n=fS,skipDelayDuration:r=300,disableHoverableContent:s=!1,children:i}=e,[a,l]=f.useState(!0),c=f.useRef(!1),u=f.useRef(0);return f.useEffect(()=>{const p=u.current;return()=>window.clearTimeout(p)},[]),o.jsx(mS,{scope:t,isOpenDelayed:a,delayDuration:n,onOpen:f.useCallback(()=>{window.clearTimeout(u.current),l(!1)},[]),onClose:f.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>l(!0),r)},[r]),isPointerInTransitRef:c,onPointerInTransitChange:f.useCallback(p=>{c.current=p},[]),disableHoverableContent:s,children:i})};Cv.displayName=bv;var Ev="Tooltip",[L5,kl]=El(Ev),Du="TooltipTrigger",pS=f.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,s=kl(Du,n),i=Sv(Du,n),a=mf(n),l=f.useRef(null),c=me(t,l,s.onTriggerChange),u=f.useRef(!1),p=f.useRef(!1),m=f.useCallback(()=>u.current=!1,[]);return f.useEffect(()=>()=>document.removeEventListener("pointerup",m),[m]),o.jsx(uf,{asChild:!0,...a,children:o.jsx(Y.button,{"aria-describedby":s.open?s.contentId:void 0,"data-state":s.stateAttribute,...r,ref:c,onPointerMove:z(e.onPointerMove,d=>{d.pointerType!=="touch"&&!p.current&&!i.isPointerInTransitRef.current&&(s.onTriggerEnter(),p.current=!0)}),onPointerLeave:z(e.onPointerLeave,()=>{s.onTriggerLeave(),p.current=!1}),onPointerDown:z(e.onPointerDown,()=>{u.current=!0,document.addEventListener("pointerup",m,{once:!0})}),onFocus:z(e.onFocus,()=>{u.current||s.onOpen()}),onBlur:z(e.onBlur,s.onClose),onClick:z(e.onClick,s.onClose)})})});pS.displayName=Du;var hS="TooltipPortal",[F5,xS]=El(hS,{forceMount:void 0}),Ys="TooltipContent",kv=f.forwardRef((e,t)=>{const n=xS(Ys,e.__scopeTooltip),{forceMount:r=n.forceMount,side:s="top",...i}=e,a=kl(Ys,e.__scopeTooltip);return o.jsx(xr,{present:r||a.open,children:a.disableHoverableContent?o.jsx(Pv,{side:s,...i,ref:t}):o.jsx(gS,{side:s,...i,ref:t})})}),gS=f.forwardRef((e,t)=>{const n=kl(Ys,e.__scopeTooltip),r=Sv(Ys,e.__scopeTooltip),s=f.useRef(null),i=me(t,s),[a,l]=f.useState(null),{trigger:c,onClose:u}=n,p=s.current,{onPointerInTransitChange:m}=r,d=f.useCallback(()=>{l(null),m(!1)},[m]),v=f.useCallback((w,h)=>{const j=w.currentTarget,g={x:w.clientX,y:w.clientY},x=jS(g,j.getBoundingClientRect()),y=NS(g,x),N=bS(h.getBoundingClientRect()),b=CS([...y,...N]);l(b),m(!0)},[m]);return f.useEffect(()=>()=>d(),[d]),f.useEffect(()=>{if(c&&p){const w=j=>v(j,p),h=j=>v(j,c);return c.addEventListener("pointerleave",w),p.addEventListener("pointerleave",h),()=>{c.removeEventListener("pointerleave",w),p.removeEventListener("pointerleave",h)}}},[c,p,v,d]),f.useEffect(()=>{if(a){const w=h=>{const j=h.target,g={x:h.clientX,y:h.clientY},x=(c==null?void 0:c.contains(j))||(p==null?void 0:p.contains(j)),y=!SS(g,a);x?d():y&&(d(),u())};return document.addEventListener("pointermove",w),()=>document.removeEventListener("pointermove",w)}},[c,p,a,u,d]),o.jsx(Pv,{...e,ref:i})}),[vS,yS]=El(Ev,{isInside:!1}),Pv=f.forwardRef((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":s,onEscapeKeyDown:i,onPointerDownOutside:a,...l}=e,c=kl(Ys,n),u=mf(n),{onClose:p}=c;return f.useEffect(()=>(document.addEventListener(gp,p),()=>document.removeEventListener(gp,p)),[p]),f.useEffect(()=>{if(c.trigger){const m=d=>{const v=d.target;v!=null&&v.contains(c.trigger)&&p()};return window.addEventListener("scroll",m,{capture:!0}),()=>window.removeEventListener("scroll",m,{capture:!0})}},[c.trigger,p]),o.jsx(vi,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:a,onFocusOutside:m=>m.preventDefault(),onDismiss:p,children:o.jsxs(df,{"data-state":c.stateAttribute,...u,...l,ref:t,style:{...l.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[o.jsx(vg,{children:r}),o.jsx(vS,{scope:n,isInside:!0,children:o.jsx(yN,{id:c.contentId,role:"tooltip",children:s||r})})]})})});kv.displayName=Ys;var Tv="TooltipArrow",wS=f.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,s=mf(n);return yS(Tv,n).isInside?null:o.jsx(ff,{...s,...r,ref:t})});wS.displayName=Tv;function jS(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),s=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,s,i)){case i:return"left";case s:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function NS(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function bS(e){const{top:t,right:n,bottom:r,left:s}=e;return[{x:s,y:t},{x:n,y:t},{x:n,y:r},{x:s,y:r}]}function SS(e,t){const{x:n,y:r}=e;let s=!1;for(let i=0,a=t.length-1;i<t.length;a=i++){const l=t[i].x,c=t[i].y,u=t[a].x,p=t[a].y;c>r!=p>r&&n<(u-l)*(r-c)/(p-c)+l&&(s=!s)}return s}function CS(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),ES(t)}function ES(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const s=e[r];for(;t.length>=2;){const i=t[t.length-1],a=t[t.length-2];if((i.x-a.x)*(s.y-a.y)>=(i.y-a.y)*(s.x-a.x))t.pop();else break}t.push(s)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const s=e[r];for(;n.length>=2;){const i=n[n.length-1],a=n[n.length-2];if((i.x-a.x)*(s.y-a.y)>=(i.y-a.y)*(s.x-a.x))n.pop();else break}n.push(s)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var kS=Cv,Rv=kv;const PS=kS,TS=f.forwardRef(({className:e,sideOffset:t=4,...n},r)=>o.jsx(Rv,{ref:r,sideOffset:t,className:ee("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n}));TS.displayName=Rv.displayName;var Pl=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},Tl=typeof window>"u"||"Deno"in globalThis;function Ft(){}function RS(e,t){return typeof e=="function"?e(t):e}function MS(e){return typeof e=="number"&&e>=0&&e!==1/0}function AS(e,t){return Math.max(e+(t||0)-Date.now(),0)}function vp(e,t){return typeof e=="function"?e(t):e}function IS(e,t){return typeof e=="function"?e(t):e}function yp(e,t){const{type:n="all",exact:r,fetchStatus:s,predicate:i,queryKey:a,stale:l}=e;if(a){if(r){if(t.queryHash!==pf(a,t.options))return!1}else if(!oi(t.queryKey,a))return!1}if(n!=="all"){const c=t.isActive();if(n==="active"&&!c||n==="inactive"&&c)return!1}return!(typeof l=="boolean"&&t.isStale()!==l||s&&s!==t.state.fetchStatus||i&&!i(t))}function wp(e,t){const{exact:n,status:r,predicate:s,mutationKey:i}=e;if(i){if(!t.options.mutationKey)return!1;if(n){if(si(t.options.mutationKey)!==si(i))return!1}else if(!oi(t.options.mutationKey,i))return!1}return!(r&&t.state.status!==r||s&&!s(t))}function pf(e,t){return((t==null?void 0:t.queryKeyHashFn)||si)(e)}function si(e){return JSON.stringify(e,(t,n)=>Lu(n)?Object.keys(n).sort().reduce((r,s)=>(r[s]=n[s],r),{}):n)}function oi(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?!Object.keys(t).some(n=>!oi(e[n],t[n])):!1}function Mv(e,t){if(e===t)return e;const n=jp(e)&&jp(t);if(n||Lu(e)&&Lu(t)){const r=n?e:Object.keys(e),s=r.length,i=n?t:Object.keys(t),a=i.length,l=n?[]:{};let c=0;for(let u=0;u<a;u++){const p=n?u:i[u];(!n&&r.includes(p)||n)&&e[p]===void 0&&t[p]===void 0?(l[p]=void 0,c++):(l[p]=Mv(e[p],t[p]),l[p]===e[p]&&e[p]!==void 0&&c++)}return s===a&&c===s?e:l}return t}function jp(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function Lu(e){if(!Np(e))return!1;const t=e.constructor;if(t===void 0)return!0;const n=t.prototype;return!(!Np(n)||!n.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function Np(e){return Object.prototype.toString.call(e)==="[object Object]"}function _S(e){return new Promise(t=>{setTimeout(t,e)})}function OS(e,t,n){return typeof n.structuralSharing=="function"?n.structuralSharing(e,t):n.structuralSharing!==!1?Mv(e,t):t}function DS(e,t,n=0){const r=[...e,t];return n&&r.length>n?r.slice(1):r}function LS(e,t,n=0){const r=[t,...e];return n&&r.length>n?r.slice(0,-1):r}var hf=Symbol();function Av(e,t){return!e.queryFn&&(t!=null&&t.initialPromise)?()=>t.initialPromise:!e.queryFn||e.queryFn===hf?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}var Tr,Vn,Rs,Qp,FS=(Qp=class extends Pl{constructor(){super();de(this,Tr);de(this,Vn);de(this,Rs);Z(this,Rs,t=>{if(!Tl&&window.addEventListener){const n=()=>t();return window.addEventListener("visibilitychange",n,!1),()=>{window.removeEventListener("visibilitychange",n)}}})}onSubscribe(){T(this,Vn)||this.setEventListener(T(this,Rs))}onUnsubscribe(){var t;this.hasListeners()||((t=T(this,Vn))==null||t.call(this),Z(this,Vn,void 0))}setEventListener(t){var n;Z(this,Rs,t),(n=T(this,Vn))==null||n.call(this),Z(this,Vn,t(r=>{typeof r=="boolean"?this.setFocused(r):this.onFocus()}))}setFocused(t){T(this,Tr)!==t&&(Z(this,Tr,t),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(n=>{n(t)})}isFocused(){var t;return typeof T(this,Tr)=="boolean"?T(this,Tr):((t=globalThis.document)==null?void 0:t.visibilityState)!=="hidden"}},Tr=new WeakMap,Vn=new WeakMap,Rs=new WeakMap,Qp),Iv=new FS,Ms,Hn,As,Yp,zS=(Yp=class extends Pl{constructor(){super();de(this,Ms,!0);de(this,Hn);de(this,As);Z(this,As,t=>{if(!Tl&&window.addEventListener){const n=()=>t(!0),r=()=>t(!1);return window.addEventListener("online",n,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",n),window.removeEventListener("offline",r)}}})}onSubscribe(){T(this,Hn)||this.setEventListener(T(this,As))}onUnsubscribe(){var t;this.hasListeners()||((t=T(this,Hn))==null||t.call(this),Z(this,Hn,void 0))}setEventListener(t){var n;Z(this,As,t),(n=T(this,Hn))==null||n.call(this),Z(this,Hn,t(this.setOnline.bind(this)))}setOnline(t){T(this,Ms)!==t&&(Z(this,Ms,t),this.listeners.forEach(r=>{r(t)}))}isOnline(){return T(this,Ms)}},Ms=new WeakMap,Hn=new WeakMap,As=new WeakMap,Yp),Ka=new zS;function $S(){let e,t;const n=new Promise((s,i)=>{e=s,t=i});n.status="pending",n.catch(()=>{});function r(s){Object.assign(n,s),delete n.resolve,delete n.reject}return n.resolve=s=>{r({status:"fulfilled",value:s}),e(s)},n.reject=s=>{r({status:"rejected",reason:s}),t(s)},n}function US(e){return Math.min(1e3*2**e,3e4)}function _v(e){return(e??"online")==="online"?Ka.isOnline():!0}var Ov=class extends Error{constructor(e){super("CancelledError"),this.revert=e==null?void 0:e.revert,this.silent=e==null?void 0:e.silent}};function Nc(e){return e instanceof Ov}function Dv(e){let t=!1,n=0,r=!1,s;const i=$S(),a=h=>{var j;r||(d(new Ov(h)),(j=e.abort)==null||j.call(e))},l=()=>{t=!0},c=()=>{t=!1},u=()=>Iv.isFocused()&&(e.networkMode==="always"||Ka.isOnline())&&e.canRun(),p=()=>_v(e.networkMode)&&e.canRun(),m=h=>{var j;r||(r=!0,(j=e.onSuccess)==null||j.call(e,h),s==null||s(),i.resolve(h))},d=h=>{var j;r||(r=!0,(j=e.onError)==null||j.call(e,h),s==null||s(),i.reject(h))},v=()=>new Promise(h=>{var j;s=g=>{(r||u())&&h(g)},(j=e.onPause)==null||j.call(e)}).then(()=>{var h;s=void 0,r||(h=e.onContinue)==null||h.call(e)}),w=()=>{if(r)return;let h;const j=n===0?e.initialPromise:void 0;try{h=j??e.fn()}catch(g){h=Promise.reject(g)}Promise.resolve(h).then(m).catch(g=>{var E;if(r)return;const x=e.retry??(Tl?0:3),y=e.retryDelay??US,N=typeof y=="function"?y(n,g):y,b=x===!0||typeof x=="number"&&n<x||typeof x=="function"&&x(n,g);if(t||!b){d(g);return}n++,(E=e.onFail)==null||E.call(e,n,g),_S(N).then(()=>u()?void 0:v()).then(()=>{t?d(g):w()})})};return{promise:i,cancel:a,continue:()=>(s==null||s(),i),cancelRetry:l,continueRetry:c,canStart:p,start:()=>(p()?w():v().then(w),i)}}function BS(){let e=[],t=0,n=l=>{l()},r=l=>{l()},s=l=>setTimeout(l,0);const i=l=>{t?e.push(l):s(()=>{n(l)})},a=()=>{const l=e;e=[],l.length&&s(()=>{r(()=>{l.forEach(c=>{n(c)})})})};return{batch:l=>{let c;t++;try{c=l()}finally{t--,t||a()}return c},batchCalls:l=>(...c)=>{i(()=>{l(...c)})},schedule:i,setNotifyFunction:l=>{n=l},setBatchNotifyFunction:l=>{r=l},setScheduler:l=>{s=l}}}var nt=BS(),Rr,qp,Lv=(qp=class{constructor(){de(this,Rr)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),MS(this.gcTime)&&Z(this,Rr,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(Tl?1/0:5*60*1e3))}clearGcTimeout(){T(this,Rr)&&(clearTimeout(T(this,Rr)),Z(this,Rr,void 0))}},Rr=new WeakMap,qp),Is,_s,Ct,Qe,di,Mr,zt,mn,Xp,VS=(Xp=class extends Lv{constructor(t){super();de(this,zt);de(this,Is);de(this,_s);de(this,Ct);de(this,Qe);de(this,di);de(this,Mr);Z(this,Mr,!1),Z(this,di,t.defaultOptions),this.setOptions(t.options),this.observers=[],Z(this,Ct,t.cache),this.queryKey=t.queryKey,this.queryHash=t.queryHash,Z(this,Is,WS(this.options)),this.state=t.state??T(this,Is),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return(t=T(this,Qe))==null?void 0:t.promise}setOptions(t){this.options={...T(this,di),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&T(this,Ct).remove(this)}setData(t,n){const r=OS(this.state.data,t,this.options);return We(this,zt,mn).call(this,{data:r,type:"success",dataUpdatedAt:n==null?void 0:n.updatedAt,manual:n==null?void 0:n.manual}),r}setState(t,n){We(this,zt,mn).call(this,{type:"setState",state:t,setStateOptions:n})}cancel(t){var r,s;const n=(r=T(this,Qe))==null?void 0:r.promise;return(s=T(this,Qe))==null||s.cancel(t),n?n.then(Ft).catch(Ft):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(T(this,Is))}isActive(){return this.observers.some(t=>IS(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===hf||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return this.state.isInvalidated?!0:this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0}isStaleByTime(t=0){return this.state.isInvalidated||this.state.data===void 0||!AS(this.state.dataUpdatedAt,t)}onFocus(){var n;const t=this.observers.find(r=>r.shouldFetchOnWindowFocus());t==null||t.refetch({cancelRefetch:!1}),(n=T(this,Qe))==null||n.continue()}onOnline(){var n;const t=this.observers.find(r=>r.shouldFetchOnReconnect());t==null||t.refetch({cancelRefetch:!1}),(n=T(this,Qe))==null||n.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),T(this,Ct).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(n=>n!==t),this.observers.length||(T(this,Qe)&&(T(this,Mr)?T(this,Qe).cancel({revert:!0}):T(this,Qe).cancelRetry()),this.scheduleGc()),T(this,Ct).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||We(this,zt,mn).call(this,{type:"invalidate"})}fetch(t,n){var c,u,p;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(n!=null&&n.cancelRefetch))this.cancel({silent:!0});else if(T(this,Qe))return T(this,Qe).continueRetry(),T(this,Qe).promise}if(t&&this.setOptions(t),!this.options.queryFn){const m=this.observers.find(d=>d.options.queryFn);m&&this.setOptions(m.options)}const r=new AbortController,s=m=>{Object.defineProperty(m,"signal",{enumerable:!0,get:()=>(Z(this,Mr,!0),r.signal)})},i=()=>{const m=Av(this.options,n),d={queryKey:this.queryKey,meta:this.meta};return s(d),Z(this,Mr,!1),this.options.persister?this.options.persister(m,d,this):m(d)},a={fetchOptions:n,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:i};s(a),(c=this.options.behavior)==null||c.onFetch(a,this),Z(this,_s,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((u=a.fetchOptions)==null?void 0:u.meta))&&We(this,zt,mn).call(this,{type:"fetch",meta:(p=a.fetchOptions)==null?void 0:p.meta});const l=m=>{var d,v,w,h;Nc(m)&&m.silent||We(this,zt,mn).call(this,{type:"error",error:m}),Nc(m)||((v=(d=T(this,Ct).config).onError)==null||v.call(d,m,this),(h=(w=T(this,Ct).config).onSettled)==null||h.call(w,this.state.data,m,this)),this.scheduleGc()};return Z(this,Qe,Dv({initialPromise:n==null?void 0:n.initialPromise,fn:a.fetchFn,abort:r.abort.bind(r),onSuccess:m=>{var d,v,w,h;if(m===void 0){l(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(m)}catch(j){l(j);return}(v=(d=T(this,Ct).config).onSuccess)==null||v.call(d,m,this),(h=(w=T(this,Ct).config).onSettled)==null||h.call(w,m,this.state.error,this),this.scheduleGc()},onError:l,onFail:(m,d)=>{We(this,zt,mn).call(this,{type:"failed",failureCount:m,error:d})},onPause:()=>{We(this,zt,mn).call(this,{type:"pause"})},onContinue:()=>{We(this,zt,mn).call(this,{type:"continue"})},retry:a.options.retry,retryDelay:a.options.retryDelay,networkMode:a.options.networkMode,canRun:()=>!0})),T(this,Qe).start()}},Is=new WeakMap,_s=new WeakMap,Ct=new WeakMap,Qe=new WeakMap,di=new WeakMap,Mr=new WeakMap,zt=new WeakSet,mn=function(t){const n=r=>{switch(t.type){case"failed":return{...r,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...r,fetchStatus:"paused"};case"continue":return{...r,fetchStatus:"fetching"};case"fetch":return{...r,...HS(r.data,this.options),fetchMeta:t.meta??null};case"success":return{...r,data:t.data,dataUpdateCount:r.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const s=t.error;return Nc(s)&&s.revert&&T(this,_s)?{...T(this,_s),fetchStatus:"idle"}:{...r,error:s,errorUpdateCount:r.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:r.fetchFailureCount+1,fetchFailureReason:s,fetchStatus:"idle",status:"error"};case"invalidate":return{...r,isInvalidated:!0};case"setState":return{...r,...t.state}}};this.state=n(this.state),nt.batch(()=>{this.observers.forEach(r=>{r.onQueryUpdate()}),T(this,Ct).notify({query:this,type:"updated",action:t})})},Xp);function HS(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:_v(t.networkMode)?"fetching":"paused",...e===void 0&&{error:null,status:"pending"}}}function WS(e){const t=typeof e.initialData=="function"?e.initialData():e.initialData,n=t!==void 0,r=n?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:n?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:n?"success":"pending",fetchStatus:"idle"}}var Zt,Jp,KS=(Jp=class extends Pl{constructor(t={}){super();de(this,Zt);this.config=t,Z(this,Zt,new Map)}build(t,n,r){const s=n.queryKey,i=n.queryHash??pf(s,n);let a=this.get(i);return a||(a=new VS({cache:this,queryKey:s,queryHash:i,options:t.defaultQueryOptions(n),state:r,defaultOptions:t.getQueryDefaults(s)}),this.add(a)),a}add(t){T(this,Zt).has(t.queryHash)||(T(this,Zt).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const n=T(this,Zt).get(t.queryHash);n&&(t.destroy(),n===t&&T(this,Zt).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){nt.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return T(this,Zt).get(t)}getAll(){return[...T(this,Zt).values()]}find(t){const n={exact:!0,...t};return this.getAll().find(r=>yp(n,r))}findAll(t={}){const n=this.getAll();return Object.keys(t).length>0?n.filter(r=>yp(t,r)):n}notify(t){nt.batch(()=>{this.listeners.forEach(n=>{n(t)})})}onFocus(){nt.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){nt.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},Zt=new WeakMap,Jp),en,et,Ar,tn,Ln,Zp,GS=(Zp=class extends Lv{constructor(t){super();de(this,tn);de(this,en);de(this,et);de(this,Ar);this.mutationId=t.mutationId,Z(this,et,t.mutationCache),Z(this,en,[]),this.state=t.state||QS(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){T(this,en).includes(t)||(T(this,en).push(t),this.clearGcTimeout(),T(this,et).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){Z(this,en,T(this,en).filter(n=>n!==t)),this.scheduleGc(),T(this,et).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){T(this,en).length||(this.state.status==="pending"?this.scheduleGc():T(this,et).remove(this))}continue(){var t;return((t=T(this,Ar))==null?void 0:t.continue())??this.execute(this.state.variables)}async execute(t){var s,i,a,l,c,u,p,m,d,v,w,h,j,g,x,y,N,b,E,C;Z(this,Ar,Dv({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(k,I)=>{We(this,tn,Ln).call(this,{type:"failed",failureCount:k,error:I})},onPause:()=>{We(this,tn,Ln).call(this,{type:"pause"})},onContinue:()=>{We(this,tn,Ln).call(this,{type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>T(this,et).canRun(this)}));const n=this.state.status==="pending",r=!T(this,Ar).canStart();try{if(!n){We(this,tn,Ln).call(this,{type:"pending",variables:t,isPaused:r}),await((i=(s=T(this,et).config).onMutate)==null?void 0:i.call(s,t,this));const I=await((l=(a=this.options).onMutate)==null?void 0:l.call(a,t));I!==this.state.context&&We(this,tn,Ln).call(this,{type:"pending",context:I,variables:t,isPaused:r})}const k=await T(this,Ar).start();return await((u=(c=T(this,et).config).onSuccess)==null?void 0:u.call(c,k,t,this.state.context,this)),await((m=(p=this.options).onSuccess)==null?void 0:m.call(p,k,t,this.state.context)),await((v=(d=T(this,et).config).onSettled)==null?void 0:v.call(d,k,null,this.state.variables,this.state.context,this)),await((h=(w=this.options).onSettled)==null?void 0:h.call(w,k,null,t,this.state.context)),We(this,tn,Ln).call(this,{type:"success",data:k}),k}catch(k){try{throw await((g=(j=T(this,et).config).onError)==null?void 0:g.call(j,k,t,this.state.context,this)),await((y=(x=this.options).onError)==null?void 0:y.call(x,k,t,this.state.context)),await((b=(N=T(this,et).config).onSettled)==null?void 0:b.call(N,void 0,k,this.state.variables,this.state.context,this)),await((C=(E=this.options).onSettled)==null?void 0:C.call(E,void 0,k,t,this.state.context)),k}finally{We(this,tn,Ln).call(this,{type:"error",error:k})}}finally{T(this,et).runNext(this)}}},en=new WeakMap,et=new WeakMap,Ar=new WeakMap,tn=new WeakSet,Ln=function(t){const n=r=>{switch(t.type){case"failed":return{...r,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...r,isPaused:!0};case"continue":return{...r,isPaused:!1};case"pending":return{...r,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...r,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...r,data:void 0,error:t.error,failureCount:r.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=n(this.state),nt.batch(()=>{T(this,en).forEach(r=>{r.onMutationUpdate(t)}),T(this,et).notify({mutation:this,type:"updated",action:t})})},Zp);function QS(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var pt,fi,eh,YS=(eh=class extends Pl{constructor(t={}){super();de(this,pt);de(this,fi);this.config=t,Z(this,pt,new Map),Z(this,fi,Date.now())}build(t,n,r){const s=new GS({mutationCache:this,mutationId:++Ei(this,fi)._,options:t.defaultMutationOptions(n),state:r});return this.add(s),s}add(t){const n=Qi(t),r=T(this,pt).get(n)??[];r.push(t),T(this,pt).set(n,r),this.notify({type:"added",mutation:t})}remove(t){var r;const n=Qi(t);if(T(this,pt).has(n)){const s=(r=T(this,pt).get(n))==null?void 0:r.filter(i=>i!==t);s&&(s.length===0?T(this,pt).delete(n):T(this,pt).set(n,s))}this.notify({type:"removed",mutation:t})}canRun(t){var r;const n=(r=T(this,pt).get(Qi(t)))==null?void 0:r.find(s=>s.state.status==="pending");return!n||n===t}runNext(t){var r;const n=(r=T(this,pt).get(Qi(t)))==null?void 0:r.find(s=>s!==t&&s.state.isPaused);return(n==null?void 0:n.continue())??Promise.resolve()}clear(){nt.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}getAll(){return[...T(this,pt).values()].flat()}find(t){const n={exact:!0,...t};return this.getAll().find(r=>wp(n,r))}findAll(t={}){return this.getAll().filter(n=>wp(t,n))}notify(t){nt.batch(()=>{this.listeners.forEach(n=>{n(t)})})}resumePausedMutations(){const t=this.getAll().filter(n=>n.state.isPaused);return nt.batch(()=>Promise.all(t.map(n=>n.continue().catch(Ft))))}},pt=new WeakMap,fi=new WeakMap,eh);function Qi(e){var t;return((t=e.options.scope)==null?void 0:t.id)??String(e.mutationId)}function bp(e){return{onFetch:(t,n)=>{var p,m,d,v,w;const r=t.options,s=(d=(m=(p=t.fetchOptions)==null?void 0:p.meta)==null?void 0:m.fetchMore)==null?void 0:d.direction,i=((v=t.state.data)==null?void 0:v.pages)||[],a=((w=t.state.data)==null?void 0:w.pageParams)||[];let l={pages:[],pageParams:[]},c=0;const u=async()=>{let h=!1;const j=y=>{Object.defineProperty(y,"signal",{enumerable:!0,get:()=>(t.signal.aborted?h=!0:t.signal.addEventListener("abort",()=>{h=!0}),t.signal)})},g=Av(t.options,t.fetchOptions),x=async(y,N,b)=>{if(h)return Promise.reject();if(N==null&&y.pages.length)return Promise.resolve(y);const E={queryKey:t.queryKey,pageParam:N,direction:b?"backward":"forward",meta:t.options.meta};j(E);const C=await g(E),{maxPages:k}=t.options,I=b?LS:DS;return{pages:I(y.pages,C,k),pageParams:I(y.pageParams,N,k)}};if(s&&i.length){const y=s==="backward",N=y?qS:Sp,b={pages:i,pageParams:a},E=N(r,b);l=await x(b,E,y)}else{const y=e??i.length;do{const N=c===0?a[0]??r.initialPageParam:Sp(r,l);if(c>0&&N==null)break;l=await x(l,N),c++}while(c<y)}return l};t.options.persister?t.fetchFn=()=>{var h,j;return(j=(h=t.options).persister)==null?void 0:j.call(h,u,{queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},n)}:t.fetchFn=u}}}function Sp(e,{pages:t,pageParams:n}){const r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,n[r],n):void 0}function qS(e,{pages:t,pageParams:n}){var r;return t.length>0?(r=e.getPreviousPageParam)==null?void 0:r.call(e,t[0],t,n[0],n):void 0}var Ee,Wn,Kn,Os,Ds,Gn,Ls,Fs,th,XS=(th=class{constructor(e={}){de(this,Ee);de(this,Wn);de(this,Kn);de(this,Os);de(this,Ds);de(this,Gn);de(this,Ls);de(this,Fs);Z(this,Ee,e.queryCache||new KS),Z(this,Wn,e.mutationCache||new YS),Z(this,Kn,e.defaultOptions||{}),Z(this,Os,new Map),Z(this,Ds,new Map),Z(this,Gn,0)}mount(){Ei(this,Gn)._++,T(this,Gn)===1&&(Z(this,Ls,Iv.subscribe(async e=>{e&&(await this.resumePausedMutations(),T(this,Ee).onFocus())})),Z(this,Fs,Ka.subscribe(async e=>{e&&(await this.resumePausedMutations(),T(this,Ee).onOnline())})))}unmount(){var e,t;Ei(this,Gn)._--,T(this,Gn)===0&&((e=T(this,Ls))==null||e.call(this),Z(this,Ls,void 0),(t=T(this,Fs))==null||t.call(this),Z(this,Fs,void 0))}isFetching(e){return T(this,Ee).findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return T(this,Wn).findAll({...e,status:"pending"}).length}getQueryData(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=T(this,Ee).get(t.queryHash))==null?void 0:n.state.data}ensureQueryData(e){const t=this.getQueryData(e.queryKey);if(t===void 0)return this.fetchQuery(e);{const n=this.defaultQueryOptions(e),r=T(this,Ee).build(this,n);return e.revalidateIfStale&&r.isStaleByTime(vp(n.staleTime,r))&&this.prefetchQuery(n),Promise.resolve(t)}}getQueriesData(e){return T(this,Ee).findAll(e).map(({queryKey:t,state:n})=>{const r=n.data;return[t,r]})}setQueryData(e,t,n){const r=this.defaultQueryOptions({queryKey:e}),s=T(this,Ee).get(r.queryHash),i=s==null?void 0:s.state.data,a=RS(t,i);if(a!==void 0)return T(this,Ee).build(this,r).setData(a,{...n,manual:!0})}setQueriesData(e,t,n){return nt.batch(()=>T(this,Ee).findAll(e).map(({queryKey:r})=>[r,this.setQueryData(r,t,n)]))}getQueryState(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=T(this,Ee).get(t.queryHash))==null?void 0:n.state}removeQueries(e){const t=T(this,Ee);nt.batch(()=>{t.findAll(e).forEach(n=>{t.remove(n)})})}resetQueries(e,t){const n=T(this,Ee),r={type:"active",...e};return nt.batch(()=>(n.findAll(e).forEach(s=>{s.reset()}),this.refetchQueries(r,t)))}cancelQueries(e={},t={}){const n={revert:!0,...t},r=nt.batch(()=>T(this,Ee).findAll(e).map(s=>s.cancel(n)));return Promise.all(r).then(Ft).catch(Ft)}invalidateQueries(e={},t={}){return nt.batch(()=>{if(T(this,Ee).findAll(e).forEach(r=>{r.invalidate()}),e.refetchType==="none")return Promise.resolve();const n={...e,type:e.refetchType??e.type??"active"};return this.refetchQueries(n,t)})}refetchQueries(e={},t){const n={...t,cancelRefetch:(t==null?void 0:t.cancelRefetch)??!0},r=nt.batch(()=>T(this,Ee).findAll(e).filter(s=>!s.isDisabled()).map(s=>{let i=s.fetch(void 0,n);return n.throwOnError||(i=i.catch(Ft)),s.state.fetchStatus==="paused"?Promise.resolve():i}));return Promise.all(r).then(Ft)}fetchQuery(e){const t=this.defaultQueryOptions(e);t.retry===void 0&&(t.retry=!1);const n=T(this,Ee).build(this,t);return n.isStaleByTime(vp(t.staleTime,n))?n.fetch(t):Promise.resolve(n.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(Ft).catch(Ft)}fetchInfiniteQuery(e){return e.behavior=bp(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(Ft).catch(Ft)}ensureInfiniteQueryData(e){return e.behavior=bp(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return Ka.isOnline()?T(this,Wn).resumePausedMutations():Promise.resolve()}getQueryCache(){return T(this,Ee)}getMutationCache(){return T(this,Wn)}getDefaultOptions(){return T(this,Kn)}setDefaultOptions(e){Z(this,Kn,e)}setQueryDefaults(e,t){T(this,Os).set(si(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...T(this,Os).values()];let n={};return t.forEach(r=>{oi(e,r.queryKey)&&(n={...n,...r.defaultOptions})}),n}setMutationDefaults(e,t){T(this,Ds).set(si(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...T(this,Ds).values()];let n={};return t.forEach(r=>{oi(e,r.mutationKey)&&(n={...n,...r.defaultOptions})}),n}defaultQueryOptions(e){if(e._defaulted)return e;const t={...T(this,Kn).queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=pf(t.queryKey,t)),t.refetchOnReconnect===void 0&&(t.refetchOnReconnect=t.networkMode!=="always"),t.throwOnError===void 0&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.enabled!==!0&&t.queryFn===hf&&(t.enabled=!1),t}defaultMutationOptions(e){return e!=null&&e._defaulted?e:{...T(this,Kn).mutations,...(e==null?void 0:e.mutationKey)&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){T(this,Ee).clear(),T(this,Wn).clear()}},Ee=new WeakMap,Wn=new WeakMap,Kn=new WeakMap,Os=new WeakMap,Ds=new WeakMap,Gn=new WeakMap,Ls=new WeakMap,Fs=new WeakMap,th),JS=f.createContext(void 0),ZS=({client:e,children:t})=>(f.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),o.jsx(JS.Provider,{value:e,children:t}));/**
 * @remix-run/router v1.20.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ii(){return ii=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ii.apply(this,arguments)}var qn;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(qn||(qn={}));const Cp="popstate";function eC(e){e===void 0&&(e={});function t(r,s){let{pathname:i,search:a,hash:l}=r.location;return Fu("",{pathname:i,search:a,hash:l},s.state&&s.state.usr||null,s.state&&s.state.key||"default")}function n(r,s){return typeof s=="string"?s:Ga(s)}return nC(t,n,null,e)}function Re(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Fv(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function tC(){return Math.random().toString(36).substr(2,8)}function Ep(e,t){return{usr:e.state,key:e.key,idx:t}}function Fu(e,t,n,r){return n===void 0&&(n=null),ii({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?oo(t):t,{state:n,key:t&&t.key||r||tC()})}function Ga(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function oo(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function nC(e,t,n,r){r===void 0&&(r={});let{window:s=document.defaultView,v5Compat:i=!1}=r,a=s.history,l=qn.Pop,c=null,u=p();u==null&&(u=0,a.replaceState(ii({},a.state,{idx:u}),""));function p(){return(a.state||{idx:null}).idx}function m(){l=qn.Pop;let j=p(),g=j==null?null:j-u;u=j,c&&c({action:l,location:h.location,delta:g})}function d(j,g){l=qn.Push;let x=Fu(h.location,j,g);u=p()+1;let y=Ep(x,u),N=h.createHref(x);try{a.pushState(y,"",N)}catch(b){if(b instanceof DOMException&&b.name==="DataCloneError")throw b;s.location.assign(N)}i&&c&&c({action:l,location:h.location,delta:1})}function v(j,g){l=qn.Replace;let x=Fu(h.location,j,g);u=p();let y=Ep(x,u),N=h.createHref(x);a.replaceState(y,"",N),i&&c&&c({action:l,location:h.location,delta:0})}function w(j){let g=s.location.origin!=="null"?s.location.origin:s.location.href,x=typeof j=="string"?j:Ga(j);return x=x.replace(/ $/,"%20"),Re(g,"No window.location.(origin|href) available to create URL for href: "+x),new URL(x,g)}let h={get action(){return l},get location(){return e(s,a)},listen(j){if(c)throw new Error("A history only accepts one active listener");return s.addEventListener(Cp,m),c=j,()=>{s.removeEventListener(Cp,m),c=null}},createHref(j){return t(s,j)},createURL:w,encodeLocation(j){let g=w(j);return{pathname:g.pathname,search:g.search,hash:g.hash}},push:d,replace:v,go(j){return a.go(j)}};return h}var kp;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(kp||(kp={}));function rC(e,t,n){return n===void 0&&(n="/"),sC(e,t,n,!1)}function sC(e,t,n,r){let s=typeof t=="string"?oo(t):t,i=xf(s.pathname||"/",n);if(i==null)return null;let a=zv(e);oC(a);let l=null;for(let c=0;l==null&&c<a.length;++c){let u=xC(i);l=pC(a[c],u,r)}return l}function zv(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let s=(i,a,l)=>{let c={relativePath:l===void 0?i.path||"":l,caseSensitive:i.caseSensitive===!0,childrenIndex:a,route:i};c.relativePath.startsWith("/")&&(Re(c.relativePath.startsWith(r),'Absolute route path "'+c.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),c.relativePath=c.relativePath.slice(r.length));let u=or([r,c.relativePath]),p=n.concat(c);i.children&&i.children.length>0&&(Re(i.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),zv(i.children,t,p,u)),!(i.path==null&&!i.index)&&t.push({path:u,score:fC(u,i.index),routesMeta:p})};return e.forEach((i,a)=>{var l;if(i.path===""||!((l=i.path)!=null&&l.includes("?")))s(i,a);else for(let c of $v(i.path))s(i,a,c)}),t}function $v(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,s=n.endsWith("?"),i=n.replace(/\?$/,"");if(r.length===0)return s?[i,""]:[i];let a=$v(r.join("/")),l=[];return l.push(...a.map(c=>c===""?i:[i,c].join("/"))),s&&l.push(...a),l.map(c=>e.startsWith("/")&&c===""?"/":c)}function oC(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:mC(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const iC=/^:[\w-]+$/,aC=3,lC=2,cC=1,uC=10,dC=-2,Pp=e=>e==="*";function fC(e,t){let n=e.split("/"),r=n.length;return n.some(Pp)&&(r+=dC),t&&(r+=lC),n.filter(s=>!Pp(s)).reduce((s,i)=>s+(iC.test(i)?aC:i===""?cC:uC),r)}function mC(e,t){return e.length===t.length&&e.slice(0,-1).every((r,s)=>r===t[s])?e[e.length-1]-t[t.length-1]:0}function pC(e,t,n){let{routesMeta:r}=e,s={},i="/",a=[];for(let l=0;l<r.length;++l){let c=r[l],u=l===r.length-1,p=i==="/"?t:t.slice(i.length)||"/",m=Tp({path:c.relativePath,caseSensitive:c.caseSensitive,end:u},p),d=c.route;if(!m&&u&&n&&!r[r.length-1].route.index&&(m=Tp({path:c.relativePath,caseSensitive:c.caseSensitive,end:!1},p)),!m)return null;Object.assign(s,m.params),a.push({params:s,pathname:or([i,m.pathname]),pathnameBase:wC(or([i,m.pathnameBase])),route:d}),m.pathnameBase!=="/"&&(i=or([i,m.pathnameBase]))}return a}function Tp(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=hC(e.path,e.caseSensitive,e.end),s=t.match(n);if(!s)return null;let i=s[0],a=i.replace(/(.)\/+$/,"$1"),l=s.slice(1);return{params:r.reduce((u,p,m)=>{let{paramName:d,isOptional:v}=p;if(d==="*"){let h=l[m]||"";a=i.slice(0,i.length-h.length).replace(/(.)\/+$/,"$1")}const w=l[m];return v&&!w?u[d]=void 0:u[d]=(w||"").replace(/%2F/g,"/"),u},{}),pathname:i,pathnameBase:a,pattern:e}}function hC(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),Fv(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],s="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(a,l,c)=>(r.push({paramName:l,isOptional:c!=null}),c?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),s+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?s+="\\/*$":e!==""&&e!=="/"&&(s+="(?:(?=\\/|$))"),[new RegExp(s,t?void 0:"i"),r]}function xC(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Fv(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function xf(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function gC(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:s=""}=typeof e=="string"?oo(e):e;return{pathname:n?n.startsWith("/")?n:vC(n,t):t,search:jC(r),hash:NC(s)}}function vC(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(s=>{s===".."?n.length>1&&n.pop():s!=="."&&n.push(s)}),n.length>1?n.join("/"):"/"}function bc(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function yC(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function gf(e,t){let n=yC(e);return t?n.map((r,s)=>s===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function vf(e,t,n,r){r===void 0&&(r=!1);let s;typeof e=="string"?s=oo(e):(s=ii({},e),Re(!s.pathname||!s.pathname.includes("?"),bc("?","pathname","search",s)),Re(!s.pathname||!s.pathname.includes("#"),bc("#","pathname","hash",s)),Re(!s.search||!s.search.includes("#"),bc("#","search","hash",s)));let i=e===""||s.pathname==="",a=i?"/":s.pathname,l;if(a==null)l=n;else{let m=t.length-1;if(!r&&a.startsWith("..")){let d=a.split("/");for(;d[0]==="..";)d.shift(),m-=1;s.pathname=d.join("/")}l=m>=0?t[m]:"/"}let c=gC(s,l),u=a&&a!=="/"&&a.endsWith("/"),p=(i||a===".")&&n.endsWith("/");return!c.pathname.endsWith("/")&&(u||p)&&(c.pathname+="/"),c}const or=e=>e.join("/").replace(/\/\/+/g,"/"),wC=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),jC=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,NC=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function bC(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const Uv=["post","put","patch","delete"];new Set(Uv);const SC=["get",...Uv];new Set(SC);/**
 * React Router v6.27.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ai(){return ai=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ai.apply(this,arguments)}const yf=f.createContext(null),CC=f.createContext(null),vr=f.createContext(null),Rl=f.createContext(null),Rn=f.createContext({outlet:null,matches:[],isDataRoute:!1}),Bv=f.createContext(null);function EC(e,t){let{relative:n}=t===void 0?{}:t;io()||Re(!1);let{basename:r,navigator:s}=f.useContext(vr),{hash:i,pathname:a,search:l}=Wv(e,{relative:n}),c=a;return r!=="/"&&(c=a==="/"?r:or([r,a])),s.createHref({pathname:c,search:l,hash:i})}function io(){return f.useContext(Rl)!=null}function Mn(){return io()||Re(!1),f.useContext(Rl).location}function Vv(e){f.useContext(vr).static||f.useLayoutEffect(e)}function qr(){let{isDataRoute:e}=f.useContext(Rn);return e?zC():kC()}function kC(){io()||Re(!1);let e=f.useContext(yf),{basename:t,future:n,navigator:r}=f.useContext(vr),{matches:s}=f.useContext(Rn),{pathname:i}=Mn(),a=JSON.stringify(gf(s,n.v7_relativeSplatPath)),l=f.useRef(!1);return Vv(()=>{l.current=!0}),f.useCallback(function(u,p){if(p===void 0&&(p={}),!l.current)return;if(typeof u=="number"){r.go(u);return}let m=vf(u,JSON.parse(a),i,p.relative==="path");e==null&&t!=="/"&&(m.pathname=m.pathname==="/"?t:or([t,m.pathname])),(p.replace?r.replace:r.push)(m,p.state,p)},[t,r,a,i,e])}function Hv(){let{matches:e}=f.useContext(Rn),t=e[e.length-1];return t?t.params:{}}function Wv(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=f.useContext(vr),{matches:s}=f.useContext(Rn),{pathname:i}=Mn(),a=JSON.stringify(gf(s,r.v7_relativeSplatPath));return f.useMemo(()=>vf(e,JSON.parse(a),i,n==="path"),[e,a,i,n])}function PC(e,t){return TC(e,t)}function TC(e,t,n,r){io()||Re(!1);let{navigator:s}=f.useContext(vr),{matches:i}=f.useContext(Rn),a=i[i.length-1],l=a?a.params:{};a&&a.pathname;let c=a?a.pathnameBase:"/";a&&a.route;let u=Mn(),p;if(t){var m;let j=typeof t=="string"?oo(t):t;c==="/"||(m=j.pathname)!=null&&m.startsWith(c)||Re(!1),p=j}else p=u;let d=p.pathname||"/",v=d;if(c!=="/"){let j=c.replace(/^\//,"").split("/");v="/"+d.replace(/^\//,"").split("/").slice(j.length).join("/")}let w=rC(e,{pathname:v}),h=_C(w&&w.map(j=>Object.assign({},j,{params:Object.assign({},l,j.params),pathname:or([c,s.encodeLocation?s.encodeLocation(j.pathname).pathname:j.pathname]),pathnameBase:j.pathnameBase==="/"?c:or([c,s.encodeLocation?s.encodeLocation(j.pathnameBase).pathname:j.pathnameBase])})),i,n,r);return t&&h?f.createElement(Rl.Provider,{value:{location:ai({pathname:"/",search:"",hash:"",state:null,key:"default"},p),navigationType:qn.Pop}},h):h}function RC(){let e=FC(),t=bC(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,s={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return f.createElement(f.Fragment,null,f.createElement("h2",null,"Unexpected Application Error!"),f.createElement("h3",{style:{fontStyle:"italic"}},t),n?f.createElement("pre",{style:s},n):null,null)}const MC=f.createElement(RC,null);class AC extends f.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?f.createElement(Rn.Provider,{value:this.props.routeContext},f.createElement(Bv.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function IC(e){let{routeContext:t,match:n,children:r}=e,s=f.useContext(yf);return s&&s.static&&s.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(s.staticContext._deepestRenderedBoundaryId=n.route.id),f.createElement(Rn.Provider,{value:t},r)}function _C(e,t,n,r){var s;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var i;if(!n)return null;if(n.errors)e=n.matches;else if((i=r)!=null&&i.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let a=e,l=(s=n)==null?void 0:s.errors;if(l!=null){let p=a.findIndex(m=>m.route.id&&(l==null?void 0:l[m.route.id])!==void 0);p>=0||Re(!1),a=a.slice(0,Math.min(a.length,p+1))}let c=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let p=0;p<a.length;p++){let m=a[p];if((m.route.HydrateFallback||m.route.hydrateFallbackElement)&&(u=p),m.route.id){let{loaderData:d,errors:v}=n,w=m.route.loader&&d[m.route.id]===void 0&&(!v||v[m.route.id]===void 0);if(m.route.lazy||w){c=!0,u>=0?a=a.slice(0,u+1):a=[a[0]];break}}}return a.reduceRight((p,m,d)=>{let v,w=!1,h=null,j=null;n&&(v=l&&m.route.id?l[m.route.id]:void 0,h=m.route.errorElement||MC,c&&(u<0&&d===0?(w=!0,j=null):u===d&&(w=!0,j=m.route.hydrateFallbackElement||null)));let g=t.concat(a.slice(0,d+1)),x=()=>{let y;return v?y=h:w?y=j:m.route.Component?y=f.createElement(m.route.Component,null):m.route.element?y=m.route.element:y=p,f.createElement(IC,{match:m,routeContext:{outlet:p,matches:g,isDataRoute:n!=null},children:y})};return n&&(m.route.ErrorBoundary||m.route.errorElement||d===0)?f.createElement(AC,{location:n.location,revalidation:n.revalidation,component:h,error:v,children:x(),routeContext:{outlet:null,matches:g,isDataRoute:!0}}):x()},null)}var Kv=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(Kv||{}),Qa=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Qa||{});function OC(e){let t=f.useContext(yf);return t||Re(!1),t}function DC(e){let t=f.useContext(CC);return t||Re(!1),t}function LC(e){let t=f.useContext(Rn);return t||Re(!1),t}function Gv(e){let t=LC(),n=t.matches[t.matches.length-1];return n.route.id||Re(!1),n.route.id}function FC(){var e;let t=f.useContext(Bv),n=DC(Qa.UseRouteError),r=Gv(Qa.UseRouteError);return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function zC(){let{router:e}=OC(Kv.UseNavigateStable),t=Gv(Qa.UseNavigateStable),n=f.useRef(!1);return Vv(()=>{n.current=!0}),f.useCallback(function(s,i){i===void 0&&(i={}),n.current&&(typeof s=="number"?e.navigate(s):e.navigate(s,ai({fromRouteId:t},i)))},[e,t])}function Ya(e){let{to:t,replace:n,state:r,relative:s}=e;io()||Re(!1);let{future:i,static:a}=f.useContext(vr),{matches:l}=f.useContext(Rn),{pathname:c}=Mn(),u=qr(),p=vf(t,gf(l,i.v7_relativeSplatPath),c,s==="path"),m=JSON.stringify(p);return f.useEffect(()=>u(JSON.parse(m),{replace:n,state:r,relative:s}),[u,m,s,n,r]),null}function Oe(e){Re(!1)}function $C(e){let{basename:t="/",children:n=null,location:r,navigationType:s=qn.Pop,navigator:i,static:a=!1,future:l}=e;io()&&Re(!1);let c=t.replace(/^\/*/,"/"),u=f.useMemo(()=>({basename:c,navigator:i,static:a,future:ai({v7_relativeSplatPath:!1},l)}),[c,l,i,a]);typeof r=="string"&&(r=oo(r));let{pathname:p="/",search:m="",hash:d="",state:v=null,key:w="default"}=r,h=f.useMemo(()=>{let j=xf(p,c);return j==null?null:{location:{pathname:j,search:m,hash:d,state:v,key:w},navigationType:s}},[c,p,m,d,v,w,s]);return h==null?null:f.createElement(vr.Provider,{value:u},f.createElement(Rl.Provider,{children:n,value:h}))}function UC(e){let{children:t,location:n}=e;return PC(zu(t),n)}new Promise(()=>{});function zu(e,t){t===void 0&&(t=[]);let n=[];return f.Children.forEach(e,(r,s)=>{if(!f.isValidElement(r))return;let i=[...t,s];if(r.type===f.Fragment){n.push.apply(n,zu(r.props.children,i));return}r.type!==Oe&&Re(!1),!r.props.index||!r.props.children||Re(!1);let a={id:r.props.id||i.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(a.children=zu(r.props.children,i)),n.push(a)}),n}/**
 * React Router DOM v6.27.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function $u(){return $u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},$u.apply(this,arguments)}function BC(e,t){if(e==null)return{};var n={},r=Object.keys(e),s,i;for(i=0;i<r.length;i++)s=r[i],!(t.indexOf(s)>=0)&&(n[s]=e[s]);return n}function VC(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function HC(e,t){return e.button===0&&(!t||t==="_self")&&!VC(e)}function Uu(e){return e===void 0&&(e=""),new URLSearchParams(typeof e=="string"||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce((t,n)=>{let r=e[n];return t.concat(Array.isArray(r)?r.map(s=>[n,s]):[[n,r]])},[]))}function WC(e,t){let n=Uu(e);return t&&t.forEach((r,s)=>{n.has(s)||t.getAll(s).forEach(i=>{n.append(s,i)})}),n}const KC=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],GC="6";try{window.__reactRouterVersion=GC}catch{}const QC="startTransition",Rp=mh[QC];function YC(e){let{basename:t,children:n,future:r,window:s}=e,i=f.useRef();i.current==null&&(i.current=eC({window:s,v5Compat:!0}));let a=i.current,[l,c]=f.useState({action:a.action,location:a.location}),{v7_startTransition:u}=r||{},p=f.useCallback(m=>{u&&Rp?Rp(()=>c(m)):c(m)},[c,u]);return f.useLayoutEffect(()=>a.listen(p),[a,p]),f.createElement($C,{basename:t,children:n,location:l.location,navigationType:l.action,navigator:a,future:r})}const qC=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",XC=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,be=f.forwardRef(function(t,n){let{onClick:r,relative:s,reloadDocument:i,replace:a,state:l,target:c,to:u,preventScrollReset:p,viewTransition:m}=t,d=BC(t,KC),{basename:v}=f.useContext(vr),w,h=!1;if(typeof u=="string"&&XC.test(u)&&(w=u,qC))try{let y=new URL(window.location.href),N=u.startsWith("//")?new URL(y.protocol+u):new URL(u),b=xf(N.pathname,v);N.origin===y.origin&&b!=null?u=b+N.search+N.hash:h=!0}catch{}let j=EC(u,{relative:s}),g=JC(u,{replace:a,state:l,target:c,preventScrollReset:p,relative:s,viewTransition:m});function x(y){r&&r(y),y.defaultPrevented||g(y)}return f.createElement("a",$u({},d,{href:w||j,onClick:h||i?r:x,ref:n,target:c}))});var Mp;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Mp||(Mp={}));var Ap;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(Ap||(Ap={}));function JC(e,t){let{target:n,replace:r,state:s,preventScrollReset:i,relative:a,viewTransition:l}=t===void 0?{}:t,c=qr(),u=Mn(),p=Wv(e,{relative:a});return f.useCallback(m=>{if(HC(m,n)){m.preventDefault();let d=r!==void 0?r:Ga(u)===Ga(p);c(e,{replace:d,state:s,preventScrollReset:i,relative:a,viewTransition:l})}},[u,c,p,r,s,n,e,i,a,l])}function ZC(e){let t=f.useRef(Uu(e)),n=f.useRef(!1),r=Mn(),s=f.useMemo(()=>WC(r.search,n.current?null:t.current),[r.search]),i=qr(),a=f.useCallback((l,c)=>{const u=Uu(typeof l=="function"?l(s):l);n.current=!0,i("?"+u,c)},[i,s]);return[s,a]}const Qv=f.createContext(void 0);function eE({children:e}){const[t,n]=f.useState(null),[r,s]=f.useState(!0),i=qr(),{toast:a}=Qr();f.useEffect(()=>{const p=localStorage.getItem("mdc_user");if(p)try{n(JSON.parse(p))}catch{localStorage.removeItem("mdc_user")}s(!1)},[]);const l=(p,m)=>{s(!0),setTimeout(()=>{if(p==="<EMAIL>"&&m==="User123!"){const d={email:p,role:"user"};n(d),localStorage.setItem("mdc_user",JSON.stringify(d)),i("/dashboard"),a({title:"Login successful",description:"Welcome to MyDigitalColleague"})}else if(p==="<EMAIL>"&&m==="Admin123!"){const d={email:p,role:"admin"};n(d),localStorage.setItem("mdc_user",JSON.stringify(d)),i("/admin/dashboard"),a({title:"Admin login successful",description:"Welcome to MyDigitalColleague Admin"})}else a({title:"Login failed",description:"Invalid email or password. Try demo login instead.",variant:"destructive"});s(!1)},1e3)},c=()=>{s(!0),setTimeout(()=>{const p={email:"<EMAIL>",role:"demo"};n(p),localStorage.setItem("mdc_user",JSON.stringify(p)),i("/dashboard"),a({title:"Demo login successful",description:"Welcome to the MyDigitalColleague demo"}),s(!1)},1e3)},u=()=>{n(null),localStorage.removeItem("mdc_user"),i("/"),a({title:"Logged out",description:"You have been successfully logged out"})};return o.jsx(Qv.Provider,{value:{user:t,isLoading:r,login:l,logout:u,demoLogin:c},children:e})}function Xr(){const e=f.useContext(Qv);if(e===void 0)throw new Error("useAuth must be used within an AuthProvider");return e}function Xt({children:e,allowedRoles:t=["user","demo"]}){const{user:n,isLoading:r}=Xr();return r?o.jsx("div",{className:"flex h-screen items-center justify-center",children:"Loading..."}):n?t.includes(n.role)?o.jsx(o.Fragment,{children:e}):o.jsx(Ya,{to:"/dashboard"}):o.jsx(Ya,{to:"/"})}function yo({children:e}){const{user:t,isLoading:n}=Xr();return n?o.jsx("div",{className:"flex h-screen items-center justify-center",children:"Loading..."}):t?t.role!=="admin"?o.jsx(Ya,{to:"/"}):o.jsx(o.Fragment,{children:e}):o.jsx(Ya,{to:"/"})}const tE=Yd("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),V=f.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...s},i)=>{const a=r?$r:"button";return o.jsx(a,{className:ee(tE({variant:t,size:n,className:e})),ref:i,...s})});V.displayName="Button";var nE=f.createContext(void 0);function Ml(e){const t=f.useContext(nE);return e||t||"ltr"}var Sc=0;function Yv(){f.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??Ip()),document.body.insertAdjacentElement("beforeend",e[1]??Ip()),Sc++,()=>{Sc===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),Sc--}},[])}function Ip(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var Cc="focusScope.autoFocusOnMount",Ec="focusScope.autoFocusOnUnmount",_p={bubbles:!1,cancelable:!0},rE="FocusScope",wf=f.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:s,onUnmountAutoFocus:i,...a}=e,[l,c]=f.useState(null),u=Xe(s),p=Xe(i),m=f.useRef(null),d=me(t,h=>c(h)),v=f.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;f.useEffect(()=>{if(r){let h=function(y){if(v.paused||!l)return;const N=y.target;l.contains(N)?m.current=N:Fn(m.current,{select:!0})},j=function(y){if(v.paused||!l)return;const N=y.relatedTarget;N!==null&&(l.contains(N)||Fn(m.current,{select:!0}))},g=function(y){if(document.activeElement===document.body)for(const b of y)b.removedNodes.length>0&&Fn(l)};document.addEventListener("focusin",h),document.addEventListener("focusout",j);const x=new MutationObserver(g);return l&&x.observe(l,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",h),document.removeEventListener("focusout",j),x.disconnect()}}},[r,l,v.paused]),f.useEffect(()=>{if(l){Dp.add(v);const h=document.activeElement;if(!l.contains(h)){const g=new CustomEvent(Cc,_p);l.addEventListener(Cc,u),l.dispatchEvent(g),g.defaultPrevented||(sE(cE(qv(l)),{select:!0}),document.activeElement===h&&Fn(l))}return()=>{l.removeEventListener(Cc,u),setTimeout(()=>{const g=new CustomEvent(Ec,_p);l.addEventListener(Ec,p),l.dispatchEvent(g),g.defaultPrevented||Fn(h??document.body,{select:!0}),l.removeEventListener(Ec,p),Dp.remove(v)},0)}}},[l,u,p,v]);const w=f.useCallback(h=>{if(!n&&!r||v.paused)return;const j=h.key==="Tab"&&!h.altKey&&!h.ctrlKey&&!h.metaKey,g=document.activeElement;if(j&&g){const x=h.currentTarget,[y,N]=oE(x);y&&N?!h.shiftKey&&g===N?(h.preventDefault(),n&&Fn(y,{select:!0})):h.shiftKey&&g===y&&(h.preventDefault(),n&&Fn(N,{select:!0})):g===x&&h.preventDefault()}},[n,r,v.paused]);return o.jsx(Y.div,{tabIndex:-1,...a,ref:d,onKeyDown:w})});wf.displayName=rE;function sE(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(Fn(r,{select:t}),document.activeElement!==n)return}function oE(e){const t=qv(e),n=Op(t,e),r=Op(t.reverse(),e);return[n,r]}function qv(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const s=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||s?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Op(e,t){for(const n of e)if(!iE(n,{upTo:t}))return n}function iE(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function aE(e){return e instanceof HTMLInputElement&&"select"in e}function Fn(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&aE(e)&&t&&e.select()}}var Dp=lE();function lE(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=Lp(e,t),e.unshift(t)},remove(t){var n;e=Lp(e,t),(n=e[0])==null||n.resume()}}}function Lp(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function cE(e){return e.filter(t=>t.tagName!=="A")}function uE(e,t=[]){let n=[];function r(i,a){const l=f.createContext(a),c=n.length;n=[...n,a];function u(m){const{scope:d,children:v,...w}=m,h=(d==null?void 0:d[e][c])||l,j=f.useMemo(()=>w,Object.values(w));return o.jsx(h.Provider,{value:j,children:v})}function p(m,d){const v=(d==null?void 0:d[e][c])||l,w=f.useContext(v);if(w)return w;if(a!==void 0)return a;throw new Error(`\`${m}\` must be used within \`${i}\``)}return u.displayName=i+"Provider",[u,p]}const s=()=>{const i=n.map(a=>f.createContext(a));return function(l){const c=(l==null?void 0:l[e])||i;return f.useMemo(()=>({[`__scope${e}`]:{...l,[e]:c}}),[l,c])}};return s.scopeName=e,[r,dE(s,...t)]}function dE(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(s=>({useScope:s(),scopeName:s.scopeName}));return function(i){const a=r.reduce((l,{useScope:c,scopeName:u})=>{const m=c(i)[`__scope${u}`];return{...l,...m}},{});return f.useMemo(()=>({[`__scope${t.scopeName}`]:a}),[a])}};return n.scopeName=t.scopeName,n}var kc="rovingFocusGroup.onEntryFocus",fE={bubbles:!1,cancelable:!0},Al="RovingFocusGroup",[Bu,Xv,mE]=gl(Al),[pE,Il]=uE(Al,[mE]),[hE,xE]=pE(Al),Jv=f.forwardRef((e,t)=>o.jsx(Bu.Provider,{scope:e.__scopeRovingFocusGroup,children:o.jsx(Bu.Slot,{scope:e.__scopeRovingFocusGroup,children:o.jsx(gE,{...e,ref:t})})}));Jv.displayName=Al;var gE=f.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:r,loop:s=!1,dir:i,currentTabStopId:a,defaultCurrentTabStopId:l,onCurrentTabStopIdChange:c,onEntryFocus:u,preventScrollOnEntryFocus:p=!1,...m}=e,d=f.useRef(null),v=me(t,d),w=Ml(i),[h=null,j]=Ks({prop:a,defaultProp:l,onChange:c}),[g,x]=f.useState(!1),y=Xe(u),N=Xv(n),b=f.useRef(!1),[E,C]=f.useState(0);return f.useEffect(()=>{const k=d.current;if(k)return k.addEventListener(kc,y),()=>k.removeEventListener(kc,y)},[y]),o.jsx(hE,{scope:n,orientation:r,dir:w,loop:s,currentTabStopId:h,onItemFocus:f.useCallback(k=>j(k),[j]),onItemShiftTab:f.useCallback(()=>x(!0),[]),onFocusableItemAdd:f.useCallback(()=>C(k=>k+1),[]),onFocusableItemRemove:f.useCallback(()=>C(k=>k-1),[]),children:o.jsx(Y.div,{tabIndex:g||E===0?-1:0,"data-orientation":r,...m,ref:v,style:{outline:"none",...e.style},onMouseDown:z(e.onMouseDown,()=>{b.current=!0}),onFocus:z(e.onFocus,k=>{const I=!b.current;if(k.target===k.currentTarget&&I&&!g){const M=new CustomEvent(kc,fE);if(k.currentTarget.dispatchEvent(M),!M.defaultPrevented){const U=N().filter(F=>F.focusable),D=U.find(F=>F.active),W=U.find(F=>F.id===h),K=[D,W,...U].filter(Boolean).map(F=>F.ref.current);t0(K,p)}}b.current=!1}),onBlur:z(e.onBlur,()=>x(!1))})})}),Zv="RovingFocusGroupItem",e0=f.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:r=!0,active:s=!1,tabStopId:i,...a}=e,l=Ur(),c=i||l,u=xE(Zv,n),p=u.currentTabStopId===c,m=Xv(n),{onFocusableItemAdd:d,onFocusableItemRemove:v}=u;return f.useEffect(()=>{if(r)return d(),()=>v()},[r,d,v]),o.jsx(Bu.ItemSlot,{scope:n,id:c,focusable:r,active:s,children:o.jsx(Y.span,{tabIndex:p?0:-1,"data-orientation":u.orientation,...a,ref:t,onMouseDown:z(e.onMouseDown,w=>{r?u.onItemFocus(c):w.preventDefault()}),onFocus:z(e.onFocus,()=>u.onItemFocus(c)),onKeyDown:z(e.onKeyDown,w=>{if(w.key==="Tab"&&w.shiftKey){u.onItemShiftTab();return}if(w.target!==w.currentTarget)return;const h=wE(w,u.orientation,u.dir);if(h!==void 0){if(w.metaKey||w.ctrlKey||w.altKey||w.shiftKey)return;w.preventDefault();let g=m().filter(x=>x.focusable).map(x=>x.ref.current);if(h==="last")g.reverse();else if(h==="prev"||h==="next"){h==="prev"&&g.reverse();const x=g.indexOf(w.currentTarget);g=u.loop?jE(g,x+1):g.slice(x+1)}setTimeout(()=>t0(g))}})})})});e0.displayName=Zv;var vE={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function yE(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function wE(e,t,n){const r=yE(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(r))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(r)))return vE[r]}function t0(e,t=!1){const n=document.activeElement;for(const r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}function jE(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var n0=Jv,r0=e0,NE=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},ss=new WeakMap,Yi=new WeakMap,qi={},Pc=0,s0=function(e){return e&&(e.host||s0(e.parentNode))},bE=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=s0(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},SE=function(e,t,n,r){var s=bE(t,Array.isArray(e)?e:[e]);qi[n]||(qi[n]=new WeakMap);var i=qi[n],a=[],l=new Set,c=new Set(s),u=function(m){!m||l.has(m)||(l.add(m),u(m.parentNode))};s.forEach(u);var p=function(m){!m||c.has(m)||Array.prototype.forEach.call(m.children,function(d){if(l.has(d))p(d);else try{var v=d.getAttribute(r),w=v!==null&&v!=="false",h=(ss.get(d)||0)+1,j=(i.get(d)||0)+1;ss.set(d,h),i.set(d,j),a.push(d),h===1&&w&&Yi.set(d,!0),j===1&&d.setAttribute(n,"true"),w||d.setAttribute(r,"true")}catch(g){console.error("aria-hidden: cannot operate on ",d,g)}})};return p(t),l.clear(),Pc++,function(){a.forEach(function(m){var d=ss.get(m)-1,v=i.get(m)-1;ss.set(m,d),i.set(m,v),d||(Yi.has(m)||m.removeAttribute(r),Yi.delete(m)),v||m.removeAttribute(n)}),Pc--,Pc||(ss=new WeakMap,ss=new WeakMap,Yi=new WeakMap,qi={})}},o0=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),s=NE(e);return s?(r.push.apply(r,Array.from(s.querySelectorAll("[aria-live]"))),SE(r,s,n,"aria-hidden")):function(){return null}},rn=function(){return rn=Object.assign||function(t){for(var n,r=1,s=arguments.length;r<s;r++){n=arguments[r];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t},rn.apply(this,arguments)};function i0(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,r=Object.getOwnPropertySymbols(e);s<r.length;s++)t.indexOf(r[s])<0&&Object.prototype.propertyIsEnumerable.call(e,r[s])&&(n[r[s]]=e[r[s]]);return n}function CE(e,t,n){if(n||arguments.length===2)for(var r=0,s=t.length,i;r<s;r++)(i||!(r in t))&&(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))}var pa="right-scroll-bar-position",ha="width-before-scroll-bar",EE="with-scroll-bars-hidden",kE="--removed-body-scroll-bar-size";function Tc(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function PE(e,t){var n=f.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var s=n.value;s!==r&&(n.value=r,n.callback(r,s))}}}})[0];return n.callback=t,n.facade}var TE=typeof window<"u"?f.useLayoutEffect:f.useEffect,Fp=new WeakMap;function RE(e,t){var n=PE(null,function(r){return e.forEach(function(s){return Tc(s,r)})});return TE(function(){var r=Fp.get(n);if(r){var s=new Set(r),i=new Set(e),a=n.current;s.forEach(function(l){i.has(l)||Tc(l,null)}),i.forEach(function(l){s.has(l)||Tc(l,a)})}Fp.set(n,e)},[e]),n}function ME(e){return e}function AE(e,t){t===void 0&&(t=ME);var n=[],r=!1,s={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(i){var a=t(i,r);return n.push(a),function(){n=n.filter(function(l){return l!==a})}},assignSyncMedium:function(i){for(r=!0;n.length;){var a=n;n=[],a.forEach(i)}n={push:function(l){return i(l)},filter:function(){return n}}},assignMedium:function(i){r=!0;var a=[];if(n.length){var l=n;n=[],l.forEach(i),a=n}var c=function(){var p=a;a=[],p.forEach(i)},u=function(){return Promise.resolve().then(c)};u(),n={push:function(p){a.push(p),u()},filter:function(p){return a=a.filter(p),n}}}};return s}function IE(e){e===void 0&&(e={});var t=AE(null);return t.options=rn({async:!0,ssr:!1},e),t}var a0=function(e){var t=e.sideCar,n=i0(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return f.createElement(r,rn({},n))};a0.isSideCarExport=!0;function _E(e,t){return e.useMedium(t),a0}var l0=IE(),Rc=function(){},_l=f.forwardRef(function(e,t){var n=f.useRef(null),r=f.useState({onScrollCapture:Rc,onWheelCapture:Rc,onTouchMoveCapture:Rc}),s=r[0],i=r[1],a=e.forwardProps,l=e.children,c=e.className,u=e.removeScrollBar,p=e.enabled,m=e.shards,d=e.sideCar,v=e.noIsolation,w=e.inert,h=e.allowPinchZoom,j=e.as,g=j===void 0?"div":j,x=e.gapMode,y=i0(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),N=d,b=RE([n,t]),E=rn(rn({},y),s);return f.createElement(f.Fragment,null,p&&f.createElement(N,{sideCar:l0,removeScrollBar:u,shards:m,noIsolation:v,inert:w,setCallbacks:i,allowPinchZoom:!!h,lockRef:n,gapMode:x}),a?f.cloneElement(f.Children.only(l),rn(rn({},E),{ref:b})):f.createElement(g,rn({},E,{className:c,ref:b}),l))});_l.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};_l.classNames={fullWidth:ha,zeroRight:pa};var OE=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function DE(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=OE();return t&&e.setAttribute("nonce",t),e}function LE(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function FE(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var zE=function(){var e=0,t=null;return{add:function(n){e==0&&(t=DE())&&(LE(t,n),FE(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},$E=function(){var e=zE();return function(t,n){f.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},c0=function(){var e=$E(),t=function(n){var r=n.styles,s=n.dynamic;return e(r,s),null};return t},UE={left:0,top:0,right:0,gap:0},Mc=function(e){return parseInt(e||"",10)||0},BE=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],s=t[e==="padding"?"paddingRight":"marginRight"];return[Mc(n),Mc(r),Mc(s)]},VE=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return UE;var t=BE(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},HE=c0(),Ps="data-scroll-locked",WE=function(e,t,n,r){var s=e.left,i=e.top,a=e.right,l=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(EE,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(l,"px ").concat(r,`;
  }
  body[`).concat(Ps,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(s,`px;
    padding-top: `).concat(i,`px;
    padding-right: `).concat(a,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(l,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(pa,` {
    right: `).concat(l,"px ").concat(r,`;
  }
  
  .`).concat(ha,` {
    margin-right: `).concat(l,"px ").concat(r,`;
  }
  
  .`).concat(pa," .").concat(pa,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(ha," .").concat(ha,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(Ps,`] {
    `).concat(kE,": ").concat(l,`px;
  }
`)},zp=function(){var e=parseInt(document.body.getAttribute(Ps)||"0",10);return isFinite(e)?e:0},KE=function(){f.useEffect(function(){return document.body.setAttribute(Ps,(zp()+1).toString()),function(){var e=zp()-1;e<=0?document.body.removeAttribute(Ps):document.body.setAttribute(Ps,e.toString())}},[])},GE=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,s=r===void 0?"margin":r;KE();var i=f.useMemo(function(){return VE(s)},[s]);return f.createElement(HE,{styles:WE(i,!t,s,n?"":"!important")})},Vu=!1;if(typeof window<"u")try{var Xi=Object.defineProperty({},"passive",{get:function(){return Vu=!0,!0}});window.addEventListener("test",Xi,Xi),window.removeEventListener("test",Xi,Xi)}catch{Vu=!1}var os=Vu?{passive:!1}:!1,QE=function(e){return e.tagName==="TEXTAREA"},u0=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!QE(e)&&n[t]==="visible")},YE=function(e){return u0(e,"overflowY")},qE=function(e){return u0(e,"overflowX")},$p=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var s=d0(e,r);if(s){var i=f0(e,r),a=i[1],l=i[2];if(a>l)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},XE=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},JE=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},d0=function(e,t){return e==="v"?YE(t):qE(t)},f0=function(e,t){return e==="v"?XE(t):JE(t)},ZE=function(e,t){return e==="h"&&t==="rtl"?-1:1},ek=function(e,t,n,r,s){var i=ZE(e,window.getComputedStyle(t).direction),a=i*r,l=n.target,c=t.contains(l),u=!1,p=a>0,m=0,d=0;do{var v=f0(e,l),w=v[0],h=v[1],j=v[2],g=h-j-i*w;(w||g)&&d0(e,l)&&(m+=g,d+=w),l instanceof ShadowRoot?l=l.host:l=l.parentNode}while(!c&&l!==document.body||c&&(t.contains(l)||t===l));return(p&&(Math.abs(m)<1||!s)||!p&&(Math.abs(d)<1||!s))&&(u=!0),u},Ji=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Up=function(e){return[e.deltaX,e.deltaY]},Bp=function(e){return e&&"current"in e?e.current:e},tk=function(e,t){return e[0]===t[0]&&e[1]===t[1]},nk=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},rk=0,is=[];function sk(e){var t=f.useRef([]),n=f.useRef([0,0]),r=f.useRef(),s=f.useState(rk++)[0],i=f.useState(c0)[0],a=f.useRef(e);f.useEffect(function(){a.current=e},[e]),f.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(s));var h=CE([e.lockRef.current],(e.shards||[]).map(Bp),!0).filter(Boolean);return h.forEach(function(j){return j.classList.add("allow-interactivity-".concat(s))}),function(){document.body.classList.remove("block-interactivity-".concat(s)),h.forEach(function(j){return j.classList.remove("allow-interactivity-".concat(s))})}}},[e.inert,e.lockRef.current,e.shards]);var l=f.useCallback(function(h,j){if("touches"in h&&h.touches.length===2||h.type==="wheel"&&h.ctrlKey)return!a.current.allowPinchZoom;var g=Ji(h),x=n.current,y="deltaX"in h?h.deltaX:x[0]-g[0],N="deltaY"in h?h.deltaY:x[1]-g[1],b,E=h.target,C=Math.abs(y)>Math.abs(N)?"h":"v";if("touches"in h&&C==="h"&&E.type==="range")return!1;var k=$p(C,E);if(!k)return!0;if(k?b=C:(b=C==="v"?"h":"v",k=$p(C,E)),!k)return!1;if(!r.current&&"changedTouches"in h&&(y||N)&&(r.current=b),!b)return!0;var I=r.current||b;return ek(I,j,h,I==="h"?y:N,!0)},[]),c=f.useCallback(function(h){var j=h;if(!(!is.length||is[is.length-1]!==i)){var g="deltaY"in j?Up(j):Ji(j),x=t.current.filter(function(b){return b.name===j.type&&(b.target===j.target||j.target===b.shadowParent)&&tk(b.delta,g)})[0];if(x&&x.should){j.cancelable&&j.preventDefault();return}if(!x){var y=(a.current.shards||[]).map(Bp).filter(Boolean).filter(function(b){return b.contains(j.target)}),N=y.length>0?l(j,y[0]):!a.current.noIsolation;N&&j.cancelable&&j.preventDefault()}}},[]),u=f.useCallback(function(h,j,g,x){var y={name:h,delta:j,target:g,should:x,shadowParent:ok(g)};t.current.push(y),setTimeout(function(){t.current=t.current.filter(function(N){return N!==y})},1)},[]),p=f.useCallback(function(h){n.current=Ji(h),r.current=void 0},[]),m=f.useCallback(function(h){u(h.type,Up(h),h.target,l(h,e.lockRef.current))},[]),d=f.useCallback(function(h){u(h.type,Ji(h),h.target,l(h,e.lockRef.current))},[]);f.useEffect(function(){return is.push(i),e.setCallbacks({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:d}),document.addEventListener("wheel",c,os),document.addEventListener("touchmove",c,os),document.addEventListener("touchstart",p,os),function(){is=is.filter(function(h){return h!==i}),document.removeEventListener("wheel",c,os),document.removeEventListener("touchmove",c,os),document.removeEventListener("touchstart",p,os)}},[]);var v=e.removeScrollBar,w=e.inert;return f.createElement(f.Fragment,null,w?f.createElement(i,{styles:nk(s)}):null,v?f.createElement(GE,{gapMode:e.gapMode}):null)}function ok(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const ik=_E(l0,sk);var jf=f.forwardRef(function(e,t){return f.createElement(_l,rn({},e,{ref:t,sideCar:ik}))});jf.classNames=_l.classNames;var Hu=["Enter"," "],ak=["ArrowDown","PageUp","Home"],m0=["ArrowUp","PageDown","End"],lk=[...ak,...m0],ck={ltr:[...Hu,"ArrowRight"],rtl:[...Hu,"ArrowLeft"]},uk={ltr:["ArrowLeft"],rtl:["ArrowRight"]},ji="Menu",[li,dk,fk]=gl(ji),[Jr,p0]=eo(ji,[fk,so,Il]),Ol=so(),h0=Il(),[mk,Zr]=Jr(ji),[pk,Ni]=Jr(ji),x0=e=>{const{__scopeMenu:t,open:n=!1,children:r,dir:s,onOpenChange:i,modal:a=!0}=e,l=Ol(t),[c,u]=f.useState(null),p=f.useRef(!1),m=Xe(i),d=Ml(s);return f.useEffect(()=>{const v=()=>{p.current=!0,document.addEventListener("pointerdown",w,{capture:!0,once:!0}),document.addEventListener("pointermove",w,{capture:!0,once:!0})},w=()=>p.current=!1;return document.addEventListener("keydown",v,{capture:!0}),()=>{document.removeEventListener("keydown",v,{capture:!0}),document.removeEventListener("pointerdown",w,{capture:!0}),document.removeEventListener("pointermove",w,{capture:!0})}},[]),o.jsx(Nv,{...l,children:o.jsx(mk,{scope:t,open:n,onOpenChange:m,content:c,onContentChange:u,children:o.jsx(pk,{scope:t,onClose:f.useCallback(()=>m(!1),[m]),isUsingKeyboardRef:p,dir:d,modal:a,children:r})})})};x0.displayName=ji;var hk="MenuAnchor",Nf=f.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,s=Ol(n);return o.jsx(uf,{...s,...r,ref:t})});Nf.displayName=hk;var bf="MenuPortal",[xk,g0]=Jr(bf,{forceMount:void 0}),v0=e=>{const{__scopeMenu:t,forceMount:n,children:r,container:s}=e,i=Zr(bf,t);return o.jsx(xk,{scope:t,forceMount:n,children:o.jsx(xr,{present:n||i.open,children:o.jsx(vl,{asChild:!0,container:s,children:r})})})};v0.displayName=bf;var At="MenuContent",[gk,Sf]=Jr(At),y0=f.forwardRef((e,t)=>{const n=g0(At,e.__scopeMenu),{forceMount:r=n.forceMount,...s}=e,i=Zr(At,e.__scopeMenu),a=Ni(At,e.__scopeMenu);return o.jsx(li.Provider,{scope:e.__scopeMenu,children:o.jsx(xr,{present:r||i.open,children:o.jsx(li.Slot,{scope:e.__scopeMenu,children:a.modal?o.jsx(vk,{...s,ref:t}):o.jsx(yk,{...s,ref:t})})})})}),vk=f.forwardRef((e,t)=>{const n=Zr(At,e.__scopeMenu),r=f.useRef(null),s=me(t,r);return f.useEffect(()=>{const i=r.current;if(i)return o0(i)},[]),o.jsx(Cf,{...e,ref:s,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:z(e.onFocusOutside,i=>i.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),yk=f.forwardRef((e,t)=>{const n=Zr(At,e.__scopeMenu);return o.jsx(Cf,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),Cf=f.forwardRef((e,t)=>{const{__scopeMenu:n,loop:r=!1,trapFocus:s,onOpenAutoFocus:i,onCloseAutoFocus:a,disableOutsidePointerEvents:l,onEntryFocus:c,onEscapeKeyDown:u,onPointerDownOutside:p,onFocusOutside:m,onInteractOutside:d,onDismiss:v,disableOutsideScroll:w,...h}=e,j=Zr(At,n),g=Ni(At,n),x=Ol(n),y=h0(n),N=dk(n),[b,E]=f.useState(null),C=f.useRef(null),k=me(t,C,j.onContentChange),I=f.useRef(0),M=f.useRef(""),U=f.useRef(0),D=f.useRef(null),W=f.useRef("right"),A=f.useRef(0),K=w?jf:f.Fragment,F=w?{as:$r,allowPinchZoom:!0}:void 0,G=P=>{var H,se;const L=M.current+P,_=N().filter(ie=>!ie.disabled),B=document.activeElement,Q=(H=_.find(ie=>ie.ref.current===B))==null?void 0:H.textValue,re=_.map(ie=>ie.textValue),Pe=Mk(re,L,Q),q=(se=_.find(ie=>ie.textValue===Pe))==null?void 0:se.ref.current;(function ie(ne){M.current=ne,window.clearTimeout(I.current),ne!==""&&(I.current=window.setTimeout(()=>ie(""),1e3))})(L),q&&setTimeout(()=>q.focus())};f.useEffect(()=>()=>window.clearTimeout(I.current),[]),Yv();const S=f.useCallback(P=>{var _,B;return W.current===((_=D.current)==null?void 0:_.side)&&Ik(P,(B=D.current)==null?void 0:B.area)},[]);return o.jsx(gk,{scope:n,searchRef:M,onItemEnter:f.useCallback(P=>{S(P)&&P.preventDefault()},[S]),onItemLeave:f.useCallback(P=>{var L;S(P)||((L=C.current)==null||L.focus(),E(null))},[S]),onTriggerLeave:f.useCallback(P=>{S(P)&&P.preventDefault()},[S]),pointerGraceTimerRef:U,onPointerGraceIntentChange:f.useCallback(P=>{D.current=P},[]),children:o.jsx(K,{...F,children:o.jsx(wf,{asChild:!0,trapped:s,onMountAutoFocus:z(i,P=>{var L;P.preventDefault(),(L=C.current)==null||L.focus({preventScroll:!0})}),onUnmountAutoFocus:a,children:o.jsx(vi,{asChild:!0,disableOutsidePointerEvents:l,onEscapeKeyDown:u,onPointerDownOutside:p,onFocusOutside:m,onInteractOutside:d,onDismiss:v,children:o.jsx(n0,{asChild:!0,...y,dir:g.dir,orientation:"vertical",loop:r,currentTabStopId:b,onCurrentTabStopIdChange:E,onEntryFocus:z(c,P=>{g.isUsingKeyboardRef.current||P.preventDefault()}),preventScrollOnEntryFocus:!0,children:o.jsx(df,{role:"menu","aria-orientation":"vertical","data-state":O0(j.open),"data-radix-menu-content":"",dir:g.dir,...x,...h,ref:k,style:{outline:"none",...h.style},onKeyDown:z(h.onKeyDown,P=>{const _=P.target.closest("[data-radix-menu-content]")===P.currentTarget,B=P.ctrlKey||P.altKey||P.metaKey,Q=P.key.length===1;_&&(P.key==="Tab"&&P.preventDefault(),!B&&Q&&G(P.key));const re=C.current;if(P.target!==re||!lk.includes(P.key))return;P.preventDefault();const q=N().filter(H=>!H.disabled).map(H=>H.ref.current);m0.includes(P.key)&&q.reverse(),Tk(q)}),onBlur:z(e.onBlur,P=>{P.currentTarget.contains(P.target)||(window.clearTimeout(I.current),M.current="")}),onPointerMove:z(e.onPointerMove,ci(P=>{const L=P.target,_=A.current!==P.clientX;if(P.currentTarget.contains(L)&&_){const B=P.clientX>A.current?"right":"left";W.current=B,A.current=P.clientX}}))})})})})})})});y0.displayName=At;var wk="MenuGroup",Ef=f.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return o.jsx(Y.div,{role:"group",...r,ref:t})});Ef.displayName=wk;var jk="MenuLabel",w0=f.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return o.jsx(Y.div,{...r,ref:t})});w0.displayName=jk;var qa="MenuItem",Vp="menu.itemSelect",Dl=f.forwardRef((e,t)=>{const{disabled:n=!1,onSelect:r,...s}=e,i=f.useRef(null),a=Ni(qa,e.__scopeMenu),l=Sf(qa,e.__scopeMenu),c=me(t,i),u=f.useRef(!1),p=()=>{const m=i.current;if(!n&&m){const d=new CustomEvent(Vp,{bubbles:!0,cancelable:!0});m.addEventListener(Vp,v=>r==null?void 0:r(v),{once:!0}),Wd(m,d),d.defaultPrevented?u.current=!1:a.onClose()}};return o.jsx(j0,{...s,ref:c,disabled:n,onClick:z(e.onClick,p),onPointerDown:m=>{var d;(d=e.onPointerDown)==null||d.call(e,m),u.current=!0},onPointerUp:z(e.onPointerUp,m=>{var d;u.current||(d=m.currentTarget)==null||d.click()}),onKeyDown:z(e.onKeyDown,m=>{const d=l.searchRef.current!=="";n||d&&m.key===" "||Hu.includes(m.key)&&(m.currentTarget.click(),m.preventDefault())})})});Dl.displayName=qa;var j0=f.forwardRef((e,t)=>{const{__scopeMenu:n,disabled:r=!1,textValue:s,...i}=e,a=Sf(qa,n),l=h0(n),c=f.useRef(null),u=me(t,c),[p,m]=f.useState(!1),[d,v]=f.useState("");return f.useEffect(()=>{const w=c.current;w&&v((w.textContent??"").trim())},[i.children]),o.jsx(li.ItemSlot,{scope:n,disabled:r,textValue:s??d,children:o.jsx(r0,{asChild:!0,...l,focusable:!r,children:o.jsx(Y.div,{role:"menuitem","data-highlighted":p?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0,...i,ref:u,onPointerMove:z(e.onPointerMove,ci(w=>{r?a.onItemLeave(w):(a.onItemEnter(w),w.defaultPrevented||w.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:z(e.onPointerLeave,ci(w=>a.onItemLeave(w))),onFocus:z(e.onFocus,()=>m(!0)),onBlur:z(e.onBlur,()=>m(!1))})})})}),Nk="MenuCheckboxItem",N0=f.forwardRef((e,t)=>{const{checked:n=!1,onCheckedChange:r,...s}=e;return o.jsx(k0,{scope:e.__scopeMenu,checked:n,children:o.jsx(Dl,{role:"menuitemcheckbox","aria-checked":Xa(n)?"mixed":n,...s,ref:t,"data-state":Pf(n),onSelect:z(s.onSelect,()=>r==null?void 0:r(Xa(n)?!0:!n),{checkForDefaultPrevented:!1})})})});N0.displayName=Nk;var b0="MenuRadioGroup",[bk,Sk]=Jr(b0,{value:void 0,onValueChange:()=>{}}),S0=f.forwardRef((e,t)=>{const{value:n,onValueChange:r,...s}=e,i=Xe(r);return o.jsx(bk,{scope:e.__scopeMenu,value:n,onValueChange:i,children:o.jsx(Ef,{...s,ref:t})})});S0.displayName=b0;var C0="MenuRadioItem",E0=f.forwardRef((e,t)=>{const{value:n,...r}=e,s=Sk(C0,e.__scopeMenu),i=n===s.value;return o.jsx(k0,{scope:e.__scopeMenu,checked:i,children:o.jsx(Dl,{role:"menuitemradio","aria-checked":i,...r,ref:t,"data-state":Pf(i),onSelect:z(r.onSelect,()=>{var a;return(a=s.onValueChange)==null?void 0:a.call(s,n)},{checkForDefaultPrevented:!1})})})});E0.displayName=C0;var kf="MenuItemIndicator",[k0,Ck]=Jr(kf,{checked:!1}),P0=f.forwardRef((e,t)=>{const{__scopeMenu:n,forceMount:r,...s}=e,i=Ck(kf,n);return o.jsx(xr,{present:r||Xa(i.checked)||i.checked===!0,children:o.jsx(Y.span,{...s,ref:t,"data-state":Pf(i.checked)})})});P0.displayName=kf;var Ek="MenuSeparator",T0=f.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return o.jsx(Y.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});T0.displayName=Ek;var kk="MenuArrow",R0=f.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,s=Ol(n);return o.jsx(ff,{...s,...r,ref:t})});R0.displayName=kk;var Pk="MenuSub",[z5,M0]=Jr(Pk),ko="MenuSubTrigger",A0=f.forwardRef((e,t)=>{const n=Zr(ko,e.__scopeMenu),r=Ni(ko,e.__scopeMenu),s=M0(ko,e.__scopeMenu),i=Sf(ko,e.__scopeMenu),a=f.useRef(null),{pointerGraceTimerRef:l,onPointerGraceIntentChange:c}=i,u={__scopeMenu:e.__scopeMenu},p=f.useCallback(()=>{a.current&&window.clearTimeout(a.current),a.current=null},[]);return f.useEffect(()=>p,[p]),f.useEffect(()=>{const m=l.current;return()=>{window.clearTimeout(m),c(null)}},[l,c]),o.jsx(Nf,{asChild:!0,...u,children:o.jsx(j0,{id:s.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":s.contentId,"data-state":O0(n.open),...e,ref:xl(t,s.onTriggerChange),onClick:m=>{var d;(d=e.onClick)==null||d.call(e,m),!(e.disabled||m.defaultPrevented)&&(m.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:z(e.onPointerMove,ci(m=>{i.onItemEnter(m),!m.defaultPrevented&&!e.disabled&&!n.open&&!a.current&&(i.onPointerGraceIntentChange(null),a.current=window.setTimeout(()=>{n.onOpenChange(!0),p()},100))})),onPointerLeave:z(e.onPointerLeave,ci(m=>{var v,w;p();const d=(v=n.content)==null?void 0:v.getBoundingClientRect();if(d){const h=(w=n.content)==null?void 0:w.dataset.side,j=h==="right",g=j?-5:5,x=d[j?"left":"right"],y=d[j?"right":"left"];i.onPointerGraceIntentChange({area:[{x:m.clientX+g,y:m.clientY},{x,y:d.top},{x:y,y:d.top},{x:y,y:d.bottom},{x,y:d.bottom}],side:h}),window.clearTimeout(l.current),l.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(m),m.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:z(e.onKeyDown,m=>{var v;const d=i.searchRef.current!=="";e.disabled||d&&m.key===" "||ck[r.dir].includes(m.key)&&(n.onOpenChange(!0),(v=n.content)==null||v.focus(),m.preventDefault())})})})});A0.displayName=ko;var I0="MenuSubContent",_0=f.forwardRef((e,t)=>{const n=g0(At,e.__scopeMenu),{forceMount:r=n.forceMount,...s}=e,i=Zr(At,e.__scopeMenu),a=Ni(At,e.__scopeMenu),l=M0(I0,e.__scopeMenu),c=f.useRef(null),u=me(t,c);return o.jsx(li.Provider,{scope:e.__scopeMenu,children:o.jsx(xr,{present:r||i.open,children:o.jsx(li.Slot,{scope:e.__scopeMenu,children:o.jsx(Cf,{id:l.contentId,"aria-labelledby":l.triggerId,...s,ref:u,align:"start",side:a.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:p=>{var m;a.isUsingKeyboardRef.current&&((m=c.current)==null||m.focus()),p.preventDefault()},onCloseAutoFocus:p=>p.preventDefault(),onFocusOutside:z(e.onFocusOutside,p=>{p.target!==l.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:z(e.onEscapeKeyDown,p=>{a.onClose(),p.preventDefault()}),onKeyDown:z(e.onKeyDown,p=>{var v;const m=p.currentTarget.contains(p.target),d=uk[a.dir].includes(p.key);m&&d&&(i.onOpenChange(!1),(v=l.trigger)==null||v.focus(),p.preventDefault())})})})})})});_0.displayName=I0;function O0(e){return e?"open":"closed"}function Xa(e){return e==="indeterminate"}function Pf(e){return Xa(e)?"indeterminate":e?"checked":"unchecked"}function Tk(e){const t=document.activeElement;for(const n of e)if(n===t||(n.focus(),document.activeElement!==t))return}function Rk(e,t){return e.map((n,r)=>e[(t+r)%e.length])}function Mk(e,t,n){const s=t.length>1&&Array.from(t).every(u=>u===t[0])?t[0]:t,i=n?e.indexOf(n):-1;let a=Rk(e,Math.max(i,0));s.length===1&&(a=a.filter(u=>u!==n));const c=a.find(u=>u.toLowerCase().startsWith(s.toLowerCase()));return c!==n?c:void 0}function Ak(e,t){const{x:n,y:r}=e;let s=!1;for(let i=0,a=t.length-1;i<t.length;a=i++){const l=t[i].x,c=t[i].y,u=t[a].x,p=t[a].y;c>r!=p>r&&n<(u-l)*(r-c)/(p-c)+l&&(s=!s)}return s}function Ik(e,t){if(!t)return!1;const n={x:e.clientX,y:e.clientY};return Ak(n,t)}function ci(e){return t=>t.pointerType==="mouse"?e(t):void 0}var _k=x0,Ok=Nf,Dk=v0,Lk=y0,Fk=Ef,zk=w0,$k=Dl,Uk=N0,Bk=S0,Vk=E0,Hk=P0,Wk=T0,Kk=R0,Gk=A0,Qk=_0,Tf="DropdownMenu",[Yk,$5]=eo(Tf,[p0]),it=p0(),[qk,D0]=Yk(Tf),L0=e=>{const{__scopeDropdownMenu:t,children:n,dir:r,open:s,defaultOpen:i,onOpenChange:a,modal:l=!0}=e,c=it(t),u=f.useRef(null),[p=!1,m]=Ks({prop:s,defaultProp:i,onChange:a});return o.jsx(qk,{scope:t,triggerId:Ur(),triggerRef:u,contentId:Ur(),open:p,onOpenChange:m,onOpenToggle:f.useCallback(()=>m(d=>!d),[m]),modal:l,children:o.jsx(_k,{...c,open:p,onOpenChange:m,dir:r,modal:l,children:n})})};L0.displayName=Tf;var F0="DropdownMenuTrigger",z0=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,disabled:r=!1,...s}=e,i=D0(F0,n),a=it(n);return o.jsx(Ok,{asChild:!0,...a,children:o.jsx(Y.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...s,ref:xl(t,i.triggerRef),onPointerDown:z(e.onPointerDown,l=>{!r&&l.button===0&&l.ctrlKey===!1&&(i.onOpenToggle(),i.open||l.preventDefault())}),onKeyDown:z(e.onKeyDown,l=>{r||(["Enter"," "].includes(l.key)&&i.onOpenToggle(),l.key==="ArrowDown"&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(l.key)&&l.preventDefault())})})})});z0.displayName=F0;var Xk="DropdownMenuPortal",$0=e=>{const{__scopeDropdownMenu:t,...n}=e,r=it(t);return o.jsx(Dk,{...r,...n})};$0.displayName=Xk;var U0="DropdownMenuContent",B0=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,s=D0(U0,n),i=it(n),a=f.useRef(!1);return o.jsx(Lk,{id:s.contentId,"aria-labelledby":s.triggerId,...i,...r,ref:t,onCloseAutoFocus:z(e.onCloseAutoFocus,l=>{var c;a.current||(c=s.triggerRef.current)==null||c.focus(),a.current=!1,l.preventDefault()}),onInteractOutside:z(e.onInteractOutside,l=>{const c=l.detail.originalEvent,u=c.button===0&&c.ctrlKey===!0,p=c.button===2||u;(!s.modal||p)&&(a.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});B0.displayName=U0;var Jk="DropdownMenuGroup",Zk=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,s=it(n);return o.jsx(Fk,{...s,...r,ref:t})});Zk.displayName=Jk;var eP="DropdownMenuLabel",V0=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,s=it(n);return o.jsx(zk,{...s,...r,ref:t})});V0.displayName=eP;var tP="DropdownMenuItem",H0=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,s=it(n);return o.jsx($k,{...s,...r,ref:t})});H0.displayName=tP;var nP="DropdownMenuCheckboxItem",W0=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,s=it(n);return o.jsx(Uk,{...s,...r,ref:t})});W0.displayName=nP;var rP="DropdownMenuRadioGroup",sP=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,s=it(n);return o.jsx(Bk,{...s,...r,ref:t})});sP.displayName=rP;var oP="DropdownMenuRadioItem",K0=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,s=it(n);return o.jsx(Vk,{...s,...r,ref:t})});K0.displayName=oP;var iP="DropdownMenuItemIndicator",G0=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,s=it(n);return o.jsx(Hk,{...s,...r,ref:t})});G0.displayName=iP;var aP="DropdownMenuSeparator",Q0=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,s=it(n);return o.jsx(Wk,{...s,...r,ref:t})});Q0.displayName=aP;var lP="DropdownMenuArrow",cP=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,s=it(n);return o.jsx(Kk,{...s,...r,ref:t})});cP.displayName=lP;var uP="DropdownMenuSubTrigger",Y0=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,s=it(n);return o.jsx(Gk,{...s,...r,ref:t})});Y0.displayName=uP;var dP="DropdownMenuSubContent",q0=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,s=it(n);return o.jsx(Qk,{...s,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});q0.displayName=dP;var fP=L0,mP=z0,pP=$0,X0=B0,J0=V0,Z0=H0,ey=W0,ty=K0,ny=G0,ry=Q0,sy=Y0,oy=q0;const hP=fP,xP=mP,gP=f.forwardRef(({className:e,inset:t,children:n,...r},s)=>o.jsxs(sy,{ref:s,className:ee("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",t&&"pl-8",e),...r,children:[n,o.jsx(BN,{className:"ml-auto h-4 w-4"})]}));gP.displayName=sy.displayName;const vP=f.forwardRef(({className:e,...t},n)=>o.jsx(oy,{ref:n,className:ee("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t}));vP.displayName=oy.displayName;const iy=f.forwardRef(({className:e,sideOffset:t=4,...n},r)=>o.jsx(pP,{children:o.jsx(X0,{ref:r,sideOffset:t,className:ee("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n})}));iy.displayName=X0.displayName;const ay=f.forwardRef(({className:e,inset:t,...n},r)=>o.jsx(Z0,{ref:r,className:ee("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t&&"pl-8",e),...n}));ay.displayName=Z0.displayName;const yP=f.forwardRef(({className:e,children:t,checked:n,...r},s)=>o.jsxs(ey,{ref:s,className:ee("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:n,...r,children:[o.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:o.jsx(ny,{children:o.jsx(Nl,{className:"h-4 w-4"})})}),t]}));yP.displayName=ey.displayName;const wP=f.forwardRef(({className:e,children:t,...n},r)=>o.jsxs(ty,{ref:r,className:ee("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...n,children:[o.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:o.jsx(ny,{children:o.jsx(HN,{className:"h-2 w-2 fill-current"})})}),t]}));wP.displayName=ty.displayName;const jP=f.forwardRef(({className:e,inset:t,...n},r)=>o.jsx(J0,{ref:r,className:ee("px-2 py-1.5 text-sm font-semibold",t&&"pl-8",e),...n}));jP.displayName=J0.displayName;const NP=f.forwardRef(({className:e,...t},n)=>o.jsx(ry,{ref:n,className:ee("-mx-1 my-1 h-px bg-muted",e),...t}));NP.displayName=ry.displayName;function Rf({userEmail:e,onLogout:t}){const[n,r]=f.useState(!1);return o.jsxs(hP,{open:n,onOpenChange:r,children:[o.jsx(xP,{asChild:!0,children:o.jsxs(V,{variant:"ghost",className:"flex items-center gap-2 text-primary",children:[o.jsx(Jd,{className:"h-4 w-4"}),o.jsx("span",{className:"hidden md:inline",children:e})]})}),o.jsx(iy,{align:"end",className:"bg-white",children:o.jsxs(ay,{onClick:t,className:"cursor-pointer flex items-center gap-2",children:[o.jsx(WN,{className:"h-4 w-4"}),"Logout"]})})]})}function Vr({className:e}){return o.jsx("div",{className:ee("flex items-center",e),children:o.jsx("h1",{className:"text-primary font-bold text-xl",children:"MyDigitalColleague"})})}function fn({children:e}){const{user:t,logout:n}=Xr(),r=Mn(),s=a=>r.pathname===a,i=[{name:"Dashboard",href:"/dashboard",icon:Kg},{name:"Job Roles",href:"/job-roles",icon:qd},{name:"Candidates",href:"/candidates",icon:Ua},{name:"Interviews",href:"/interviews",icon:jl},{name:"Activity Log",href:"/activity",icon:UN},{name:"Alex Config",href:"/config",icon:Xd}];return o.jsxs("div",{className:"min-h-screen bg-gray-50",children:[o.jsx("header",{className:"bg-white shadow-sm z-10 relative",children:o.jsx("div",{className:"container-page py-4",children:o.jsxs("div",{className:"flex justify-between items-center",children:[o.jsx(be,{to:"/dashboard",className:"flex items-center",children:o.jsx(Vr,{})}),t&&o.jsx(Rf,{userEmail:t.email,onLogout:n})]})})}),o.jsxs("div",{className:"flex",children:[o.jsx("aside",{className:"hidden md:flex flex-col w-64 bg-white border-r min-h-[calc(100vh-64px)]",children:o.jsx("nav",{className:"flex flex-col flex-1 pt-5",children:i.map(a=>o.jsxs(be,{to:a.href,className:`flex items-center px-4 py-2 mx-3 my-1 text-sm font-medium rounded-md ${s(a.href)?"bg-primary text-white":"text-primary hover:bg-gray-100"}`,children:[o.jsx(a.icon,{className:"mr-3 h-5 w-5","aria-hidden":"true"}),a.name]},a.name))})}),o.jsx("div",{className:"md:hidden fixed bottom-0 left-0 right-0 bg-white border-t z-10",children:o.jsx("div",{className:"grid grid-cols-5 gap-1",children:i.slice(0,5).map(a=>o.jsxs(be,{to:a.href,className:`flex flex-col items-center justify-center py-2 ${s(a.href)?"text-primary":"text-gray-500"}`,children:[o.jsx(a.icon,{className:"h-5 w-5","aria-hidden":"true"}),o.jsx("span",{className:"text-xs mt-1",children:a.name})]},a.name))})}),o.jsx("main",{className:"flex-1 min-w-0",children:o.jsx("div",{className:"container-page",children:e})})]})]})}function wo({children:e}){const{user:t,logout:n}=Xr(),r=Mn(),s=a=>r.pathname===a,i=[{name:"Dashboard",href:"/admin/dashboard",icon:Kg},{name:"Users & Subscriptions",href:"/admin/users",icon:Ua},{name:"Account Management",href:"/admin/accounts",icon:Xd},{name:"Twilio Numbers",href:"/admin/twilio",icon:bn},{name:"Issues",href:"/admin/issues",icon:Tu}];return o.jsxs("div",{className:"min-h-screen bg-gray-50",children:[o.jsx("header",{className:"bg-white shadow-sm z-10 relative",children:o.jsx("div",{className:"container-page py-4",children:o.jsxs("div",{className:"flex justify-between items-center",children:[o.jsxs(be,{to:"/admin/dashboard",className:"flex items-center",children:[o.jsx(Vr,{}),o.jsx("span",{className:"ml-2 text-primary font-semibold",children:"Admin"})]}),t&&o.jsx(Rf,{userEmail:t.email,onLogout:n})]})})}),o.jsxs("div",{className:"flex",children:[o.jsx("aside",{className:"hidden md:flex flex-col w-64 bg-white border-r min-h-[calc(100vh-64px)]",children:o.jsx("nav",{className:"flex flex-col flex-1 pt-5",children:i.map(a=>o.jsxs(be,{to:a.href,className:`flex items-center px-4 py-2 mx-3 my-1 text-sm font-medium rounded-md ${s(a.href)?"bg-primary text-white":"text-primary hover:bg-gray-100"}`,children:[o.jsx(a.icon,{className:"mr-3 h-5 w-5","aria-hidden":"true"}),a.name]},a.name))})}),o.jsx("div",{className:"md:hidden fixed bottom-0 left-0 right-0 bg-white border-t z-10",children:o.jsx("div",{className:"grid grid-cols-5 gap-1",children:i.map(a=>o.jsxs(be,{to:a.href,className:`flex flex-col items-center justify-center py-2 ${s(a.href)?"text-primary":"text-gray-500"}`,children:[o.jsx(a.icon,{className:"h-5 w-5","aria-hidden":"true"}),o.jsx("span",{className:"text-xs mt-1",children:a.name})]},a.name))})}),o.jsx("main",{className:"flex-1 min-w-0",children:o.jsx("div",{className:"container-page",children:e})})]})]})}const Be=f.forwardRef>(({className:e,type:t,...n},r)=>o.jsx("input",{type:t,className:ee("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:r,...n}));Be.displayName="Input";var bP="Label",ly=f.forwardRef((e,t)=>o.jsx(Y.label,{...e,ref:t,onMouseDown:n=>{var s;n.target.closest("button, input, select, textarea")||((s=e.onMouseDown)==null||s.call(e,n),!n.defaultPrevented&&n.detail>1&&n.preventDefault())}}));ly.displayName=bP;var cy=ly;const SP=Yd("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),Bt=f.forwardRef(({className:e,...t},n)=>o.jsx(cy,{ref:n,className:ee(SP(),e),...t}));Bt.displayName=cy.displayName;const ce=f.forwardRef(({className:e,...t},n)=>o.jsx("div",{ref:n,className:ee("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));ce.displayName="Card";const Le=f.forwardRef(({className:e,...t},n)=>o.jsx("div",{ref:n,className:ee("flex flex-col space-y-1.5 p-6",e),...t}));Le.displayName="CardHeader";const Fe=f.forwardRef(({className:e,...t},n)=>o.jsx("h3",{ref:n,className:ee("text-2xl font-semibold leading-none tracking-tight",e),...t}));Fe.displayName="CardTitle";const qs=f.forwardRef(({className:e,...t},n)=>o.jsx("p",{ref:n,className:ee("text-sm text-muted-foreground",e),...t}));qs.displayName="CardDescription";const Me=f.forwardRef(({className:e,...t},n)=>o.jsx("div",{ref:n,className:ee("p-6 pt-0",e),...t}));Me.displayName="CardContent";const ui=f.forwardRef(({className:e,...t},n)=>o.jsx("div",{ref:n,className:ee("flex items-center p-6 pt-0",e),...t}));ui.displayName="CardFooter";const CP=()=>{const[e,t]=f.useState(""),[n,r]=f.useState(""),{login:s,demoLogin:i,isLoading:a,user:l}=Xr(),c=qr();l&&(l.role==="admin"?c("/admin/dashboard"):c("/dashboard"));const u=d=>{d.preventDefault(),s(e,n)},p=()=>{i()},m=[{name:"Voucher",price:"$25",description:"for 5 calls",features:["One-time purchase","No expiry","$5 per call","Basic analytics"],buttonText:"Get Voucher",popular:!1},{name:"Monthly Basic",price:"$50",description:"per month",features:["120 calls included","Full analytics","Email and phone support","Export reports"],buttonText:"Subscribe",popular:!0},{name:"Monthly Pro",price:"$200",description:"per month",features:["500 calls included","Advanced analytics","Priority support","Custom integrations"],buttonText:"Subscribe Pro",popular:!1}];return o.jsxs("div",{className:"min-h-screen flex flex-col",children:[o.jsx("header",{className:"bg-white shadow-sm z-10 relative",children:o.jsx("div",{className:"container-page py-4",children:o.jsxs("div",{className:"flex justify-between items-center",children:[o.jsx(Vr,{}),o.jsxs("div",{className:"flex items-center gap-4",children:[o.jsx("a",{href:"#pricing",className:"text-primary font-medium",children:"Pricing"}),o.jsx("a",{href:"#login",className:"text-primary font-medium",children:"Login"})]})]})})}),o.jsx("section",{className:"bg-primary py-24",children:o.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:o.jsxs("div",{className:"flex flex-col md:flex-row items-center justify-between",children:[o.jsxs("div",{className:"md:w-1/2 mb-8 md:mb-0",children:[o.jsx("h1",{className:"text-4xl md:text-5xl font-bold text-white mb-4 animate-fade-in",children:"Meet Alex the Interviewer"}),o.jsx("p",{className:"text-lg md:text-xl text-white/80 mb-8 animate-fade-in",children:"Your AI-powered assistant for automating L1 candidate screening. Alex conducts interviews via calls, schedules candidates, and delivers detailed performance metrics to streamline hiring."}),o.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 animate-fade-in",children:[o.jsx(V,{size:"lg",className:"bg-white text-primary hover:bg-white/90",onClick:()=>{var d;return(d=document.getElementById("login"))==null?void 0:d.scrollIntoView({behavior:"smooth"})},children:"Get Started"}),o.jsx(V,{size:"lg",variant:"outline",className:"border-white text-white hover:bg-white/10",onClick:()=>{var d;return(d=document.getElementById("pricing"))==null?void 0:d.scrollIntoView({behavior:"smooth"})},children:"View Pricing"})]})]}),o.jsx("div",{className:"md:w-1/2 flex justify-center",children:o.jsx("div",{className:"relative w-64 h-64 md:w-80 md:h-80 bg-white rounded-full flex items-center justify-center shadow-lg animate-fade-in",children:o.jsx("div",{className:"absolute inset-2 bg-secondary rounded-full flex items-center justify-center",children:o.jsx("span",{className:"text-4xl md:text-5xl font-bold text-white",children:"Alex"})})})})]})})}),o.jsx("section",{className:"py-24 bg-gray-50",children:o.jsxs("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[o.jsx("h2",{className:"text-3xl font-bold text-center mb-12 text-text",children:"How Alex Helps You"}),o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[o.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[o.jsx("div",{className:"w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center text-primary mb-4",children:o.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),o.jsx("h3",{className:"text-xl font-semibold mb-2 text-text",children:"Save Time"}),o.jsx("p",{className:"text-gray-600",children:"Automate the entire L1 interview process, from scheduling to evaluation, saving your team valuable hours."})]}),o.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[o.jsx("div",{className:"w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center text-primary mb-4",children:o.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})})}),o.jsx("h3",{className:"text-xl font-semibold mb-2 text-text",children:"Consistent Screening"}),o.jsx("p",{className:"text-gray-600",children:"Ensure every candidate gets the same fair evaluation with standardized questions and objective scoring."})]}),o.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[o.jsx("div",{className:"w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center text-primary mb-4",children:o.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})})}),o.jsx("h3",{className:"text-xl font-semibold mb-2 text-text",children:"Data-Driven Decisions"}),o.jsx("p",{className:"text-gray-600",children:"Get detailed metrics and insights on candidate performance to make informed hiring decisions."})]})]})]})}),o.jsx("section",{id:"pricing",className:"py-24",children:o.jsxs("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[o.jsx("h2",{className:"text-3xl font-bold text-center mb-4 text-text",children:"Simple, Transparent Pricing"}),o.jsx("p",{className:"text-xl text-center mb-12 text-gray-600",children:"Choose the plan that works for your hiring needs"}),o.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:m.map((d,v)=>o.jsxs(ce,{className:`relative overflow-hidden ${d.popular?"border-primary shadow-lg":""}`,children:[d.popular&&o.jsx("div",{className:"absolute top-0 right-0 bg-primary text-white px-4 py-1 text-sm font-medium",children:"Popular"}),o.jsxs(Le,{children:[o.jsx(Fe,{className:"text-2xl",children:d.name}),o.jsxs(qs,{children:[o.jsx("span",{className:"text-3xl font-bold text-text",children:d.price}),o.jsxs("span",{className:"text-gray-500",children:[" ",d.description]})]})]}),o.jsx(Me,{children:o.jsx("ul",{className:"space-y-2",children:d.features.map((w,h)=>o.jsxs("li",{className:"flex items-start",children:[o.jsx(Nl,{className:"h-5 w-5 text-green-500 mr-2 shrink-0"}),o.jsx("span",{children:w})]},h))})}),o.jsx(ui,{children:o.jsx(V,{className:`w-full ${d.popular?"bg-primary hover:bg-primary/90":""}`,onClick:()=>{var w;return(w=document.getElementById("login"))==null?void 0:w.scrollIntoView({behavior:"smooth"})},children:d.buttonText})})]},v))})]})}),o.jsx("section",{id:"login",className:"py-24 bg-gray-50",children:o.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:o.jsxs("div",{className:"max-w-md mx-auto",children:[o.jsx("h2",{className:"text-3xl font-bold text-center mb-8 text-text",children:"Login to Your Account"}),o.jsx(ce,{children:o.jsxs("form",{onSubmit:u,children:[o.jsxs(Me,{className:"pt-6 space-y-4",children:[o.jsxs("div",{className:"space-y-2",children:[o.jsx(Bt,{htmlFor:"email",children:"Email"}),o.jsx(Be,{id:"email",type:"email",placeholder:"<EMAIL>",value:e,onChange:d=>t(d.target.value),required:!0})]}),o.jsxs("div",{className:"space-y-2",children:[o.jsx(Bt,{htmlFor:"password",children:"Password"}),o.jsx(Be,{id:"password",type:"password",placeholder:"Password",value:n,onChange:d=>r(d.target.value),required:!0})]}),o.jsxs("div",{className:"text-sm text-gray-500",children:[o.jsx("p",{children:"Demo credentials: <EMAIL> / User123!"}),o.jsx("p",{children:"Admin: <EMAIL> / Admin123!"})]})]}),o.jsxs(ui,{className:"flex flex-col space-y-4",children:[o.jsx(V,{type:"submit",className:"w-full bg-primary hover:bg-primary/90",disabled:a,children:a?"Logging in...":"Login"}),o.jsx(V,{type:"button",variant:"outline",className:"w-full",onClick:p,disabled:a,children:"Try Demo"})]})]})})]})})}),o.jsx("footer",{className:"bg-primary text-white py-12",children:o.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:o.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[o.jsx(Vr,{className:"text-white mb-4 md:mb-0"}),o.jsxs("div",{className:"text-center md:text-right",children:[o.jsx("p",{children:"© 2025 Alex Interviewer. All rights reserved."}),o.jsx("p",{className:"text-white/70",children:"Alex Interviewer - AI-Powered Interview Platform"})]})]})})})]})},EP=()=>{const e=Mn();return f.useEffect(()=>{console.error("404 Error: User attempted to access non-existent route:",e.pathname)},[e.pathname]),o.jsxs("div",{className:"min-h-screen bg-gray-50 flex flex-col",children:[o.jsx("header",{className:"bg-white shadow-sm z-10 relative",children:o.jsx("div",{className:"container-page py-4",children:o.jsx(Vr,{})})}),o.jsx("div",{className:"flex-grow flex items-center justify-center",children:o.jsxs("div",{className:"text-center max-w-lg px-4",children:[o.jsx("div",{className:"text-primary text-6xl font-bold mb-4",children:"404"}),o.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-3",children:"Page Not Found"}),o.jsx("p",{className:"text-gray-600 mb-8",children:"We couldn't find the page you were looking for. It might have been moved or doesn't exist."}),o.jsxs("div",{className:"flex justify-center gap-4",children:[o.jsx(V,{className:"bg-primary hover:bg-primary/90",onClick:()=>window.history.back(),children:"Go Back"}),o.jsx(V,{variant:"outline",onClick:()=>window.location.href="/",children:"Go to Home"})]})]})}),o.jsx("footer",{className:"bg-white border-t py-6",children:o.jsx("div",{className:"container-page",children:o.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[o.jsx(Vr,{}),o.jsx("div",{className:"text-center md:text-right mt-4 md:mt-0",children:o.jsx("p",{className:"text-sm text-gray-500",children:"© 2025 Alex Interviewer. All rights reserved."})})]})})})]})};function ft({title:e,description:t,actions:n}){return o.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 gap-4",children:[o.jsxs("div",{children:[o.jsx("h1",{className:"text-2xl font-bold text-text",children:e}),t&&o.jsx("p",{className:"text-muted-foreground mt-1",children:t})]}),n&&o.jsx("div",{className:"mt-2 sm:mt-0",children:n})]})}function kP(e,t=[]){let n=[];function r(i,a){const l=f.createContext(a),c=n.length;n=[...n,a];function u(m){const{scope:d,children:v,...w}=m,h=(d==null?void 0:d[e][c])||l,j=f.useMemo(()=>w,Object.values(w));return o.jsx(h.Provider,{value:j,children:v})}function p(m,d){const v=(d==null?void 0:d[e][c])||l,w=f.useContext(v);if(w)return w;if(a!==void 0)return a;throw new Error(`\`${m}\` must be used within \`${i}\``)}return u.displayName=i+"Provider",[u,p]}const s=()=>{const i=n.map(a=>f.createContext(a));return function(l){const c=(l==null?void 0:l[e])||i;return f.useMemo(()=>({[`__scope${e}`]:{...l,[e]:c}}),[l,c])}};return s.scopeName=e,[r,PP(s,...t)]}function PP(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(s=>({useScope:s(),scopeName:s.scopeName}));return function(i){const a=r.reduce((l,{useScope:c,scopeName:u})=>{const m=c(i)[`__scope${u}`];return{...l,...m}},{});return f.useMemo(()=>({[`__scope${t.scopeName}`]:a}),[a])}};return n.scopeName=t.scopeName,n}var Mf="Progress",Af=100,[TP,U5]=kP(Mf),[RP,MP]=TP(Mf),uy=f.forwardRef((e,t)=>{const{__scopeProgress:n,value:r=null,max:s,getValueLabel:i=AP,...a}=e;(s||s===0)&&!Hp(s)&&console.error(IP(`${s}`,"Progress"));const l=Hp(s)?s:Af;r!==null&&!Wp(r,l)&&console.error(_P(`${r}`,"Progress"));const c=Wp(r,l)?r:null,u=Ja(c)?i(c,l):void 0;return o.jsx(RP,{scope:n,value:c,max:l,children:o.jsx(Y.div,{"aria-valuemax":l,"aria-valuemin":0,"aria-valuenow":Ja(c)?c:void 0,"aria-valuetext":u,role:"progressbar","data-state":my(c,l),"data-value":c??void 0,"data-max":l,...a,ref:t})})});uy.displayName=Mf;var dy="ProgressIndicator",fy=f.forwardRef((e,t)=>{const{__scopeProgress:n,...r}=e,s=MP(dy,n);return o.jsx(Y.div,{"data-state":my(s.value,s.max),"data-value":s.value??void 0,"data-max":s.max,...r,ref:t})});fy.displayName=dy;function AP(e,t){return`${Math.round(e/t*100)}%`}function my(e,t){return e==null?"indeterminate":e===t?"complete":"loading"}function Ja(e){return typeof e=="number"}function Hp(e){return Ja(e)&&!isNaN(e)&&e>0}function Wp(e,t){return Ja(e)&&!isNaN(e)&&e<=t&&e>=0}function IP(e,t){return`Invalid prop \`max\` of value \`${e}\` supplied to \`${t}\`. Only numbers greater than 0 are valid max values. Defaulting to \`${Af}\`.`}function _P(e,t){return`Invalid prop \`value\` of value \`${e}\` supplied to \`${t}\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or ${Af} if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`}var py=uy,OP=fy;const Ts=f.forwardRef(({className:e,value:t,...n},r)=>o.jsx(py,{ref:r,className:ee("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...n,children:o.jsx(OP,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})}));Ts.displayName=py.displayName;const En=[{id:"1",name:"Software Engineer",duties:"Develop web applications using modern technologies. Collaborate with cross-functional teams to define and design new features.",skills:["Python","JavaScript","React","Teamwork"],education:"BS Computer Science",experience:3,criteria:"Strong Python skills, 3+ years experience, good communication",createdAt:"2025-05-01"},{id:"2",name:"Data Analyst",duties:"Analyze business data to derive meaningful insights. Create dashboards and reports for stakeholders.",skills:["SQL","Excel","Tableau","Communication"],education:"BA Statistics",experience:2,criteria:"Proficient in SQL, 2+ years experience, analytical mindset",createdAt:"2025-05-05"},{id:"3",name:"Product Manager",duties:"Define product vision and strategy. Work with development teams to deliver features.",skills:["Product Strategy","Agile","Leadership","Communication"],education:"MBA or equivalent",experience:5,criteria:"Experience in product development, excellent communication skills",createdAt:"2025-05-10"}],Tt=[{id:"1",name:"John Doe",email:"<EMAIL>",phone:"******-123-4567",jobRoleId:"1",experience:3,education:"BS Computer Science, MIT, 2020",skills:["Python","JavaScript","React","Node.js"],projects:["Developed CRM system using Python/Django","Built e-commerce platform with React"],otherDetails:"AWS Certified Developer",contactStatus:"Emailed",interviewStatus:"Completed",interviewResult:"Pass",scheduleDateTime:"2025-05-21T10:00:00",metrics:{communication:8,skills:7,responseQuality:9,overallScore:80},agentNotes:"Candidate demonstrated strong technical knowledge but hesitated on JavaScript questions.",communicationHistory:{emails:[{date:"2025-05-20",subject:"Interview Schedule",status:"Sent"}],calls:[{date:"2025-05-21",response:"Confirmed availability"}]},contactOutcome:"Confirmed for 2025-05-22, 10:00 AM",audioUrl:"john_doe_20250521.mp3",createdAt:"2025-05-15"},{id:"2",name:"Jane Smith",email:"<EMAIL>",phone:"******-987-6543",jobRoleId:"2",experience:2,education:"BA Statistics, Stanford, 2021",skills:["SQL","Excel","Tableau","Python"],projects:["Built sales analytics dashboard","Customer segmentation analysis"],otherDetails:"Tableau Certified Associate",contactStatus:"Called",interviewStatus:"Scheduled",interviewResult:"N/A",scheduleDateTime:"2025-05-22T14:00:00",communicationHistory:{emails:[],calls:[{date:"2025-05-21",response:"Scheduled interview"}]},contactOutcome:"Scheduled for 2025-05-22, 14:00",createdAt:"2025-05-16"},{id:"3",name:"Alice Brown",email:"<EMAIL>",phone:"******-111-2222",jobRoleId:"1",experience:4,education:"MS Computer Engineering, Berkeley, 2019",skills:["JavaScript","TypeScript","React","AWS"],projects:["Built serverless application on AWS","Developed CI/CD pipeline"],otherDetails:"Open source contributor",contactStatus:"Not Contacted",interviewStatus:"Not Scheduled",interviewResult:"N/A",createdAt:"2025-05-17"},{id:"4",name:"Bob Johnson",email:"<EMAIL>",phone:"******-333-4444",jobRoleId:"3",experience:6,education:"MBA, Harvard, 2018",skills:["Product Management","Leadership","Agile","Strategy"],projects:["Led development of fintech product","Launched mobile app with 1M users"],otherDetails:"Certified Scrum Master",contactStatus:"Emailed",interviewStatus:"Completed",interviewResult:"Fail",scheduleDateTime:"2025-05-19T11:00:00",metrics:{communication:6,skills:5,responseQuality:6,overallScore:55},agentNotes:"Candidate lacked depth in technical understanding of product development.",communicationHistory:{emails:[{date:"2025-05-18",subject:"Interview Schedule",status:"Sent"}],calls:[{date:"2025-05-19",response:"Confirmed availability"}]},contactOutcome:"Interview completed, not selected",audioUrl:"bob_johnson_20250519.mp3",createdAt:"2025-05-18"}],DP=[{id:"1",candidateId:"1",question:"Describe a Python project you recently worked on.",answer:"I built a CRM system using Django, integrated with AWS S3 for file storage. It included features like customer segmentation, email campaigns, and reporting dashboards."},{id:"2",candidateId:"1",question:"How do you handle team conflicts?",answer:"I use active listening to understand all perspectives and mediate solutions that work for everyone. In my last role, I resolved a conflict between design and development by creating a shared understanding of priorities."},{id:"3",candidateId:"1",question:"What JavaScript frameworks have you used?",answer:"I have used React for the past 2 years, and have some experience with Vue. I prefer React because of its component-based architecture and the ecosystem around it."},{id:"4",candidateId:"4",question:"Describe your approach to product strategy.",answer:"I start with customer research to identify pain points, then validate solutions with prototypes before full development. I use data to measure success and iterate based on feedback."},{id:"5",candidateId:"4",question:"How do you prioritize features?",answer:"I use a framework that considers business value, customer impact, and effort. I create a weighted score for each feature and discuss with stakeholders to ensure alignment."}],ys=[{id:"1",name:"Jane Smith",email:"<EMAIL>",plan:"Basic",callsUsed:50,callsTotal:120,status:"Active",lastReset:"2025-05-20"},{id:"2",name:"John Doe",email:"<EMAIL>",plan:"Voucher",callsUsed:2,callsTotal:5,status:"Active"},{id:"3",name:"Sarah Johnson",email:"<EMAIL>",plan:"Pro",callsUsed:150,callsTotal:500,status:"Active"}],Kp=[{id:"1",userId:"1",userName:"Jane Smith",invoiceNumber:"INV001",amount:50,status:"Paid",date:"2025-05-01"},{id:"2",userId:"2",userName:"John Doe",invoiceNumber:"INV002",amount:25,status:"Paid",date:"2025-05-02"},{id:"3",userId:"1",userName:"Jane Smith",invoiceNumber:"INV003",amount:150,status:"Paid",date:"2025-05-10"}],jo=[{id:"1",number:"******-123-4567",assignedTo:"Jane Smith",date:"2025-05-01"},{id:"2",number:"******-987-6543",assignedTo:"John Doe",date:"2025-05-02"},{id:"3",number:"******-456-7890",assignedTo:"Sarah Johnson",date:"2025-05-05"}],LP=[{id:"ISS001",userId:"1",userName:"Jane Smith",description:"Login failed multiple times",priority:"High",date:"2025-05-20",status:"Resolved"},{id:"ISS002",userId:"2",userName:"John Doe",description:"Payment declined",priority:"Medium",date:"2025-05-21",status:"Pending"},{id:"ISS003",userId:"3",userName:"Sarah Johnson",description:"Call quality issues",priority:"Low",date:"2025-05-22",status:"Pending"}],FP=[{id:"1",timestamp:"2025-05-20 10:00",action:"Emailed",details:"Sent interview schedule",candidateId:"1",candidateName:"John Doe"},{id:"2",timestamp:"2025-05-21 09:00",action:"Called",details:"Confirmed availability",candidateId:"2",candidateName:"Jane Smith"},{id:"3",timestamp:"2025-05-21 10:30",action:"Interview Completed",details:"Score: 80/100, Pass",candidateId:"1",candidateName:"John Doe"},{id:"4",timestamp:"2025-05-19 11:30",action:"Interview Completed",details:"Score: 55/100, Fail",candidateId:"4",candidateName:"Bob Johnson"},{id:"5",timestamp:"2025-05-22 08:15",action:"Scheduled Interview",details:"Set for 2025-05-22, 14:00",candidateId:"2",candidateName:"Jane Smith"}],Et={email:{provider:"Gmail",user:"<EMAIL>",token:"mock-token-123"},agentName:"Alex",twilioNumber:"******-123-4567",assignedDate:"2025-05-01"},zP=()=>{const{user:e}=Xr(),t=Tt.length,n=Tt.filter(u=>u.interviewStatus==="Completed").length,r=Tt.filter(u=>u.interviewResult==="Pass").length,s=Tt.filter(u=>u.interviewResult==="Fail").length,i=50,a=120,l=Math.round(i/a*100),c=[{invoiceNumber:"INV001",amount:"$50.00",status:"Paid",date:"2025-05-01"},{invoiceNumber:"INV003",amount:"$150.00",status:"Paid",date:"2025-05-10"}];return o.jsxs("div",{children:[o.jsx(ft,{title:`Welcome, ${e==null?void 0:e.email.split("@")[0]}`,description:"Here's an overview of your account",actions:o.jsx(V,{className:"bg-primary hover:bg-primary/90",children:"Manage Subscription"})}),o.jsxs("div",{className:"mb-6 p-4 bg-amber-50 border border-amber-200 rounded-md text-amber-800 flex items-center justify-between",children:[o.jsxs("div",{className:"flex items-center",children:[o.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})}),o.jsx("span",{children:"Your Basic subscription is ending on 2025-05-31."})]}),o.jsx(V,{variant:"ghost",size:"sm",className:"text-amber-800",children:"Renew Now"})]}),o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[o.jsxs(ce,{className:"p-6",children:[o.jsx("h3",{className:"text-lg font-medium mb-4",children:"Usage"}),o.jsxs("div",{className:"mb-2 flex justify-between items-center",children:[o.jsx("span",{className:"text-sm text-gray-500",children:"Basic Plan"}),o.jsxs("span",{className:"text-sm font-medium",children:[i,"/",a," calls"]})]}),o.jsx(Ts,{value:l,className:"h-2 mb-4"}),o.jsxs("div",{className:"mb-2 flex justify-between items-center",children:[o.jsx("span",{className:"text-sm text-gray-500",children:"Vouchers"}),o.jsx("span",{className:"text-sm font-medium",children:"3/5 remaining"})]}),o.jsx(Ts,{value:40,className:"h-2"})]}),o.jsxs(ce,{className:"p-6",children:[o.jsx("h3",{className:"text-lg font-medium mb-4",children:"Job Roles"}),o.jsx("div",{className:"space-y-3",children:En.slice(0,3).map(u=>o.jsxs("div",{className:"flex justify-between items-center",children:[o.jsx("span",{children:u.name}),o.jsxs("span",{className:"text-sm text-gray-500",children:[Tt.filter(p=>p.jobRoleId===u.id).length," candidates"]})]},u.id))}),o.jsx("div",{className:"mt-4",children:o.jsx(be,{to:"/job-roles",children:o.jsx(V,{variant:"ghost",size:"sm",className:"w-full",children:"View All Job Roles"})})})]}),o.jsxs(ce,{className:"p-6",children:[o.jsx("h3",{className:"text-lg font-medium mb-4",children:"Candidate Metrics"}),o.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[o.jsxs("div",{className:"bg-blue-50 p-4 rounded-md",children:[o.jsx("div",{className:"text-2xl font-semibold text-blue-700",children:t}),o.jsx("div",{className:"text-sm text-gray-500",children:"Total Candidates"})]}),o.jsxs("div",{className:"bg-purple-50 p-4 rounded-md",children:[o.jsx("div",{className:"text-2xl font-semibold text-purple-700",children:n}),o.jsx("div",{className:"text-sm text-gray-500",children:"Screened"})]}),o.jsxs("div",{className:"bg-green-50 p-4 rounded-md",children:[o.jsx("div",{className:"text-2xl font-semibold text-green-700",children:r}),o.jsx("div",{className:"text-sm text-gray-500",children:"Passed"})]}),o.jsxs("div",{className:"bg-red-50 p-4 rounded-md",children:[o.jsx("div",{className:"text-2xl font-semibold text-red-700",children:s}),o.jsx("div",{className:"text-sm text-gray-500",children:"Failed"})]})]})]})]}),o.jsxs("div",{className:"mb-8",children:[o.jsx("h3",{className:"text-lg font-medium mb-4",children:"Recent Billing"}),o.jsx("div",{className:"bg-white overflow-hidden shadow-sm rounded-lg",children:o.jsx("div",{className:"overflow-x-auto",children:o.jsxs("table",{className:"w-full",children:[o.jsx("thead",{className:"bg-gray-50 text-left",children:o.jsxs("tr",{children:[o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Invoice"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Action"})]})}),o.jsx("tbody",{className:"divide-y divide-gray-200",children:c.map((u,p)=>o.jsxs("tr",{className:"bg-white",children:[o.jsx("td",{className:"px-6 py-4 text-sm font-medium text-gray-900",children:u.invoiceNumber}),o.jsx("td",{className:"px-6 py-4 text-sm text-gray-500",children:u.amount}),o.jsx("td",{className:"px-6 py-4 text-sm text-gray-500",children:o.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:u.status})}),o.jsx("td",{className:"px-6 py-4 text-sm text-gray-500",children:u.date}),o.jsx("td",{className:"px-6 py-4 text-sm",children:o.jsx(V,{variant:"ghost",size:"sm",children:"View"})})]},p))})]})})})]}),o.jsxs("div",{children:[o.jsx("h3",{className:"text-lg font-medium mb-4",children:"Upcoming Interviews"}),o.jsx("div",{className:"bg-white overflow-hidden shadow-sm rounded-lg",children:o.jsx("div",{className:"overflow-x-auto",children:o.jsxs("table",{className:"w-full",children:[o.jsx("thead",{className:"bg-gray-50 text-left",children:o.jsxs("tr",{children:[o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Candidate"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Position"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date & Time"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Action"})]})}),o.jsx("tbody",{className:"divide-y divide-gray-200",children:Tt.filter(u=>u.interviewStatus==="Scheduled").map(u=>{const p=En.find(m=>m.id===u.jobRoleId);return o.jsxs("tr",{className:"bg-white",children:[o.jsx("td",{className:"px-6 py-4 text-sm font-medium text-gray-900",children:u.name}),o.jsx("td",{className:"px-6 py-4 text-sm text-gray-500",children:(p==null?void 0:p.name)||"Unknown"}),o.jsx("td",{className:"px-6 py-4 text-sm text-gray-500",children:u.scheduleDateTime?new Date(u.scheduleDateTime).toLocaleString():"Not scheduled"}),o.jsx("td",{className:"px-6 py-4 text-sm text-gray-500",children:o.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:u.interviewStatus})}),o.jsx("td",{className:"px-6 py-4 text-sm",children:o.jsx(be,{to:`/candidates/${u.id}`,children:o.jsx(V,{variant:"ghost",size:"sm",children:"View"})})})]},u.id)})})]})})})]})]})},$P=()=>{const[e,t]=f.useState(""),n=En.filter(s=>s.name.toLowerCase().includes(e.toLowerCase())),r=s=>Tt.filter(i=>i.jobRoleId===s).length;return o.jsxs("div",{children:[o.jsx(ft,{title:"Job Roles",description:"Create and manage job roles for your interviews",actions:o.jsx(be,{to:"/job-roles/create",children:o.jsxs(V,{className:"bg-primary hover:bg-primary/90",children:[o.jsx($a,{className:"mr-2 h-4 w-4"})," New Job Role"]})})}),o.jsx("div",{className:"mb-6",children:o.jsxs("div",{className:"relative",children:[o.jsx(gr,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),o.jsx(Be,{placeholder:"Search job roles...",className:"pl-10",value:e,onChange:s=>t(s.target.value)})]})}),n.length===0?o.jsxs("div",{className:"text-center py-12",children:[o.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"No job roles found"}),o.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Create a new job role to get started."}),o.jsx("div",{className:"mt-6",children:o.jsx(be,{to:"/job-roles/create",children:o.jsxs(V,{className:"bg-primary hover:bg-primary/90",children:[o.jsx($a,{className:"mr-2 h-4 w-4"})," New Job Role"]})})})]}):o.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:n.map(s=>o.jsxs(ce,{className:"overflow-hidden",children:[o.jsxs(Le,{className:"pb-3",children:[o.jsx(Fe,{className:"text-xl",children:s.name}),o.jsxs(qs,{className:"text-sm text-gray-500",children:["Created on ",new Date(s.createdAt).toLocaleDateString()]})]}),o.jsx(Me,{className:"pb-3",children:o.jsxs("div",{className:"space-y-4",children:[o.jsxs("div",{children:[o.jsx("h4",{className:"text-sm font-medium text-gray-500",children:"Key Skills"}),o.jsxs("div",{className:"mt-1 flex flex-wrap gap-1",children:[s.skills.slice(0,3).map((i,a)=>o.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:i},a)),s.skills.length>3&&o.jsxs("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800",children:["+",s.skills.length-3," more"]})]})]}),o.jsxs("div",{children:[o.jsx("h4",{className:"text-sm font-medium text-gray-500",children:"Experience Required"}),o.jsxs("p",{className:"mt-1 text-sm",children:[s.experience," years"]})]}),o.jsxs("div",{children:[o.jsx("h4",{className:"text-sm font-medium text-gray-500",children:"Candidates"}),o.jsxs("p",{className:"mt-1 text-sm",children:[r(s.id)," candidates"]})]})]})}),o.jsxs(ui,{className:"flex justify-between pt-3 border-t",children:[o.jsx(be,{to:`/candidates?role=${s.id}`,children:o.jsx(V,{variant:"outline",size:"sm",children:"View Candidates"})}),o.jsxs("div",{className:"flex space-x-2",children:[o.jsx(V,{variant:"ghost",size:"icon",children:o.jsx(GN,{className:"h-4 w-4"})}),o.jsx(V,{variant:"ghost",size:"icon",children:o.jsx(QN,{className:"h-4 w-4"})})]})]})]},s.id))})]})},Wu=f.forwardRef(({className:e,...t},n)=>o.jsx("textarea",{className:ee("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:n,...t}));Wu.displayName="Textarea";var If="Tabs",[UP,B5]=eo(If,[Il]),hy=Il(),[BP,_f]=UP(If),xy=f.forwardRef((e,t)=>{const{__scopeTabs:n,value:r,onValueChange:s,defaultValue:i,orientation:a="horizontal",dir:l,activationMode:c="automatic",...u}=e,p=Ml(l),[m,d]=Ks({prop:r,onChange:s,defaultProp:i});return o.jsx(BP,{scope:n,baseId:Ur(),value:m,onValueChange:d,orientation:a,dir:p,activationMode:c,children:o.jsx(Y.div,{dir:p,"data-orientation":a,...u,ref:t})})});xy.displayName=If;var gy="TabsList",vy=f.forwardRef((e,t)=>{const{__scopeTabs:n,loop:r=!0,...s}=e,i=_f(gy,n),a=hy(n);return o.jsx(n0,{asChild:!0,...a,orientation:i.orientation,dir:i.dir,loop:r,children:o.jsx(Y.div,{role:"tablist","aria-orientation":i.orientation,...s,ref:t})})});vy.displayName=gy;var yy="TabsTrigger",wy=f.forwardRef((e,t)=>{const{__scopeTabs:n,value:r,disabled:s=!1,...i}=e,a=_f(yy,n),l=hy(n),c=by(a.baseId,r),u=Sy(a.baseId,r),p=r===a.value;return o.jsx(r0,{asChild:!0,...l,focusable:!s,active:p,children:o.jsx(Y.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":u,"data-state":p?"active":"inactive","data-disabled":s?"":void 0,disabled:s,id:c,...i,ref:t,onMouseDown:z(e.onMouseDown,m=>{!s&&m.button===0&&m.ctrlKey===!1?a.onValueChange(r):m.preventDefault()}),onKeyDown:z(e.onKeyDown,m=>{[" ","Enter"].includes(m.key)&&a.onValueChange(r)}),onFocus:z(e.onFocus,()=>{const m=a.activationMode!=="manual";!p&&!s&&m&&a.onValueChange(r)})})})});wy.displayName=yy;var jy="TabsContent",Ny=f.forwardRef((e,t)=>{const{__scopeTabs:n,value:r,forceMount:s,children:i,...a}=e,l=_f(jy,n),c=by(l.baseId,r),u=Sy(l.baseId,r),p=r===l.value,m=f.useRef(p);return f.useEffect(()=>{const d=requestAnimationFrame(()=>m.current=!1);return()=>cancelAnimationFrame(d)},[]),o.jsx(xr,{present:s||p,children:({present:d})=>o.jsx(Y.div,{"data-state":p?"active":"inactive","data-orientation":l.orientation,role:"tabpanel","aria-labelledby":c,hidden:!d,id:u,tabIndex:0,...a,ref:t,style:{...e.style,animationDuration:m.current?"0s":void 0},children:d&&i})})});Ny.displayName=jy;function by(e,t){return`${e}-trigger-${t}`}function Sy(e,t){return`${e}-content-${t}`}var VP=xy,Cy=vy,Ey=wy,ky=Ny;const Py=VP,Of=f.forwardRef(({className:e,...t},n)=>o.jsx(Cy,{ref:n,className:ee("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));Of.displayName=Cy.displayName;const kr=f.forwardRef(({className:e,...t},n)=>o.jsx(Ey,{ref:n,className:ee("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));kr.displayName=Ey.displayName;const Pr=f.forwardRef(({className:e,...t},n)=>o.jsx(ky,{ref:n,className:ee("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));Pr.displayName=ky.displayName;const HP=()=>{const[e,t]=f.useState({name:"",duties:"",skills:"",education:"",experience:"",criteria:""}),[n,r]=f.useState(null),s=qr(),{toast:i}=Qr(),a=p=>{const{name:m,value:d}=p.target;t(v=>({...v,[m]:d}))},l=p=>{p.target.files&&p.target.files[0]&&(r(p.target.files[0]),setTimeout(()=>{t({name:"Software Engineer",duties:"Develop web applications using modern technologies. Collaborate with cross-functional teams to define and design new features.",skills:"Python, JavaScript, React, TypeScript, Node.js",education:"BS Computer Science or equivalent",experience:"3",criteria:"Strong Python skills, 3+ years experience, knowledge of React"}),i({title:"Job Description Processed",description:"AI has extracted job details from the uploaded file"})},1500))},c=()=>{r(null)},u=p=>{p.preventDefault(),i({title:"Job Role Created",description:`${e.name} has been created successfully`}),s("/job-roles")};return o.jsxs("div",{children:[o.jsx(ft,{title:"Create Job Role",description:"Add a new job role for interviewing candidates"}),o.jsxs(ce,{className:"mb-6",children:[o.jsx(Le,{children:o.jsx(Fe,{children:"How would you like to create this job role?"})}),o.jsx(Me,{children:o.jsxs(Py,{defaultValue:"manual",className:"w-full",children:[o.jsxs(Of,{className:"grid w-full grid-cols-2",children:[o.jsx(kr,{value:"manual",children:"Manual Entry"}),o.jsx(kr,{value:"upload",children:"Upload JD"})]}),o.jsx(Pr,{value:"manual",className:"mt-6",children:o.jsx("p",{className:"text-sm text-gray-500 mb-4",children:"Enter the job details manually using the form below"})}),o.jsx(Pr,{value:"upload",className:"mt-6",children:o.jsx("div",{className:"flex flex-col items-center justify-center p-6 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50",children:n?o.jsxs("div",{className:"w-full",children:[o.jsxs("div",{className:"flex items-center justify-between p-2 bg-blue-50 rounded",children:[o.jsxs("div",{className:"flex items-center",children:[o.jsx("div",{className:"bg-blue-100 rounded p-2",children:o.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-blue-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"})})}),o.jsxs("div",{className:"ml-3",children:[o.jsx("p",{className:"text-sm font-medium text-gray-900",children:n.name}),o.jsxs("p",{className:"text-xs text-gray-500",children:[Math.round(n.size/1024)," KB"]})]})]}),o.jsx("button",{type:"button",className:"text-gray-500 hover:text-gray-700",onClick:c,children:o.jsx(Zd,{className:"h-5 w-5"})})]}),o.jsx("p",{className:"mt-4 text-sm text-gray-600",children:"AI has extracted job details. Review and edit below as needed."})]}):o.jsxs("div",{className:"text-center",children:[o.jsx(Gg,{className:"mx-auto h-12 w-12 text-gray-400"}),o.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"Upload a job description document (PDF, DOCX)"}),o.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"AI will extract job details automatically"}),o.jsxs("label",{htmlFor:"file-upload",className:"mt-4 inline-block",children:[o.jsx(Be,{id:"file-upload",type:"file",className:"hidden",accept:".pdf,.docx,.doc",onChange:l}),o.jsx(V,{type:"button",className:"bg-primary hover:bg-primary/90",children:"Select File"})]})]})})})]})})]}),o.jsx("form",{onSubmit:u,children:o.jsxs(ce,{children:[o.jsx(Le,{children:o.jsx(Fe,{children:"Job Role Details"})}),o.jsxs(Me,{className:"space-y-6",children:[o.jsxs("div",{className:"space-y-2",children:[o.jsx(Bt,{htmlFor:"name",children:"Job Role Name"}),o.jsx(Be,{id:"name",name:"name",placeholder:"e.g. Software Engineer",value:e.name,onChange:a,required:!0})]}),o.jsxs("div",{className:"space-y-2",children:[o.jsx(Bt,{htmlFor:"duties",children:"Job Duties"}),o.jsx(Wu,{id:"duties",name:"duties",placeholder:"Describe the main responsibilities of this role",value:e.duties,onChange:a,rows:4,required:!0})]}),o.jsxs("div",{className:"space-y-2",children:[o.jsx(Bt,{htmlFor:"skills",children:"Skills (comma-separated)"}),o.jsx(Be,{id:"skills",name:"skills",placeholder:"e.g. Python, JavaScript, Communication",value:e.skills,onChange:a,required:!0}),o.jsx("p",{className:"text-xs text-gray-500",children:"Enter both technical and soft skills"})]}),o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[o.jsxs("div",{className:"space-y-2",children:[o.jsx(Bt,{htmlFor:"education",children:"Education Qualifications"}),o.jsx(Be,{id:"education",name:"education",placeholder:"e.g. BS Computer Science",value:e.education,onChange:a,required:!0})]}),o.jsxs("div",{className:"space-y-2",children:[o.jsx(Bt,{htmlFor:"experience",children:"Experience (years)"}),o.jsx(Be,{id:"experience",name:"experience",type:"number",placeholder:"e.g. 3",value:e.experience,onChange:a,min:"0",required:!0})]})]}),o.jsxs("div",{className:"space-y-2",children:[o.jsx(Bt,{htmlFor:"criteria",children:"Criteria for Best Match"}),o.jsx(Wu,{id:"criteria",name:"criteria",placeholder:"What makes a candidate ideal for this role?",value:e.criteria,onChange:a,rows:3,required:!0})]})]}),o.jsxs(ui,{className:"flex justify-between",children:[o.jsx(V,{type:"button",variant:"outline",onClick:()=>s("/job-roles"),children:"Cancel"}),o.jsx(V,{type:"submit",className:"bg-primary hover:bg-primary/90",children:"Create Job Role"})]})]})})]})};function Gp(e,[t,n]){return Math.min(n,Math.max(t,e))}function WP(e){const t=f.useRef({value:e,previous:e});return f.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}var KP=[" ","Enter","ArrowUp","ArrowDown"],GP=[" ","Enter"],bi="Select",[Ll,Fl,QP]=gl(bi),[ao,V5]=eo(bi,[QP,so]),zl=so(),[YP,yr]=ao(bi),[qP,XP]=ao(bi),Ty=e=>{const{__scopeSelect:t,children:n,open:r,defaultOpen:s,onOpenChange:i,value:a,defaultValue:l,onValueChange:c,dir:u,name:p,autoComplete:m,disabled:d,required:v,form:w}=e,h=zl(t),[j,g]=f.useState(null),[x,y]=f.useState(null),[N,b]=f.useState(!1),E=Ml(u),[C=!1,k]=Ks({prop:r,defaultProp:s,onChange:i}),[I,M]=Ks({prop:a,defaultProp:l,onChange:c}),U=f.useRef(null),D=j?w||!!j.closest("form"):!0,[W,A]=f.useState(new Set),K=Array.from(W).map(F=>F.props.value).join(";");return o.jsx(Nv,{...h,children:o.jsxs(YP,{required:v,scope:t,trigger:j,onTriggerChange:g,valueNode:x,onValueNodeChange:y,valueNodeHasChildren:N,onValueNodeHasChildrenChange:b,contentId:Ur(),value:I,onValueChange:M,open:C,onOpenChange:k,dir:E,triggerPointerDownPosRef:U,disabled:d,children:[o.jsx(Ll.Provider,{scope:t,children:o.jsx(qP,{scope:e.__scopeSelect,onNativeOptionAdd:f.useCallback(F=>{A(G=>new Set(G).add(F))},[]),onNativeOptionRemove:f.useCallback(F=>{A(G=>{const S=new Set(G);return S.delete(F),S})},[]),children:n})}),D?o.jsxs(ew,{"aria-hidden":!0,required:v,tabIndex:-1,name:p,autoComplete:m,value:I,onChange:F=>M(F.target.value),disabled:d,form:w,children:[I===void 0?o.jsx("option",{value:""}):null,Array.from(W)]},K):null]})})};Ty.displayName=bi;var Ry="SelectTrigger",My=f.forwardRef((e,t)=>{const{__scopeSelect:n,disabled:r=!1,...s}=e,i=zl(n),a=yr(Ry,n),l=a.disabled||r,c=me(t,a.onTriggerChange),u=Fl(n),p=f.useRef("touch"),[m,d,v]=tw(h=>{const j=u().filter(y=>!y.disabled),g=j.find(y=>y.value===a.value),x=nw(j,h,g);x!==void 0&&a.onValueChange(x.value)}),w=h=>{l||(a.onOpenChange(!0),v()),h&&(a.triggerPointerDownPosRef.current={x:Math.round(h.pageX),y:Math.round(h.pageY)})};return o.jsx(uf,{asChild:!0,...i,children:o.jsx(Y.button,{type:"button",role:"combobox","aria-controls":a.contentId,"aria-expanded":a.open,"aria-required":a.required,"aria-autocomplete":"none",dir:a.dir,"data-state":a.open?"open":"closed",disabled:l,"data-disabled":l?"":void 0,"data-placeholder":Zy(a.value)?"":void 0,...s,ref:c,onClick:z(s.onClick,h=>{h.currentTarget.focus(),p.current!=="mouse"&&w(h)}),onPointerDown:z(s.onPointerDown,h=>{p.current=h.pointerType;const j=h.target;j.hasPointerCapture(h.pointerId)&&j.releasePointerCapture(h.pointerId),h.button===0&&h.ctrlKey===!1&&h.pointerType==="mouse"&&(w(h),h.preventDefault())}),onKeyDown:z(s.onKeyDown,h=>{const j=m.current!=="";!(h.ctrlKey||h.altKey||h.metaKey)&&h.key.length===1&&d(h.key),!(j&&h.key===" ")&&KP.includes(h.key)&&(w(),h.preventDefault())})})})});My.displayName=Ry;var Ay="SelectValue",Iy=f.forwardRef((e,t)=>{const{__scopeSelect:n,className:r,style:s,children:i,placeholder:a="",...l}=e,c=yr(Ay,n),{onValueNodeHasChildrenChange:u}=c,p=i!==void 0,m=me(t,c.onValueNodeChange);return Je(()=>{u(p)},[u,p]),o.jsx(Y.span,{...l,ref:m,style:{pointerEvents:"none"},children:Zy(c.value)?o.jsx(o.Fragment,{children:a}):i})});Iy.displayName=Ay;var JP="SelectIcon",_y=f.forwardRef((e,t)=>{const{__scopeSelect:n,children:r,...s}=e;return o.jsx(Y.span,{"aria-hidden":!0,...s,ref:t,children:r||"▼"})});_y.displayName=JP;var ZP="SelectPortal",Oy=e=>o.jsx(vl,{asChild:!0,...e});Oy.displayName=ZP;var Hr="SelectContent",Dy=f.forwardRef((e,t)=>{const n=yr(Hr,e.__scopeSelect),[r,s]=f.useState();if(Je(()=>{s(new DocumentFragment)},[]),!n.open){const i=r;return i?Gr.createPortal(o.jsx(Ly,{scope:e.__scopeSelect,children:o.jsx(Ll.Slot,{scope:e.__scopeSelect,children:o.jsx("div",{children:e.children})})}),i):null}return o.jsx(Fy,{...e,ref:t})});Dy.displayName=Hr;var Dt=10,[Ly,wr]=ao(Hr),e5="SelectContentImpl",Fy=f.forwardRef((e,t)=>{const{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:s,onEscapeKeyDown:i,onPointerDownOutside:a,side:l,sideOffset:c,align:u,alignOffset:p,arrowPadding:m,collisionBoundary:d,collisionPadding:v,sticky:w,hideWhenDetached:h,avoidCollisions:j,...g}=e,x=yr(Hr,n),[y,N]=f.useState(null),[b,E]=f.useState(null),C=me(t,H=>N(H)),[k,I]=f.useState(null),[M,U]=f.useState(null),D=Fl(n),[W,A]=f.useState(!1),K=f.useRef(!1);f.useEffect(()=>{if(y)return o0(y)},[y]),Yv();const F=f.useCallback(H=>{const[se,...ie]=D().map(ae=>ae.ref.current),[ne]=ie.slice(-1),ue=document.activeElement;for(const ae of H)if(ae===ue||(ae==null||ae.scrollIntoView({block:"nearest"}),ae===se&&b&&(b.scrollTop=0),ae===ne&&b&&(b.scrollTop=b.scrollHeight),ae==null||ae.focus(),document.activeElement!==ue))return},[D,b]),G=f.useCallback(()=>F([k,y]),[F,k,y]);f.useEffect(()=>{W&&G()},[W,G]);const{onOpenChange:S,triggerPointerDownPosRef:P}=x;f.useEffect(()=>{if(y){let H={x:0,y:0};const se=ne=>{var ue,ae;H={x:Math.abs(Math.round(ne.pageX)-(((ue=P.current)==null?void 0:ue.x)??0)),y:Math.abs(Math.round(ne.pageY)-(((ae=P.current)==null?void 0:ae.y)??0))}},ie=ne=>{H.x<=10&&H.y<=10?ne.preventDefault():y.contains(ne.target)||S(!1),document.removeEventListener("pointermove",se),P.current=null};return P.current!==null&&(document.addEventListener("pointermove",se),document.addEventListener("pointerup",ie,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",se),document.removeEventListener("pointerup",ie,{capture:!0})}}},[y,S,P]),f.useEffect(()=>{const H=()=>S(!1);return window.addEventListener("blur",H),window.addEventListener("resize",H),()=>{window.removeEventListener("blur",H),window.removeEventListener("resize",H)}},[S]);const[L,_]=tw(H=>{const se=D().filter(ue=>!ue.disabled),ie=se.find(ue=>ue.ref.current===document.activeElement),ne=nw(se,H,ie);ne&&setTimeout(()=>ne.ref.current.focus())}),B=f.useCallback((H,se,ie)=>{const ne=!K.current&&!ie;(x.value!==void 0&&x.value===se||ne)&&(I(H),ne&&(K.current=!0))},[x.value]),Q=f.useCallback(()=>y==null?void 0:y.focus(),[y]),re=f.useCallback((H,se,ie)=>{const ne=!K.current&&!ie;(x.value!==void 0&&x.value===se||ne)&&U(H)},[x.value]),Pe=r==="popper"?Ku:zy,q=Pe===Ku?{side:l,sideOffset:c,align:u,alignOffset:p,arrowPadding:m,collisionBoundary:d,collisionPadding:v,sticky:w,hideWhenDetached:h,avoidCollisions:j}:{};return o.jsx(Ly,{scope:n,content:y,viewport:b,onViewportChange:E,itemRefCallback:B,selectedItem:k,onItemLeave:Q,itemTextRefCallback:re,focusSelectedItem:G,selectedItemText:M,position:r,isPositioned:W,searchRef:L,children:o.jsx(jf,{as:$r,allowPinchZoom:!0,children:o.jsx(wf,{asChild:!0,trapped:x.open,onMountAutoFocus:H=>{H.preventDefault()},onUnmountAutoFocus:z(s,H=>{var se;(se=x.trigger)==null||se.focus({preventScroll:!0}),H.preventDefault()}),children:o.jsx(vi,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:a,onFocusOutside:H=>H.preventDefault(),onDismiss:()=>x.onOpenChange(!1),children:o.jsx(Pe,{role:"listbox",id:x.contentId,"data-state":x.open?"open":"closed",dir:x.dir,onContextMenu:H=>H.preventDefault(),...g,...q,onPlaced:()=>A(!0),ref:C,style:{display:"flex",flexDirection:"column",outline:"none",...g.style},onKeyDown:z(g.onKeyDown,H=>{const se=H.ctrlKey||H.altKey||H.metaKey;if(H.key==="Tab"&&H.preventDefault(),!se&&H.key.length===1&&_(H.key),["ArrowUp","ArrowDown","Home","End"].includes(H.key)){let ne=D().filter(ue=>!ue.disabled).map(ue=>ue.ref.current);if(["ArrowUp","End"].includes(H.key)&&(ne=ne.slice().reverse()),["ArrowUp","ArrowDown"].includes(H.key)){const ue=H.target,ae=ne.indexOf(ue);ne=ne.slice(ae+1)}setTimeout(()=>F(ne)),H.preventDefault()}})})})})})})});Fy.displayName=e5;var t5="SelectItemAlignedPosition",zy=f.forwardRef((e,t)=>{const{__scopeSelect:n,onPlaced:r,...s}=e,i=yr(Hr,n),a=wr(Hr,n),[l,c]=f.useState(null),[u,p]=f.useState(null),m=me(t,C=>p(C)),d=Fl(n),v=f.useRef(!1),w=f.useRef(!0),{viewport:h,selectedItem:j,selectedItemText:g,focusSelectedItem:x}=a,y=f.useCallback(()=>{if(i.trigger&&i.valueNode&&l&&u&&h&&j&&g){const C=i.trigger.getBoundingClientRect(),k=u.getBoundingClientRect(),I=i.valueNode.getBoundingClientRect(),M=g.getBoundingClientRect();if(i.dir!=="rtl"){const ue=M.left-k.left,ae=I.left-ue,Ze=C.left-ae,mt=C.width+Ze,An=Math.max(mt,k.width),St=window.innerWidth-Dt,jr=Gp(ae,[Dt,Math.max(Dt,St-An)]);l.style.minWidth=mt+"px",l.style.left=jr+"px"}else{const ue=k.right-M.right,ae=window.innerWidth-I.right-ue,Ze=window.innerWidth-C.right-ae,mt=C.width+Ze,An=Math.max(mt,k.width),St=window.innerWidth-Dt,jr=Gp(ae,[Dt,Math.max(Dt,St-An)]);l.style.minWidth=mt+"px",l.style.right=jr+"px"}const U=d(),D=window.innerHeight-Dt*2,W=h.scrollHeight,A=window.getComputedStyle(u),K=parseInt(A.borderTopWidth,10),F=parseInt(A.paddingTop,10),G=parseInt(A.borderBottomWidth,10),S=parseInt(A.paddingBottom,10),P=K+F+W+S+G,L=Math.min(j.offsetHeight*5,P),_=window.getComputedStyle(h),B=parseInt(_.paddingTop,10),Q=parseInt(_.paddingBottom,10),re=C.top+C.height/2-Dt,Pe=D-re,q=j.offsetHeight/2,H=j.offsetTop+q,se=K+F+H,ie=P-se;if(se<=re){const ue=U.length>0&&j===U[U.length-1].ref.current;l.style.bottom="0px";const ae=u.clientHeight-h.offsetTop-h.offsetHeight,Ze=Math.max(Pe,q+(ue?Q:0)+ae+G),mt=se+Ze;l.style.height=mt+"px"}else{const ue=U.length>0&&j===U[0].ref.current;l.style.top="0px";const Ze=Math.max(re,K+h.offsetTop+(ue?B:0)+q)+ie;l.style.height=Ze+"px",h.scrollTop=se-re+h.offsetTop}l.style.margin=`${Dt}px 0`,l.style.minHeight=L+"px",l.style.maxHeight=D+"px",r==null||r(),requestAnimationFrame(()=>v.current=!0)}},[d,i.trigger,i.valueNode,l,u,h,j,g,i.dir,r]);Je(()=>y(),[y]);const[N,b]=f.useState();Je(()=>{u&&b(window.getComputedStyle(u).zIndex)},[u]);const E=f.useCallback(C=>{C&&w.current===!0&&(y(),x==null||x(),w.current=!1)},[y,x]);return o.jsx(r5,{scope:n,contentWrapper:l,shouldExpandOnScrollRef:v,onScrollButtonChange:E,children:o.jsx("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:N},children:o.jsx(Y.div,{...s,ref:m,style:{boxSizing:"border-box",maxHeight:"100%",...s.style}})})})});zy.displayName=t5;var n5="SelectPopperPosition",Ku=f.forwardRef((e,t)=>{const{__scopeSelect:n,align:r="start",collisionPadding:s=Dt,...i}=e,a=zl(n);return o.jsx(df,{...a,...i,ref:t,align:r,collisionPadding:s,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});Ku.displayName=n5;var[r5,Df]=ao(Hr,{}),Gu="SelectViewport",$y=f.forwardRef((e,t)=>{const{__scopeSelect:n,nonce:r,...s}=e,i=wr(Gu,n),a=Df(Gu,n),l=me(t,i.onViewportChange),c=f.useRef(0);return o.jsxs(o.Fragment,{children:[o.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),o.jsx(Ll.Slot,{scope:n,children:o.jsx(Y.div,{"data-radix-select-viewport":"",role:"presentation",...s,ref:l,style:{position:"relative",flex:1,overflow:"hidden auto",...s.style},onScroll:z(s.onScroll,u=>{const p=u.currentTarget,{contentWrapper:m,shouldExpandOnScrollRef:d}=a;if(d!=null&&d.current&&m){const v=Math.abs(c.current-p.scrollTop);if(v>0){const w=window.innerHeight-Dt*2,h=parseFloat(m.style.minHeight),j=parseFloat(m.style.height),g=Math.max(h,j);if(g<w){const x=g+v,y=Math.min(w,x),N=x-y;m.style.height=y+"px",m.style.bottom==="0px"&&(p.scrollTop=N>0?N:0,m.style.justifyContent="flex-end")}}}c.current=p.scrollTop})})})]})});$y.displayName=Gu;var Uy="SelectGroup",[s5,o5]=ao(Uy),i5=f.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,s=Ur();return o.jsx(s5,{scope:n,id:s,children:o.jsx(Y.div,{role:"group","aria-labelledby":s,...r,ref:t})})});i5.displayName=Uy;var By="SelectLabel",Vy=f.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,s=o5(By,n);return o.jsx(Y.div,{id:s.id,...r,ref:t})});Vy.displayName=By;var Za="SelectItem",[a5,Hy]=ao(Za),Wy=f.forwardRef((e,t)=>{const{__scopeSelect:n,value:r,disabled:s=!1,textValue:i,...a}=e,l=yr(Za,n),c=wr(Za,n),u=l.value===r,[p,m]=f.useState(i??""),[d,v]=f.useState(!1),w=me(t,x=>{var y;return(y=c.itemRefCallback)==null?void 0:y.call(c,x,r,s)}),h=Ur(),j=f.useRef("touch"),g=()=>{s||(l.onValueChange(r),l.onOpenChange(!1))};if(r==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return o.jsx(a5,{scope:n,value:r,disabled:s,textId:h,isSelected:u,onItemTextChange:f.useCallback(x=>{m(y=>y||((x==null?void 0:x.textContent)??"").trim())},[]),children:o.jsx(Ll.ItemSlot,{scope:n,value:r,disabled:s,textValue:p,children:o.jsx(Y.div,{role:"option","aria-labelledby":h,"data-highlighted":d?"":void 0,"aria-selected":u&&d,"data-state":u?"checked":"unchecked","aria-disabled":s||void 0,"data-disabled":s?"":void 0,tabIndex:s?void 0:-1,...a,ref:w,onFocus:z(a.onFocus,()=>v(!0)),onBlur:z(a.onBlur,()=>v(!1)),onClick:z(a.onClick,()=>{j.current!=="mouse"&&g()}),onPointerUp:z(a.onPointerUp,()=>{j.current==="mouse"&&g()}),onPointerDown:z(a.onPointerDown,x=>{j.current=x.pointerType}),onPointerMove:z(a.onPointerMove,x=>{var y;j.current=x.pointerType,s?(y=c.onItemLeave)==null||y.call(c):j.current==="mouse"&&x.currentTarget.focus({preventScroll:!0})}),onPointerLeave:z(a.onPointerLeave,x=>{var y;x.currentTarget===document.activeElement&&((y=c.onItemLeave)==null||y.call(c))}),onKeyDown:z(a.onKeyDown,x=>{var N;((N=c.searchRef)==null?void 0:N.current)!==""&&x.key===" "||(GP.includes(x.key)&&g(),x.key===" "&&x.preventDefault())})})})})});Wy.displayName=Za;var Po="SelectItemText",Ky=f.forwardRef((e,t)=>{const{__scopeSelect:n,className:r,style:s,...i}=e,a=yr(Po,n),l=wr(Po,n),c=Hy(Po,n),u=XP(Po,n),[p,m]=f.useState(null),d=me(t,g=>m(g),c.onItemTextChange,g=>{var x;return(x=l.itemTextRefCallback)==null?void 0:x.call(l,g,c.value,c.disabled)}),v=p==null?void 0:p.textContent,w=f.useMemo(()=>o.jsx("option",{value:c.value,disabled:c.disabled,children:v},c.value),[c.disabled,c.value,v]),{onNativeOptionAdd:h,onNativeOptionRemove:j}=u;return Je(()=>(h(w),()=>j(w)),[h,j,w]),o.jsxs(o.Fragment,{children:[o.jsx(Y.span,{id:c.textId,...i,ref:d}),c.isSelected&&a.valueNode&&!a.valueNodeHasChildren?Gr.createPortal(i.children,a.valueNode):null]})});Ky.displayName=Po;var Gy="SelectItemIndicator",Qy=f.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e;return Hy(Gy,n).isSelected?o.jsx(Y.span,{"aria-hidden":!0,...r,ref:t}):null});Qy.displayName=Gy;var Qu="SelectScrollUpButton",Yy=f.forwardRef((e,t)=>{const n=wr(Qu,e.__scopeSelect),r=Df(Qu,e.__scopeSelect),[s,i]=f.useState(!1),a=me(t,r.onScrollButtonChange);return Je(()=>{if(n.viewport&&n.isPositioned){let l=function(){const u=c.scrollTop>0;i(u)};const c=n.viewport;return l(),c.addEventListener("scroll",l),()=>c.removeEventListener("scroll",l)}},[n.viewport,n.isPositioned]),s?o.jsx(Xy,{...e,ref:a,onAutoScroll:()=>{const{viewport:l,selectedItem:c}=n;l&&c&&(l.scrollTop=l.scrollTop-c.offsetHeight)}}):null});Yy.displayName=Qu;var Yu="SelectScrollDownButton",qy=f.forwardRef((e,t)=>{const n=wr(Yu,e.__scopeSelect),r=Df(Yu,e.__scopeSelect),[s,i]=f.useState(!1),a=me(t,r.onScrollButtonChange);return Je(()=>{if(n.viewport&&n.isPositioned){let l=function(){const u=c.scrollHeight-c.clientHeight,p=Math.ceil(c.scrollTop)<u;i(p)};const c=n.viewport;return l(),c.addEventListener("scroll",l),()=>c.removeEventListener("scroll",l)}},[n.viewport,n.isPositioned]),s?o.jsx(Xy,{...e,ref:a,onAutoScroll:()=>{const{viewport:l,selectedItem:c}=n;l&&c&&(l.scrollTop=l.scrollTop+c.offsetHeight)}}):null});qy.displayName=Yu;var Xy=f.forwardRef((e,t)=>{const{__scopeSelect:n,onAutoScroll:r,...s}=e,i=wr("SelectScrollButton",n),a=f.useRef(null),l=Fl(n),c=f.useCallback(()=>{a.current!==null&&(window.clearInterval(a.current),a.current=null)},[]);return f.useEffect(()=>()=>c(),[c]),Je(()=>{var p;const u=l().find(m=>m.ref.current===document.activeElement);(p=u==null?void 0:u.ref.current)==null||p.scrollIntoView({block:"nearest"})},[l]),o.jsx(Y.div,{"aria-hidden":!0,...s,ref:t,style:{flexShrink:0,...s.style},onPointerDown:z(s.onPointerDown,()=>{a.current===null&&(a.current=window.setInterval(r,50))}),onPointerMove:z(s.onPointerMove,()=>{var u;(u=i.onItemLeave)==null||u.call(i),a.current===null&&(a.current=window.setInterval(r,50))}),onPointerLeave:z(s.onPointerLeave,()=>{c()})})}),l5="SelectSeparator",Jy=f.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e;return o.jsx(Y.div,{"aria-hidden":!0,...r,ref:t})});Jy.displayName=l5;var qu="SelectArrow",c5=f.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,s=zl(n),i=yr(qu,n),a=wr(qu,n);return i.open&&a.position==="popper"?o.jsx(ff,{...s,...r,ref:t}):null});c5.displayName=qu;function Zy(e){return e===""||e===void 0}var ew=f.forwardRef((e,t)=>{const{value:n,...r}=e,s=f.useRef(null),i=me(t,s),a=WP(n);return f.useEffect(()=>{const l=s.current,c=window.HTMLSelectElement.prototype,p=Object.getOwnPropertyDescriptor(c,"value").set;if(a!==n&&p){const m=new Event("change",{bubbles:!0});p.call(l,n),l.dispatchEvent(m)}},[a,n]),o.jsx(yi,{asChild:!0,children:o.jsx("select",{...r,ref:i,defaultValue:n})})});ew.displayName="BubbleSelect";function tw(e){const t=Xe(e),n=f.useRef(""),r=f.useRef(0),s=f.useCallback(a=>{const l=n.current+a;t(l),function c(u){n.current=u,window.clearTimeout(r.current),u!==""&&(r.current=window.setTimeout(()=>c(""),1e3))}(l)},[t]),i=f.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return f.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,s,i]}function nw(e,t,n){const s=t.length>1&&Array.from(t).every(u=>u===t[0])?t[0]:t,i=n?e.indexOf(n):-1;let a=u5(e,Math.max(i,0));s.length===1&&(a=a.filter(u=>u!==n));const c=a.find(u=>u.textValue.toLowerCase().startsWith(s.toLowerCase()));return c!==n?c:void 0}function u5(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var d5=Ty,rw=My,f5=Iy,m5=_y,p5=Oy,sw=Dy,h5=$y,ow=Vy,iw=Wy,x5=Ky,g5=Qy,aw=Yy,lw=qy,cw=Jy;const kn=d5,Pn=f5,ln=f.forwardRef(({className:e,children:t,...n},r)=>o.jsxs(rw,{ref:r,className:ee("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...n,children:[t,o.jsx(m5,{asChild:!0,children:o.jsx(Hg,{className:"h-4 w-4 opacity-50"})})]}));ln.displayName=rw.displayName;const uw=f.forwardRef(({className:e,...t},n)=>o.jsx(aw,{ref:n,className:ee("flex cursor-default items-center justify-center py-1",e),...t,children:o.jsx(VN,{className:"h-4 w-4"})}));uw.displayName=aw.displayName;const dw=f.forwardRef(({className:e,...t},n)=>o.jsx(lw,{ref:n,className:ee("flex cursor-default items-center justify-center py-1",e),...t,children:o.jsx(Hg,{className:"h-4 w-4"})}));dw.displayName=lw.displayName;const cn=f.forwardRef(({className:e,children:t,position:n="popper",...r},s)=>o.jsx(p5,{children:o.jsxs(sw,{ref:s,className:ee("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",n==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:n,...r,children:[o.jsx(uw,{}),o.jsx(h5,{className:ee("p-1",n==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),o.jsx(dw,{})]})}));cn.displayName=sw.displayName;const v5=f.forwardRef(({className:e,...t},n)=>o.jsx(ow,{ref:n,className:ee("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t}));v5.displayName=ow.displayName;const le=f.forwardRef(({className:e,children:t,...n},r)=>o.jsxs(iw,{ref:r,className:ee("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...n,children:[o.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:o.jsx(g5,{children:o.jsx(Nl,{className:"h-4 w-4"})})}),o.jsx(x5,{children:t})]}));le.displayName=iw.displayName;const y5=f.forwardRef(({className:e,...t},n)=>o.jsx(cw,{ref:n,className:ee("-mx-1 my-1 h-px bg-muted",e),...t}));y5.displayName=cw.displayName;function ir({status:e,className:t}){const n=()=>{switch(e){case"Scheduled":case"Active":return"bg-blue-100 text-blue-800";case"In Progress":case"Pending":return"bg-yellow-100 text-yellow-800";case"Completed":case"Resolved":case"Pass":return"bg-green-100 text-green-800";case"Cancelled":case"Fail":return"bg-red-100 text-red-800";case"N/A":case"Not Scheduled":default:return"bg-gray-100 text-gray-800"}};return o.jsx("span",{className:ee("inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",n(),t),children:e})}const w5=()=>{const[e]=ZC(),t=e.get("role")||"all",[n,r]=f.useState(""),[s,i]=f.useState("all"),a=Tt.filter(c=>{const u=t==="all"||c.jobRoleId===t,p=c.name.toLowerCase().includes(n.toLowerCase())||c.email.toLowerCase().includes(n.toLowerCase()),m=s==="all"||s==="contacted"&&c.contactStatus!=="Not Contacted"||s==="scheduled"&&c.interviewStatus==="Scheduled"||s==="completed"&&c.interviewStatus==="Completed";return u&&p&&m}),l=c=>{const u=En.find(p=>p.id===c);return u?u.name:"Unknown"};return o.jsxs("div",{children:[o.jsx(ft,{title:"Candidates",description:"Manage candidates for your job roles",actions:o.jsxs("div",{className:"flex space-x-2",children:[o.jsxs(V,{variant:"outline",className:"flex items-center gap-2",children:[o.jsx(Gg,{className:"h-4 w-4"})," Upload Resumes"]}),o.jsx(V,{className:"bg-primary hover:bg-primary/90",children:"Add Candidate"})]})}),o.jsxs("div",{className:"mb-6 flex flex-col sm:flex-row gap-4",children:[o.jsxs("div",{className:"relative flex-grow",children:[o.jsx(gr,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),o.jsx(Be,{placeholder:"Search candidates by name or email...",className:"pl-10",value:n,onChange:c=>r(c.target.value)})]}),o.jsxs("div",{className:"grid grid-cols-2 sm:grid-cols-3 gap-4 sm:w-auto",children:[o.jsxs(kn,{value:t,onValueChange:c=>{const u=new URLSearchParams(window.location.search);c==="all"?u.delete("role"):u.set("role",c),window.history.pushState({},"",`?${u.toString()}`),window.location.reload()},children:[o.jsx(ln,{className:"w-full",children:o.jsx(Pn,{placeholder:"Filter by role"})}),o.jsxs(cn,{children:[o.jsx(le,{value:"all",children:"All Roles"}),En.map(c=>o.jsx(le,{value:c.id,children:c.name},c.id))]})]}),o.jsxs(kn,{value:s,onValueChange:i,children:[o.jsx(ln,{className:"w-full",children:o.jsx(Pn,{placeholder:"Filter by status"})}),o.jsxs(cn,{children:[o.jsx(le,{value:"all",children:"All Status"}),o.jsx(le,{value:"contacted",children:"Contacted"}),o.jsx(le,{value:"scheduled",children:"Scheduled"}),o.jsx(le,{value:"completed",children:"Completed"})]})]}),o.jsxs(V,{variant:"outline",className:"flex items-center gap-2",children:[o.jsx(Yr,{className:"h-4 w-4"})," Export"]})]})]}),a.length===0?o.jsxs("div",{className:"text-center py-12",children:[o.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"No candidates found"}),o.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Try adjusting your search or filters"})]}):o.jsx("div",{className:"bg-white overflow-hidden shadow-sm rounded-lg",children:o.jsx("div",{className:"overflow-x-auto",children:o.jsxs("table",{className:"w-full",children:[o.jsx("thead",{className:"bg-gray-50 text-left",children:o.jsxs("tr",{children:[o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Name"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Position"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Experience"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Contact Status"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Interview Status"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Result"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),o.jsx("tbody",{className:"divide-y divide-gray-200",children:a.map(c=>o.jsxs("tr",{className:"hover:bg-gray-50",children:[o.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:o.jsxs("div",{className:"flex items-center",children:[o.jsx("div",{className:"h-10 w-10 rounded-full bg-primary/20 flex items-center justify-center text-primary font-medium",children:c.name.split(" ").map(u=>u[0]).join("")}),o.jsxs("div",{className:"ml-4",children:[o.jsx("div",{className:"text-sm font-medium text-gray-900",children:c.name}),o.jsx("div",{className:"text-sm text-gray-500",children:c.email})]})]})}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:l(c.jobRoleId)}),o.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:[c.experience," years"]}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:o.jsx(ir,{status:c.contactStatus})}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:o.jsx(ir,{status:c.interviewStatus})}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:o.jsx(ir,{status:c.interviewResult})}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:o.jsx(be,{to:`/candidates/${c.id}`,children:o.jsx(V,{variant:"ghost",size:"sm",children:"View"})})})]},c.id))})]})})})]})},j5=()=>{var s,i;const{id:e}=Hv(),t=Tt.find(a=>a.id===e);if(!t)return o.jsxs("div",{className:"text-center py-12",children:[o.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Candidate not found"}),o.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"The candidate you're looking for doesn't exist or has been removed."}),o.jsx("div",{className:"mt-6",children:o.jsx(be,{to:"/candidates",children:o.jsx(V,{children:"Back to Candidates"})})})]});const n=En.find(a=>a.id===t.jobRoleId),r=t.scheduleDateTime?new Date(t.scheduleDateTime):null;return o.jsxs("div",{children:[o.jsx("div",{className:"mb-6",children:o.jsxs(be,{to:"/candidates",className:"text-primary hover:underline flex items-center",children:[o.jsx(Wg,{className:"h-4 w-4 mr-1"})," Back to Candidates"]})}),o.jsx(ft,{title:t.name,description:(n==null?void 0:n.name)||"Unknown Role",actions:o.jsxs(V,{variant:"outline",className:"flex items-center gap-2",children:[o.jsx(Yr,{className:"h-4 w-4"})," Export PDF"]})}),o.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[o.jsxs("div",{className:"lg:col-span-1 space-y-6",children:[o.jsxs(ce,{children:[o.jsx(Le,{children:o.jsx(Fe,{children:"Candidate Summary"})}),o.jsxs(Me,{className:"space-y-4",children:[o.jsxs("div",{children:[o.jsx("h4",{className:"text-sm font-medium text-gray-500",children:"Email"}),o.jsxs("p",{className:"flex items-center mt-1",children:[o.jsx(Gs,{className:"h-4 w-4 mr-2 text-gray-400"}),o.jsx("a",{href:`mailto:${t.email}`,className:"text-primary hover:underline",children:t.email})]})]}),o.jsxs("div",{children:[o.jsx("h4",{className:"text-sm font-medium text-gray-500",children:"Phone"}),o.jsxs("p",{className:"flex items-center mt-1",children:[o.jsx(bn,{className:"h-4 w-4 mr-2 text-gray-400"}),o.jsx("a",{href:`tel:${t.phone}`,className:"text-primary hover:underline",children:t.phone})]})]}),o.jsxs("div",{children:[o.jsx("h4",{className:"text-sm font-medium text-gray-500",children:"Interview Status"}),o.jsx("div",{className:"mt-1",children:o.jsx(ir,{status:t.interviewStatus})})]}),r&&o.jsxs("div",{children:[o.jsx("h4",{className:"text-sm font-medium text-gray-500",children:"Interview Date"}),o.jsxs("p",{className:"flex items-center mt-1",children:[o.jsx(jl,{className:"h-4 w-4 mr-2 text-gray-400"}),r.toLocaleString()]})]}),t.interviewResult!=="N/A"&&o.jsxs("div",{children:[o.jsx("h4",{className:"text-sm font-medium text-gray-500",children:"Interview Result"}),o.jsx("div",{className:"mt-1",children:o.jsx(ir,{status:t.interviewResult})})]})]})]}),o.jsxs(ce,{children:[o.jsx(Le,{children:o.jsx(Fe,{children:"Communication History"})}),o.jsx(Me,{className:"space-y-4",children:(s=t.communicationHistory)!=null&&s.emails.length||(i=t.communicationHistory)!=null&&i.calls.length?o.jsxs("div",{className:"space-y-6",children:[t.communicationHistory.emails.length>0&&o.jsxs("div",{children:[o.jsx("h4",{className:"text-sm font-medium text-gray-500 mb-2",children:"Emails"}),o.jsx("div",{className:"space-y-3",children:t.communicationHistory.emails.map((a,l)=>o.jsxs("div",{className:"flex items-start pb-3 border-b border-gray-100",children:[o.jsx("div",{className:"w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center shrink-0",children:o.jsx(Gs,{className:"h-5 w-5 text-blue-600"})}),o.jsxs("div",{className:"ml-3",children:[o.jsx("p",{className:"text-sm font-medium",children:a.subject}),o.jsxs("p",{className:"text-xs text-gray-500",children:[a.date," • ",a.status]})]})]},l))})]}),t.communicationHistory.calls.length>0&&o.jsxs("div",{children:[o.jsx("h4",{className:"text-sm font-medium text-gray-500 mb-2",children:"Calls"}),o.jsx("div",{className:"space-y-3",children:t.communicationHistory.calls.map((a,l)=>o.jsxs("div",{className:"flex items-start pb-3 border-b border-gray-100",children:[o.jsx("div",{className:"w-10 h-10 rounded-full bg-green-100 flex items-center justify-center shrink-0",children:o.jsx(bn,{className:"h-5 w-5 text-green-600"})}),o.jsxs("div",{className:"ml-3",children:[o.jsx("p",{className:"text-sm font-medium",children:a.response}),o.jsx("p",{className:"text-xs text-gray-500",children:a.date})]})]},l))})]}),t.contactOutcome&&o.jsxs("div",{children:[o.jsx("h4",{className:"text-sm font-medium text-gray-500 mb-2",children:"Outcome"}),o.jsx("p",{className:"text-sm bg-green-50 border border-green-100 rounded-md p-3",children:t.contactOutcome})]})]}):o.jsx("p",{className:"text-sm text-gray-500",children:"No communication history yet."})})]})]}),o.jsx("div",{className:"lg:col-span-2 space-y-6",children:o.jsxs(Py,{defaultValue:"profile",className:"w-full",children:[o.jsxs(Of,{className:"grid w-full grid-cols-4",children:[o.jsx(kr,{value:"profile",children:"Profile"}),o.jsx(kr,{value:"metrics",children:"Metrics"}),o.jsx(kr,{value:"transcript",children:"Transcript"}),o.jsx(kr,{value:"questions",children:o.jsx(be,{to:`/candidates/${t.id}/questions`,className:"w-full h-full flex items-center justify-center",children:"Q&A"})})]}),o.jsx(Pr,{value:"profile",className:"mt-6 space-y-6",children:o.jsxs(ce,{children:[o.jsx(Le,{children:o.jsx(Fe,{children:"Education & Experience"})}),o.jsxs(Me,{className:"space-y-4",children:[o.jsxs("div",{children:[o.jsx("h4",{className:"text-sm font-medium text-gray-500",children:"Education"}),o.jsx("p",{className:"mt-1",children:t.education})]}),o.jsxs("div",{children:[o.jsx("h4",{className:"text-sm font-medium text-gray-500",children:"Experience"}),o.jsxs("p",{className:"mt-1",children:[t.experience," years"]})]}),o.jsxs("div",{children:[o.jsx("h4",{className:"text-sm font-medium text-gray-500",children:"Skills"}),o.jsx("div",{className:"mt-1 flex flex-wrap gap-1",children:t.skills.map((a,l)=>o.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:a},l))})]}),o.jsxs("div",{children:[o.jsx("h4",{className:"text-sm font-medium text-gray-500",children:"Projects"}),o.jsx("ul",{className:"mt-1 list-disc list-inside space-y-1",children:t.projects.map((a,l)=>o.jsx("li",{className:"text-sm",children:a},l))})]}),t.otherDetails&&o.jsxs("div",{children:[o.jsx("h4",{className:"text-sm font-medium text-gray-500",children:"Other Details"}),o.jsx("p",{className:"mt-1",children:t.otherDetails})]})]})]})}),o.jsx(Pr,{value:"metrics",className:"mt-6 space-y-6",children:t.metrics?o.jsxs(ce,{children:[o.jsxs(Le,{children:[o.jsx(Fe,{children:"Performance Metrics"}),o.jsxs(qs,{children:["Overall Score: ",t.metrics.overallScore,"/100 • ",t.interviewResult]})]}),o.jsxs(Me,{className:"space-y-6",children:[o.jsxs("div",{className:"space-y-2",children:[o.jsxs("div",{className:"flex justify-between",children:[o.jsx("span",{className:"text-sm font-medium",children:"Communication"}),o.jsxs("span",{className:"text-sm",children:[t.metrics.communication,"/10"]})]}),o.jsx(Ts,{value:t.metrics.communication*10,className:"h-2"}),o.jsx("p",{className:"text-xs text-gray-500",children:"Clear, confident speech"})]}),o.jsxs("div",{className:"space-y-2",children:[o.jsxs("div",{className:"flex justify-between",children:[o.jsx("span",{className:"text-sm font-medium",children:"Skills"}),o.jsxs("span",{className:"text-sm",children:[t.metrics.skills,"/10"]})]}),o.jsx(Ts,{value:t.metrics.skills*10,className:"h-2"}),o.jsx("p",{className:"text-xs text-gray-500",children:"Strong Python, moderate JavaScript"})]}),o.jsxs("div",{className:"space-y-2",children:[o.jsxs("div",{className:"flex justify-between",children:[o.jsx("span",{className:"text-sm font-medium",children:"Response Quality"}),o.jsxs("span",{className:"text-sm",children:[t.metrics.responseQuality,"/10"]})]}),o.jsx(Ts,{value:t.metrics.responseQuality*10,className:"h-2"}),o.jsx("p",{className:"text-xs text-gray-500",children:"Detailed, relevant answers"})]}),t.agentNotes&&o.jsxs("div",{className:"pt-2 border-t",children:[o.jsx("h4",{className:"text-sm font-medium text-gray-500 mb-2",children:"Agent Notes"}),o.jsx("p",{className:"text-sm",children:t.agentNotes})]})]})]}):o.jsxs(ce,{children:[o.jsx(Le,{children:o.jsx(Fe,{children:"Performance Metrics"})}),o.jsx(Me,{children:o.jsx("p",{className:"text-center py-6 text-gray-500",children:"Metrics will be available after the interview is completed."})})]})}),o.jsx(Pr,{value:"transcript",className:"mt-6 space-y-6",children:t.audioUrl?o.jsxs(ce,{children:[o.jsxs(Le,{children:[o.jsx(Fe,{children:"Interview Transcript"}),o.jsxs(qs,{children:["Recorded on ",(r==null?void 0:r.toLocaleDateString())||"Unknown date"]})]}),o.jsxs(Me,{className:"space-y-4",children:[o.jsxs("div",{className:"bg-gray-50 p-4 rounded-md",children:[o.jsxs("div",{className:"flex items-center space-x-2 mb-4",children:[o.jsxs(V,{size:"sm",className:"rounded-full px-2 flex items-center gap-1",children:[o.jsx(KN,{className:"h-3 w-3"})," Play"]}),o.jsx("div",{className:"h-1 bg-gray-200 flex-grow rounded-full overflow-hidden",children:o.jsx("div",{className:"bg-primary h-1 w-1/3"})}),o.jsx("span",{className:"text-xs text-gray-500",children:"1:30 / 4:25"})]}),o.jsx("p",{className:"text-xs text-gray-500",children:"Transcript"})]}),o.jsxs("div",{className:"space-y-4",children:[o.jsxs("div",{className:"flex gap-3",children:[o.jsx("div",{className:"w-8 h-8 rounded-full bg-primary flex items-center justify-center text-white shrink-0",children:"A"}),o.jsx("div",{className:"bg-gray-50 px-3 py-2 rounded-lg",children:o.jsx("p",{className:"text-sm text-gray-700",children:"Hello, I'm Alex from MyDigitalColleague. Thank you for joining this interview for the Software Engineer role. Can you tell me about your experience with Python?"})})]}),o.jsxs("div",{className:"flex gap-3",children:[o.jsx("div",{className:"w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-700 font-medium shrink-0",children:t.name.split(" ").map(a=>a[0]).join("")}),o.jsx("div",{className:"bg-blue-50 px-3 py-2 rounded-lg",children:o.jsx("p",{className:"text-sm text-gray-700",children:"I've been working with Python for about 3 years now. I built a CRM system using Django, which included features like customer segmentation, email campaigns, and reporting dashboards."})})]}),o.jsxs("div",{className:"flex gap-3",children:[o.jsx("div",{className:"w-8 h-8 rounded-full bg-primary flex items-center justify-center text-white shrink-0",children:"A"}),o.jsx("div",{className:"bg-gray-50 px-3 py-2 rounded-lg",children:o.jsx("p",{className:"text-sm text-gray-700",children:"That sounds impressive. Can you tell me more about how you integrated AWS S3 for file storage in that project?"})})]}),o.jsxs("div",{className:"flex gap-3",children:[o.jsx("div",{className:"w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-700 font-medium shrink-0",children:t.name.split(" ").map(a=>a[0]).join("")}),o.jsx("div",{className:"bg-blue-50 px-3 py-2 rounded-lg",children:o.jsx("p",{className:"text-sm text-gray-700",children:"Sure! I used boto3, AWS's Python SDK, to handle the file operations. The system allowed users to upload documents which were stored in S3 buckets with appropriate permissions..."})})]}),o.jsx(be,{to:`/candidates/${t.id}/questions`,children:o.jsxs(V,{variant:"outline",className:"flex items-center gap-2 w-full",children:[o.jsx(qd,{className:"h-4 w-4"})," View Full Q&A"]})})]})]})]}):o.jsxs(ce,{children:[o.jsx(Le,{children:o.jsx(Fe,{children:"Interview Transcript"})}),o.jsx(Me,{children:o.jsx("p",{className:"text-center py-6 text-gray-500",children:"Transcript will be available after the interview is completed."})})]})}),o.jsx(Pr,{value:"questions",className:"mt-6",children:o.jsxs(ce,{children:[o.jsx(Le,{children:o.jsx(Fe,{children:"Interview Questions & Answers"})}),o.jsx(Me,{children:o.jsx("p",{className:"text-center py-6 text-gray-500",children:"Loading questions and answers..."})})]})})]})})]})]})},N5=()=>{const{id:e}=Hv(),t=Tt.find(s=>s.id===e),n=DP.filter(s=>s.candidateId===e);if(!t)return o.jsxs("div",{className:"text-center py-12",children:[o.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Candidate not found"}),o.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"The candidate you're looking for doesn't exist or has been removed."}),o.jsx("div",{className:"mt-6",children:o.jsx(be,{to:"/candidates",children:o.jsx(V,{children:"Back to Candidates"})})})]});const r=En.find(s=>s.id===t.jobRoleId);return o.jsxs("div",{children:[o.jsx("div",{className:"mb-6",children:o.jsxs(be,{to:`/candidates/${e}`,className:"text-primary hover:underline flex items-center",children:[o.jsx(Wg,{className:"h-4 w-4 mr-1"})," Back to Candidate Profile"]})}),o.jsx(ft,{title:`${t.name} - Interview Q&A`,description:(r==null?void 0:r.name)||"Unknown Role",actions:o.jsxs(V,{variant:"outline",className:"flex items-center gap-2",children:[o.jsx(Yr,{className:"h-4 w-4"})," Export Q&A"]})}),o.jsxs(ce,{children:[o.jsxs(Le,{children:[o.jsx(Fe,{children:"Interview Questions & Answers"}),t.scheduleDateTime&&o.jsxs(qs,{children:["Interview conducted on ",new Date(t.scheduleDateTime).toLocaleDateString()]})]}),o.jsx(Me,{children:n.length>0?o.jsx("div",{className:"space-y-6",children:n.map((s,i)=>o.jsxs("div",{className:"border-b pb-6 last:border-0 last:pb-0",children:[o.jsxs("div",{className:"flex gap-4",children:[o.jsx("div",{className:"w-10 h-10 rounded-full bg-primary flex items-center justify-center text-white shrink-0",children:"Q"}),o.jsxs("div",{children:[o.jsxs("h4",{className:"font-medium text-gray-900",children:["Question ",i+1]}),o.jsx("p",{className:"mt-1",children:s.question})]})]}),o.jsxs("div",{className:"flex gap-4 mt-4 ml-14",children:[o.jsx("div",{className:"w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-700 font-medium shrink-0",children:"A"}),o.jsxs("div",{children:[o.jsx("h4",{className:"font-medium text-gray-900",children:"Answer"}),o.jsx("p",{className:"mt-1",children:s.answer})]})]})]},s.id))}):o.jsx("p",{className:"text-center py-6 text-gray-500",children:"No questions and answers available yet."})})]}),t.metrics&&o.jsxs(ce,{className:"mt-6",children:[o.jsx(Le,{children:o.jsx(Fe,{children:"Performance Summary"})}),o.jsxs(Me,{children:[o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[o.jsxs("div",{className:"bg-blue-50 p-4 rounded-md",children:[o.jsx("div",{className:"text-sm text-gray-500",children:"Communication"}),o.jsxs("div",{className:"text-2xl font-semibold text-blue-700",children:[t.metrics.communication,"/10"]})]}),o.jsxs("div",{className:"bg-green-50 p-4 rounded-md",children:[o.jsx("div",{className:"text-sm text-gray-500",children:"Skills"}),o.jsxs("div",{className:"text-2xl font-semibold text-green-700",children:[t.metrics.skills,"/10"]})]}),o.jsxs("div",{className:"bg-purple-50 p-4 rounded-md",children:[o.jsx("div",{className:"text-sm text-gray-500",children:"Response Quality"}),o.jsxs("div",{className:"text-2xl font-semibold text-purple-700",children:[t.metrics.responseQuality,"/10"]})]})]}),t.agentNotes&&o.jsxs("div",{className:"mt-6",children:[o.jsx("h4",{className:"text-sm font-medium text-gray-500 mb-2",children:"Agent Notes"}),o.jsx("p",{className:"bg-gray-50 p-4 rounded-md",children:t.agentNotes})]})]})]})]})},b5=()=>{const[e,t]=f.useState(""),[n,r]=f.useState("all"),[s,i]=f.useState("all"),[a,l]=f.useState([]),c=Tt.filter(d=>{const v=d.name.toLowerCase().includes(e.toLowerCase()),w=n==="all"||d.interviewStatus===n,h=s==="all"||d.jobRoleId===s;return v&&w&&h}),u=d=>{const v=En.find(w=>w.id===d);return v?v.name:"Unknown"},p=d=>{l(v=>v.includes(d)?v.filter(w=>w!==d):[...v,d])},m=()=>{a.length===c.length?l([]):l(c.map(d=>d.id))};return o.jsxs("div",{children:[o.jsx(ft,{title:"Interviews",description:"Track and manage candidate interviews",actions:o.jsxs(V,{className:"bg-primary hover:bg-primary/90",children:[o.jsx(jl,{className:"mr-2 h-4 w-4"})," Schedule Interview"]})}),o.jsxs("div",{className:"mb-6 flex flex-col sm:flex-row gap-4",children:[o.jsxs("div",{className:"relative flex-grow",children:[o.jsx(gr,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),o.jsx(Be,{placeholder:"Search candidate name...",className:"pl-10",value:e,onChange:d=>t(d.target.value)})]}),o.jsxs("div",{className:"grid grid-cols-2 sm:grid-cols-3 gap-4 sm:w-auto",children:[o.jsxs(kn,{value:s,onValueChange:i,children:[o.jsx(ln,{className:"w-full",children:o.jsx(Pn,{placeholder:"All Roles"})}),o.jsxs(cn,{children:[o.jsx(le,{value:"all",children:"All Roles"}),En.map(d=>o.jsx(le,{value:d.id,children:d.name},d.id))]})]}),o.jsxs(kn,{value:n,onValueChange:r,children:[o.jsx(ln,{className:"w-full",children:o.jsx(Pn,{placeholder:"All Status"})}),o.jsxs(cn,{children:[o.jsx(le,{value:"all",children:"All Status"}),o.jsx(le,{value:"Scheduled",children:"Scheduled"}),o.jsx(le,{value:"Completed",children:"Completed"}),o.jsx(le,{value:"Not Scheduled",children:"Not Scheduled"})]})]}),o.jsxs(V,{variant:"outline",className:"flex items-center gap-2",children:[o.jsx(Yr,{className:"h-4 w-4"})," Export CSV"]})]})]}),a.length>0&&o.jsxs("div",{className:"mb-4 p-2 bg-blue-50 border border-blue-100 rounded flex items-center justify-between",children:[o.jsxs("span",{className:"text-sm",children:[a.length," ",a.length===1?"interview":"interviews"," selected"]}),o.jsxs("div",{className:"flex gap-2",children:[o.jsx(V,{variant:"outline",size:"sm",className:"bg-white",children:"Reschedule"}),o.jsx(V,{variant:"outline",size:"sm",className:"bg-white text-red-500 hover:text-red-600",children:"Cancel Interviews"}),o.jsx(V,{variant:"ghost",size:"sm",onClick:()=>l([]),children:o.jsx(Zd,{className:"h-4 w-4"})})]})]}),o.jsx("div",{className:"bg-white overflow-hidden shadow-sm rounded-lg",children:o.jsx("div",{className:"overflow-x-auto",children:o.jsxs("table",{className:"w-full",children:[o.jsx("thead",{className:"bg-gray-50 text-left",children:o.jsxs("tr",{children:[o.jsx("th",{className:"px-4 py-3 text-xs font-medium text-gray-500",children:o.jsx("input",{type:"checkbox",className:"rounded text-primary focus:ring-primary",checked:a.length===c.length&&c.length>0,onChange:m})}),o.jsx("th",{className:"px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Candidate"}),o.jsx("th",{className:"px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Position"}),o.jsx("th",{className:"px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date & Time"}),o.jsx("th",{className:"px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),o.jsx("th",{className:"px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Score"}),o.jsx("th",{className:"px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),o.jsx("tbody",{className:"divide-y divide-gray-200",children:c.length===0?o.jsx("tr",{children:o.jsx("td",{colSpan:7,className:"px-6 py-10 text-center text-gray-500",children:"No interviews found. Try adjusting your search or filters."})}):c.map(d=>o.jsxs("tr",{className:`hover:bg-gray-50 ${a.includes(d.id)?"bg-blue-50":""}`,children:[o.jsx("td",{className:"px-4 py-4 whitespace-nowrap",children:o.jsx("input",{type:"checkbox",className:"rounded text-primary focus:ring-primary",checked:a.includes(d.id),onChange:()=>p(d.id)})}),o.jsx("td",{className:"px-4 py-4 whitespace-nowrap",children:o.jsxs("div",{className:"flex items-center",children:[o.jsx("div",{className:"h-8 w-8 rounded-full bg-primary/20 flex items-center justify-center text-primary font-medium",children:d.name.split(" ").map(v=>v[0]).join("")}),o.jsxs("div",{className:"ml-3",children:[o.jsx("div",{className:"text-sm font-medium text-gray-900",children:d.name}),o.jsx("div",{className:"text-xs text-gray-500",children:d.email})]})]})}),o.jsx("td",{className:"px-4 py-4 whitespace-nowrap text-sm text-gray-500",children:u(d.jobRoleId)}),o.jsx("td",{className:"px-4 py-4 whitespace-nowrap text-sm text-gray-500",children:d.scheduleDateTime?new Date(d.scheduleDateTime).toLocaleString():"Not scheduled"}),o.jsx("td",{className:"px-4 py-4 whitespace-nowrap",children:o.jsx(ir,{status:d.interviewStatus})}),o.jsx("td",{className:"px-4 py-4 whitespace-nowrap text-sm text-gray-500",children:d.metrics?`${d.metrics.overallScore}/100`:"N/A"}),o.jsx("td",{className:"px-4 py-4 whitespace-nowrap text-sm",children:o.jsxs("div",{className:"flex gap-2",children:[o.jsx(be,{to:`/candidates/${d.id}`,children:o.jsx(V,{variant:"ghost",size:"sm",children:"View"})}),d.interviewStatus==="Scheduled"&&o.jsx(V,{variant:"outline",size:"sm",children:"Reschedule"}),d.interviewStatus==="Not Scheduled"&&o.jsx(V,{size:"sm",className:"bg-primary hover:bg-primary/90",children:"Schedule"})]})})]},d.id))})]})})}),o.jsxs("div",{className:"mt-6 flex justify-between items-center",children:[o.jsxs("div",{className:"text-sm text-gray-500",children:["Showing ",c.length," of ",Tt.length," interviews"]}),o.jsxs("div",{className:"flex gap-2",children:[o.jsx(V,{variant:"outline",size:"sm",disabled:!0,children:"Previous"}),o.jsx(V,{variant:"outline",size:"sm",disabled:!0,children:"Next"})]})]})]})},S5=()=>{const[e,t]=f.useState(""),[n,r]=f.useState("all"),[s,i]=f.useState("all"),a=FP.filter(c=>{var d;const u=((d=c.candidateName)==null?void 0:d.toLowerCase().includes(e.toLowerCase()))||c.details.toLowerCase().includes(e.toLowerCase()),p=n==="all"||c.action===n,m=s==="all"||s==="today"&&c.timestamp.includes("2025-05-22")||s==="yesterday"&&c.timestamp.includes("2025-05-21")||s==="thisWeek"&&(c.timestamp.includes("2025-05-22")||c.timestamp.includes("2025-05-21")||c.timestamp.includes("2025-05-20")||c.timestamp.includes("2025-05-19"));return u&&p&&m}),l=c=>{switch(c){case"Emailed":return o.jsx(Gs,{className:"h-4 w-4 text-blue-500"});case"Called":return o.jsx(bn,{className:"h-4 w-4 text-green-500"});case"Scheduled Interview":return o.jsx(jl,{className:"h-4 w-4 text-purple-500"});case"Interview Completed":return o.jsx(qd,{className:"h-4 w-4 text-amber-500"});default:return o.jsx(Gs,{className:"h-4 w-4 text-gray-500"})}};return o.jsxs("div",{children:[o.jsx(ft,{title:"Agent Activity Log",description:"Track all actions performed by Alex",actions:o.jsxs(V,{variant:"outline",className:"flex items-center gap-2",children:[o.jsx(Yr,{className:"h-4 w-4"})," Export CSV"]})}),o.jsxs("div",{className:"mb-6 flex flex-col sm:flex-row gap-4",children:[o.jsxs("div",{className:"relative flex-grow",children:[o.jsx(gr,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),o.jsx(Be,{placeholder:"Search by candidate or details...",className:"pl-10",value:e,onChange:c=>t(c.target.value)})]}),o.jsxs("div",{className:"grid grid-cols-2 gap-4 sm:w-auto",children:[o.jsxs(kn,{value:n,onValueChange:r,children:[o.jsx(ln,{className:"w-full",children:o.jsx(Pn,{placeholder:"All Actions"})}),o.jsxs(cn,{children:[o.jsx(le,{value:"all",children:"All Actions"}),o.jsx(le,{value:"Emailed",children:"Emailed"}),o.jsx(le,{value:"Called",children:"Called"}),o.jsx(le,{value:"Scheduled Interview",children:"Scheduled"}),o.jsx(le,{value:"Interview Completed",children:"Completed"})]})]}),o.jsxs(kn,{value:s,onValueChange:i,children:[o.jsx(ln,{className:"w-full",children:o.jsx(Pn,{placeholder:"All Dates"})}),o.jsxs(cn,{children:[o.jsx(le,{value:"all",children:"All Dates"}),o.jsx(le,{value:"today",children:"Today"}),o.jsx(le,{value:"yesterday",children:"Yesterday"}),o.jsx(le,{value:"thisWeek",children:"This Week"})]})]})]})]}),o.jsx("div",{className:"bg-white overflow-hidden shadow-sm rounded-lg",children:o.jsx("div",{className:"overflow-x-auto",children:o.jsxs("table",{className:"w-full",children:[o.jsx("thead",{className:"bg-gray-50 text-left",children:o.jsxs("tr",{children:[o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Timestamp"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Action"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Candidate"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Details"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"View"})]})}),o.jsx("tbody",{className:"divide-y divide-gray-200",children:a.length===0?o.jsx("tr",{children:o.jsx("td",{colSpan:5,className:"px-6 py-10 text-center text-gray-500",children:"No activities found. Try adjusting your search or filters."})}):a.map(c=>o.jsxs("tr",{className:"hover:bg-gray-50",children:[o.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:c.timestamp}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:o.jsxs("div",{className:"flex items-center",children:[l(c.action),o.jsx("span",{className:"ml-2 text-sm font-medium",children:c.action})]})}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm",children:c.candidateName?o.jsx(be,{to:`/candidates/${c.candidateId}`,className:"text-primary hover:underline",children:c.candidateName}):o.jsx("span",{className:"text-gray-500",children:"-"})}),o.jsx("td",{className:"px-6 py-4 text-sm text-gray-500 max-w-md truncate",children:c.details}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm",children:c.candidateId&&o.jsx(be,{to:`/candidates/${c.candidateId}`,children:o.jsx(V,{variant:"ghost",size:"sm",children:"View"})})})]},c.id))})]})})})]})},C5=()=>o.jsxs("div",{children:[o.jsx(ft,{title:"Alex Configuration",description:"View and manage your AI agent settings",actions:o.jsx(be,{to:"/onboarding",children:o.jsxs(V,{className:"bg-primary hover:bg-primary/90",children:[o.jsx(Xd,{className:"mr-2 h-4 w-4"})," Edit Configuration"]})})}),o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[o.jsxs(ce,{children:[o.jsx(Le,{children:o.jsxs(Fe,{className:"flex items-center",children:[o.jsx(Gs,{className:"mr-2 h-5 w-5 text-primary"})," Email Configuration"]})}),o.jsx(Me,{children:o.jsxs("div",{className:"space-y-4",children:[o.jsxs("div",{className:"flex justify-between",children:[o.jsx("span",{className:"text-sm font-medium text-gray-500",children:"Provider"}),o.jsx("span",{className:"text-sm",children:Et.email.provider})]}),o.jsxs("div",{className:"flex justify-between",children:[o.jsx("span",{className:"text-sm font-medium text-gray-500",children:"Email Address"}),o.jsx("span",{className:"text-sm",children:Et.email.user})]}),o.jsxs("div",{className:"flex justify-between",children:[o.jsx("span",{className:"text-sm font-medium text-gray-500",children:"Authentication"}),o.jsxs("span",{className:"text-sm font-mono bg-gray-100 px-2 py-0.5 rounded text-xs",children:[Et.email.token.slice(0,5),"...",Et.email.token.slice(-5)]})]}),o.jsx("div",{className:"pt-2 mt-2 border-t",children:o.jsx("p",{className:"text-xs text-gray-500",children:"Alex uses this email account to contact candidates for scheduling interviews."})})]})})]}),o.jsxs(ce,{children:[o.jsx(Le,{children:o.jsxs(Fe,{className:"flex items-center",children:[o.jsx(Jd,{className:"mr-2 h-5 w-5 text-primary"})," Agent Configuration"]})}),o.jsx(Me,{children:o.jsxs("div",{className:"space-y-4",children:[o.jsxs("div",{className:"flex justify-between",children:[o.jsx("span",{className:"text-sm font-medium text-gray-500",children:"Agent Name"}),o.jsx("span",{className:"text-sm",children:Et.agentName})]}),o.jsx("div",{className:"pt-2 mt-2 border-t",children:o.jsx("p",{className:"text-xs text-gray-500",children:"This name is used when Alex introduces itself to candidates during interviews."})})]})})]}),o.jsxs(ce,{className:"md:col-span-2",children:[o.jsx(Le,{children:o.jsxs(Fe,{className:"flex items-center",children:[o.jsx(bn,{className:"mr-2 h-5 w-5 text-primary"})," Twilio Configuration"]})}),o.jsx(Me,{children:o.jsxs("div",{className:"space-y-4",children:[o.jsxs("div",{className:"flex justify-between",children:[o.jsx("span",{className:"text-sm font-medium text-gray-500",children:"Phone Number"}),o.jsx("span",{className:"text-sm",children:Et.twilioNumber})]}),o.jsxs("div",{className:"flex justify-between",children:[o.jsx("span",{className:"text-sm font-medium text-gray-500",children:"Assigned Date"}),o.jsx("span",{className:"text-sm",children:Et.assignedDate})]}),o.jsx("div",{className:"pt-2 mt-2 border-t",children:o.jsx("p",{className:"text-xs text-gray-500",children:"This phone number is used by Alex to call candidates. It is managed by Alex Interviewer and cannot be changed."})})]})})]})]}),o.jsx("div",{className:"mt-6",children:o.jsx(ce,{className:"bg-blue-50 border-blue-100",children:o.jsx(Me,{className:"p-4",children:o.jsxs("div",{className:"flex items-start",children:[o.jsx("div",{className:"bg-blue-100 rounded-full p-2 mr-4",children:o.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-blue-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),o.jsxs("div",{children:[o.jsx("h3",{className:"text-lg font-medium text-blue-800 mb-1",children:"Need to change your configuration?"}),o.jsx("p",{className:"text-blue-700",children:"You can update your email provider, agent name, and other settings through the onboarding process."}),o.jsx(be,{to:"/onboarding",className:"mt-4 inline-block",children:o.jsx(V,{className:"bg-blue-700 hover:bg-blue-800",children:"Go to Onboarding"})})]})]})})})})]}),No=[{id:"email",title:"Email Setup",description:"Connect your email for Alex"},{id:"agent",title:"Agent Name",description:"Personalize your AI agent"},{id:"twilio",title:"Twilio",description:"View your assigned number"}],E5=()=>{const[e,t]=f.useState("email"),[n,r]=f.useState(Et.email.provider),[s,i]=f.useState(Et.agentName),a=qr(),{user:l,logout:c}=Xr(),{toast:u}=Qr(),p=()=>{e==="email"?t("agent"):e==="agent"?t("twilio"):(u({title:"Configuration Saved",description:"Your Alex configuration has been updated"}),a("/dashboard"))},m=()=>{e==="twilio"?t("agent"):e==="agent"&&t("email")},d=()=>{u({title:"Google Authentication",description:"Successfully connected with Gmail"})},v=()=>{u({title:"Microsoft Authentication",description:"Successfully connected with Outlook"})};return o.jsxs("div",{className:"min-h-screen bg-gray-50",children:[o.jsx("header",{className:"bg-white shadow-sm z-10 relative",children:o.jsx("div",{className:"container-page py-4",children:o.jsxs("div",{className:"flex justify-between items-center",children:[o.jsx(Vr,{}),l&&o.jsx(Rf,{userEmail:l.email,onLogout:c})]})})}),o.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-12",children:o.jsxs("div",{className:"max-w-3xl mx-auto",children:[o.jsx("nav",{"aria-label":"Progress",className:"mb-12",children:o.jsx("ol",{className:"flex items-center",children:No.map((w,h)=>o.jsxs("li",{className:`relative ${h===No.length-1?"":"pr-8"} ${h===0?"":"pl-8"} flex-1`,children:[h!==0&&o.jsx("div",{className:"absolute inset-0 flex items-center","aria-hidden":"true",children:o.jsx("div",{className:`h-0.5 w-full ${e===w.id||No.findIndex(j=>j.id===e)>h?"bg-primary":"bg-gray-200"}`})}),o.jsxs("div",{className:"relative flex flex-col items-center group",children:[o.jsx("span",{className:`h-9 w-9 rounded-full flex items-center justify-center ${e===w.id||No.findIndex(j=>j.id===e)>h?"bg-primary text-white":"bg-white border-2 border-gray-300 text-gray-500"}`,children:No.findIndex(j=>j.id===e)>h?o.jsx(Nl,{className:"h-5 w-5"}):h+1}),o.jsxs("div",{className:"mt-3 hidden sm:block",children:[o.jsx("span",{className:"text-sm font-medium",children:w.title}),o.jsx("p",{className:"text-xs text-gray-500",children:w.description})]})]})]},w.id))})}),o.jsxs("div",{className:"bg-white shadow sm:rounded-lg",children:[o.jsxs("div",{className:"px-4 py-5 sm:p-6",children:[e==="email"&&o.jsxs("div",{children:[o.jsx("h2",{className:"text-lg font-medium text-gray-900 mb-6",children:"Email Setup"}),o.jsx("p",{className:"mb-6 text-gray-500",children:"Connect your email account to allow Alex to send emails to candidates. Choose your email provider below."}),o.jsxs("div",{className:"space-y-6",children:[o.jsxs("div",{className:"space-y-2",children:[o.jsx(Bt,{htmlFor:"email-provider",children:"Email Provider"}),o.jsxs(kn,{value:n,onValueChange:r,children:[o.jsx(ln,{id:"email-provider",children:o.jsx(Pn,{placeholder:"Select an email provider"})}),o.jsxs(cn,{children:[o.jsx(le,{value:"Gmail",children:"Gmail"}),o.jsx(le,{value:"Outlook",children:"Outlook"})]})]})]}),o.jsxs("div",{className:"pt-4 border-t",children:[o.jsx("h3",{className:"text-sm font-medium text-gray-900 mb-3",children:"Connect Your Account"}),o.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[o.jsxs(V,{type:"button",variant:"outline",className:"flex items-center justify-center gap-2",onClick:d,disabled:n!=="Gmail",children:[o.jsx("svg",{width:"18",height:"18",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 488 512",children:o.jsx("path",{fill:"currentColor",d:"M488 261.8C488 403.3 391.1 504 248 504 110.8 504 0 393.2 0 256S110.8 8 248 8c66.8 0 123 24.5 166.3 64.9l-67.5 64.9C258.5 52.6 94.3 116.6 94.3 256c0 86.5 69.1 156.6 153.7 156.6 98.2 0 135-70.4 140.8-106.9H248v-85.3h236.1c2.3 12.7 3.9 24.9 3.9 41.4z"})}),"Sign in with Google"]}),o.jsxs(V,{type:"button",variant:"outline",className:"flex items-center justify-center gap-2",onClick:v,disabled:n!=="Outlook",children:[o.jsxs("svg",{width:"18",height:"18",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 23 23",children:[o.jsx("path",{fill:"#f3f3f3",d:"M0 0h23v23H0z"}),o.jsx("path",{fill:"#f35325",d:"M1 1h10v10H1z"}),o.jsx("path",{fill:"#81bc06",d:"M12 1h10v10H12z"}),o.jsx("path",{fill:"#05a6f0",d:"M1 12h10v10H1z"}),o.jsx("path",{fill:"#ffba08",d:"M12 12h10v10H12z"})]}),"Sign in with Microsoft"]})]})]}),o.jsx("div",{className:"bg-blue-50 p-4 rounded-md",children:o.jsxs("div",{className:"flex",children:[o.jsx("div",{className:"flex-shrink-0",children:o.jsx(Gs,{className:"h-5 w-5 text-blue-400"})}),o.jsxs("div",{className:"ml-3",children:[o.jsx("h3",{className:"text-sm font-medium text-blue-800",children:"Connected Account"}),o.jsxs("div",{className:"mt-2 text-sm text-blue-700",children:[o.jsxs("p",{children:["Email: ",Et.email.user]}),o.jsx("p",{className:"mt-1",children:"Status: ✅ Connected"})]})]})]})})]})]}),e==="agent"&&o.jsxs("div",{children:[o.jsx("h2",{className:"text-lg font-medium text-gray-900 mb-6",children:"Agent Name"}),o.jsx("p",{className:"mb-6 text-gray-500",children:"Personalize your AI interviewer's name. This name will be used when communicating with candidates."}),o.jsxs("div",{className:"space-y-6",children:[o.jsxs("div",{className:"space-y-2",children:[o.jsx(Bt,{htmlFor:"agent-name",children:"Agent Name"}),o.jsx(Be,{id:"agent-name",value:s,onChange:w=>i(w.target.value),className:"max-w-md",placeholder:"e.g. Alex"}),o.jsx("p",{className:"text-xs text-gray-500",children:'The default name is "Alex", but you can customize it to anything you prefer.'})]}),o.jsx("div",{className:"bg-blue-50 p-4 rounded-md",children:o.jsxs("div",{className:"flex",children:[o.jsx("div",{className:"flex-shrink-0",children:o.jsx(Jd,{className:"h-5 w-5 text-blue-400"})}),o.jsxs("div",{className:"ml-3",children:[o.jsx("h3",{className:"text-sm font-medium text-blue-800",children:"Agent Preview"}),o.jsx("div",{className:"mt-2 text-sm text-blue-700",children:o.jsxs("p",{children:["Hello, I'm ",s||"Alex"," from MyDigitalColleague. I'll be conducting your technical interview today."]})})]})]})})]})]}),e==="twilio"&&o.jsxs("div",{children:[o.jsx("h2",{className:"text-lg font-medium text-gray-900 mb-6",children:"Twilio Configuration"}),o.jsx("p",{className:"mb-6 text-gray-500",children:"View your assigned Twilio phone number. This number is managed by Alex Interviewer."}),o.jsxs("div",{className:"space-y-6",children:[o.jsx("div",{className:"bg-gray-50 border border-gray-200 rounded-md p-4",children:o.jsxs("div",{className:"flex",children:[o.jsx("div",{className:"flex-shrink-0",children:o.jsx(bn,{className:"h-5 w-5 text-gray-400"})}),o.jsxs("div",{className:"ml-3",children:[o.jsx("h3",{className:"text-sm font-medium text-gray-800",children:"Assigned Phone Number"}),o.jsxs("div",{className:"mt-2 text-sm text-gray-700",children:[o.jsx("p",{className:"font-medium",children:Et.twilioNumber}),o.jsxs("p",{className:"mt-1 text-gray-500",children:["Assigned on ",Et.assignedDate]})]})]})]})}),o.jsx("div",{className:"bg-yellow-50 p-4 rounded-md",children:o.jsxs("div",{className:"flex",children:[o.jsx("div",{className:"flex-shrink-0",children:o.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-yellow-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),o.jsxs("div",{className:"ml-3",children:[o.jsx("h3",{className:"text-sm font-medium text-yellow-800",children:"Important Note"}),o.jsx("div",{className:"mt-2 text-sm text-yellow-700",children:o.jsx("p",{children:"This phone number is managed by Alex Interviewer and cannot be changed. If you need a different number, please contact support."})})]})]})})]})]})]}),o.jsxs("div",{className:"px-4 py-3 bg-gray-50 sm:px-6 flex justify-between rounded-b-lg",children:[e!=="email"&&o.jsx(V,{type:"button",variant:"outline",onClick:m,children:"Back"}),e==="email"&&o.jsx("div",{}),o.jsx(V,{type:"button",className:"bg-primary hover:bg-primary/90",onClick:p,children:e==="twilio"?"Complete":"Next"})]})]})]})})]})},k5=()=>{const e=ys.filter(i=>i.status==="Active").length,t=ys.filter(i=>i.plan==="Basic").length,n=ys.filter(i=>i.plan==="Pro").length,r=ys.filter(i=>i.plan==="Voucher").length,s=Kp.reduce((i,a)=>i+a.amount,0);return o.jsxs("div",{children:[o.jsx(ft,{title:"Admin Dashboard",description:"Overview of system metrics and user statistics"}),o.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[o.jsx(ce,{className:"p-6",children:o.jsxs("div",{className:"flex items-start",children:[o.jsx("div",{className:"p-2 bg-blue-100 rounded-md",children:o.jsx(Ua,{className:"h-6 w-6 text-blue-600"})}),o.jsxs("div",{className:"ml-4",children:[o.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Active Users"}),o.jsx("p",{className:"mt-1 text-2xl font-semibold",children:e})]})]})}),o.jsx(ce,{className:"p-6",children:o.jsxs("div",{className:"flex items-start",children:[o.jsx("div",{className:"p-2 bg-green-100 rounded-md",children:o.jsx(sp,{className:"h-6 w-6 text-green-600"})}),o.jsxs("div",{className:"ml-4",children:[o.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Total Revenue"}),o.jsxs("p",{className:"mt-1 text-2xl font-semibold",children:["$",s]})]})]})}),o.jsx(ce,{className:"p-6",children:o.jsxs("div",{className:"flex items-start",children:[o.jsx("div",{className:"p-2 bg-purple-100 rounded-md",children:o.jsx(bn,{className:"h-6 w-6 text-purple-600"})}),o.jsxs("div",{className:"ml-4",children:[o.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Twilio Numbers"}),o.jsx("p",{className:"mt-1 text-2xl font-semibold",children:"3"})]})]})}),o.jsx(ce,{className:"p-6",children:o.jsxs("div",{className:"flex items-start",children:[o.jsx("div",{className:"p-2 bg-red-100 rounded-md",children:o.jsx(Tu,{className:"h-6 w-6 text-red-600"})}),o.jsxs("div",{className:"ml-4",children:[o.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Open Issues"}),o.jsx("p",{className:"mt-1 text-2xl font-semibold",children:"2"})]})]})})]}),o.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[o.jsxs(ce,{className:"p-6",children:[o.jsx("h3",{className:"text-lg font-medium mb-4",children:"Subscription Distribution"}),o.jsxs("div",{className:"flex items-center space-x-4",children:[o.jsxs("div",{className:"w-32 h-32 relative",children:[o.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:o.jsx("span",{className:"text-lg font-medium",children:e})}),o.jsxs("svg",{className:"w-full h-full",viewBox:"0 0 36 36",children:[o.jsx("circle",{cx:"18",cy:"18",r:"16",fill:"none",stroke:"#f3f4f6",strokeWidth:"2"}),o.jsx("circle",{cx:"18",cy:"18",r:"16",fill:"none",stroke:"#3b82f6",strokeWidth:"2",strokeDasharray:`${t/e*100} 100`,strokeDashoffset:"25"}),o.jsx("circle",{cx:"18",cy:"18",r:"16",fill:"none",stroke:"#10b981",strokeWidth:"2",strokeDasharray:`${n/e*100} 100`,strokeDashoffset:`${100-t/e*100+25}`}),o.jsx("circle",{cx:"18",cy:"18",r:"16",fill:"none",stroke:"#f59e0b",strokeWidth:"2",strokeDasharray:`${r/e*100} 100`,strokeDashoffset:`${100-t/e*100-n/e*100+25}`})]})]}),o.jsxs("div",{className:"space-y-2",children:[o.jsxs("div",{className:"flex items-center",children:[o.jsx("span",{className:"w-3 h-3 bg-blue-500 rounded-full mr-2"}),o.jsxs("span",{className:"text-sm",children:["Basic: ",t]})]}),o.jsxs("div",{className:"flex items-center",children:[o.jsx("span",{className:"w-3 h-3 bg-green-500 rounded-full mr-2"}),o.jsxs("span",{className:"text-sm",children:["Pro: ",n]})]}),o.jsxs("div",{className:"flex items-center",children:[o.jsx("span",{className:"w-3 h-3 bg-amber-500 rounded-full mr-2"}),o.jsxs("span",{className:"text-sm",children:["Voucher: ",r]})]})]})]})]}),o.jsxs(ce,{className:"p-6",children:[o.jsx("h3",{className:"text-lg font-medium mb-4",children:"Recent System Activity"}),o.jsxs("div",{className:"space-y-4",children:[o.jsxs("div",{className:"flex items-start",children:[o.jsx("div",{className:"p-1 bg-green-100 rounded-full",children:o.jsx(sp,{className:"h-4 w-4 text-green-600"})}),o.jsxs("div",{className:"ml-3",children:[o.jsx("p",{className:"text-sm",children:"Jane Smith upgraded to Pro plan"}),o.jsx("p",{className:"text-xs text-gray-500",children:"2025-05-10"})]})]}),o.jsxs("div",{className:"flex items-start",children:[o.jsx("div",{className:"p-1 bg-blue-100 rounded-full",children:o.jsx(Ua,{className:"h-4 w-4 text-blue-600"})}),o.jsxs("div",{className:"ml-3",children:[o.jsx("p",{className:"text-sm",children:"New user registered: John Doe"}),o.jsx("p",{className:"text-xs text-gray-500",children:"2025-05-02"})]})]}),o.jsxs("div",{className:"flex items-start",children:[o.jsx("div",{className:"p-1 bg-red-100 rounded-full",children:o.jsx(Tu,{className:"h-4 w-4 text-red-600"})}),o.jsxs("div",{className:"ml-3",children:[o.jsx("p",{className:"text-sm",children:"Login issue reported by Jane Smith"}),o.jsx("p",{className:"text-xs text-gray-500",children:"2025-05-20"})]})]}),o.jsxs("div",{className:"flex items-start",children:[o.jsx("div",{className:"p-1 bg-purple-100 rounded-full",children:o.jsx(bn,{className:"h-4 w-4 text-purple-600"})}),o.jsxs("div",{className:"ml-3",children:[o.jsx("p",{className:"text-sm",children:"Twilio number assigned to Sarah Johnson"}),o.jsx("p",{className:"text-xs text-gray-500",children:"2025-05-05"})]})]})]})]}),o.jsxs(ce,{className:"p-6",children:[o.jsx("h3",{className:"text-lg font-medium mb-4",children:"Recent Subscriptions"}),o.jsx("div",{className:"space-y-2",children:Kp.slice(0,3).map(i=>o.jsxs("div",{className:"flex justify-between items-center p-2 bg-gray-50 rounded-md",children:[o.jsxs("div",{children:[o.jsx("p",{className:"font-medium text-sm",children:i.userName}),o.jsxs("p",{className:"text-xs text-gray-500",children:[i.invoiceNumber," • ",i.date]})]}),o.jsx("div",{children:o.jsxs("span",{className:"font-medium text-green-600",children:["$",i.amount]})})]},i.id))})]}),o.jsxs(ce,{className:"p-6",children:[o.jsx("h3",{className:"text-lg font-medium mb-4",children:"Quick Stats"}),o.jsxs("div",{className:"space-y-3",children:[o.jsxs("div",{className:"flex justify-between",children:[o.jsx("span",{className:"text-sm text-gray-600",children:"Total Calls Used"}),o.jsx("span",{className:"font-medium",children:"202/625"})]}),o.jsxs("div",{className:"flex justify-between",children:[o.jsx("span",{className:"text-sm text-gray-600",children:"Average Score"}),o.jsx("span",{className:"font-medium",children:"76/100"})]}),o.jsxs("div",{className:"flex justify-between",children:[o.jsx("span",{className:"text-sm text-gray-600",children:"Pass Rate"}),o.jsx("span",{className:"font-medium",children:"67%"})]}),o.jsxs("div",{className:"flex justify-between",children:[o.jsx("span",{className:"text-sm text-gray-600",children:"Server Status"}),o.jsx("span",{className:"text-green-600 font-medium",children:"Operational"})]})]})]})]})]})},P5=()=>{const[e,t]=f.useState(""),n=ys.filter(r=>r.name.toLowerCase().includes(e.toLowerCase())||r.email.toLowerCase().includes(e.toLowerCase()));return o.jsxs("div",{children:[o.jsx(ft,{title:"User & Subscription Management",description:"Manage users and their subscription details",actions:o.jsxs("div",{className:"flex gap-2",children:[o.jsxs(V,{variant:"outline",className:"flex items-center gap-2",children:[o.jsx(Yr,{className:"h-4 w-4"})," Export"]}),o.jsxs(V,{className:"bg-primary hover:bg-primary/90",children:[o.jsx($a,{className:"h-4 w-4 mr-2"})," Add User"]})]})}),o.jsx("div",{className:"mb-6",children:o.jsxs("div",{className:"relative",children:[o.jsx(gr,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),o.jsx(Be,{placeholder:"Search users by name or email...",className:"pl-10",value:e,onChange:r=>t(r.target.value)})]})}),o.jsx("div",{className:"bg-white overflow-hidden shadow-sm rounded-lg",children:o.jsx("div",{className:"overflow-x-auto",children:o.jsxs("table",{className:"w-full",children:[o.jsx("thead",{className:"bg-gray-50 text-left",children:o.jsxs("tr",{children:[o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Plan"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Usage"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Last Reset"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),o.jsx("tbody",{className:"divide-y divide-gray-200",children:n.map(r=>o.jsxs("tr",{className:"hover:bg-gray-50",children:[o.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:o.jsxs("div",{className:"flex items-center",children:[o.jsx("div",{className:"h-10 w-10 rounded-full bg-primary/20 flex items-center justify-center text-primary font-medium",children:r.name.split(" ").map(s=>s[0]).join("")}),o.jsxs("div",{className:"ml-4",children:[o.jsx("div",{className:"text-sm font-medium text-gray-900",children:r.name}),o.jsx("div",{className:"text-sm text-gray-500",children:r.email})]})]})}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:o.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${r.plan==="Pro"?"bg-green-100 text-green-800":r.plan==="Basic"?"bg-blue-100 text-blue-800":"bg-amber-100 text-amber-800"}`,children:r.plan})}),o.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:[r.callsUsed,"/",r.callsTotal," calls"]}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:o.jsx(ir,{status:r.status})}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:r.lastReset||"-"}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:o.jsxs("div",{className:"flex space-x-2 justify-end",children:[o.jsx(V,{variant:"outline",size:"sm",children:"Edit"}),o.jsx(V,{variant:"outline",size:"sm",children:"Manage"}),o.jsx(V,{variant:"ghost",size:"sm",className:"text-red-500 hover:text-red-700 hover:bg-red-50",children:r.status==="Active"?"Suspend":"Activate"})]})})]},r.id))})]})})}),o.jsxs("div",{className:"mt-8",children:[o.jsx("h2",{className:"text-lg font-medium mb-4",children:"Recent Subscription Activity"}),o.jsx("div",{className:"bg-white overflow-hidden shadow-sm rounded-lg",children:o.jsx("div",{className:"overflow-x-auto",children:o.jsxs("table",{className:"w-full",children:[o.jsx("thead",{className:"bg-gray-50 text-left",children:o.jsxs("tr",{children:[o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Action"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Details"})]})}),o.jsxs("tbody",{className:"divide-y divide-gray-200",children:[o.jsxs("tr",{className:"hover:bg-gray-50",children:[o.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:o.jsx("div",{className:"text-sm font-medium",children:"Jane Smith"})}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:o.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"Upgraded"})}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"2025-05-10"}),o.jsx("td",{className:"px-6 py-4 text-sm text-gray-500",children:"Upgraded from Basic to Pro. Invoice #INV003, $150 (prorated)."})]}),o.jsxs("tr",{className:"hover:bg-gray-50",children:[o.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:o.jsx("div",{className:"text-sm font-medium",children:"John Doe"})}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:o.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800",children:"Downgraded"})}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"2025-05-15"}),o.jsx("td",{className:"px-6 py-4 text-sm text-gray-500",children:"Downgraded from Pro to Basic, effective 2025-06-01."})]}),o.jsxs("tr",{className:"hover:bg-gray-50",children:[o.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:o.jsx("div",{className:"text-sm font-medium",children:"Jane Smith"})}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:o.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800",children:"Cancelled"})}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"2025-05-18"}),o.jsx("td",{className:"px-6 py-4 text-sm text-gray-500",children:"Cancelled Basic subscription, ending 2025-05-31."})]})]})]})})})]})]})},T5=()=>{const[e,t]=f.useState(""),{toast:n}=Qr(),r=ys.filter(a=>a.name.toLowerCase().includes(e.toLowerCase())||a.email.toLowerCase().includes(e.toLowerCase())),s=a=>{n({title:"Password Reset",description:`Password reset email sent to ${a}`})},i=a=>{n({title:"Email Credentials Reset",description:`Email credentials reset for ${a}`})};return o.jsxs("div",{children:[o.jsx(ft,{title:"User Account Management",description:"Reset passwords and email credentials"}),o.jsx("div",{className:"mb-6",children:o.jsxs("div",{className:"relative",children:[o.jsx(gr,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),o.jsx(Be,{placeholder:"Search users by name or email...",className:"pl-10",value:e,onChange:a=>t(a.target.value)})]})}),o.jsx("div",{className:"bg-white overflow-hidden shadow-sm rounded-lg",children:o.jsx("div",{className:"overflow-x-auto",children:o.jsxs("table",{className:"w-full",children:[o.jsx("thead",{className:"bg-gray-50 text-left",children:o.jsxs("tr",{children:[o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Email"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Last Reset"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),o.jsx("tbody",{className:"divide-y divide-gray-200",children:r.map(a=>o.jsxs("tr",{className:"hover:bg-gray-50",children:[o.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:o.jsxs("div",{className:"flex items-center",children:[o.jsx("div",{className:"h-10 w-10 rounded-full bg-primary/20 flex items-center justify-center text-primary font-medium",children:a.name.split(" ").map(l=>l[0]).join("")}),o.jsx("div",{className:"ml-4",children:o.jsx("div",{className:"text-sm font-medium text-gray-900",children:a.name})})]})}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm",children:a.email}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:a.lastReset||"Never"}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:o.jsxs("div",{className:"flex space-x-2",children:[o.jsxs(V,{variant:"outline",size:"sm",className:"flex items-center gap-1",onClick:()=>s(a.name),children:[o.jsx(op,{className:"h-3 w-3"})," Reset Password"]}),o.jsxs(V,{variant:"outline",size:"sm",className:"flex items-center gap-1",onClick:()=>i(a.name),children:[o.jsx(op,{className:"h-3 w-3"})," Reset Email"]})]})})]},a.id))})]})})}),o.jsxs("div",{className:"mt-8",children:[o.jsx("h2",{className:"text-lg font-medium mb-4",children:"Recent Reset History"}),o.jsx("div",{className:"bg-white overflow-hidden shadow-sm rounded-lg",children:o.jsx("div",{className:"overflow-x-auto",children:o.jsxs("table",{className:"w-full",children:[o.jsx("thead",{className:"bg-gray-50 text-left",children:o.jsxs("tr",{children:[o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Requested By"})]})}),o.jsxs("tbody",{className:"divide-y divide-gray-200",children:[o.jsxs("tr",{className:"hover:bg-gray-50",children:[o.jsxs("td",{className:"px-6 py-4 whitespace-nowrap",children:[o.jsx("div",{className:"text-sm font-medium",children:"Jane Smith"}),o.jsx("div",{className:"text-xs text-gray-500",children:"<EMAIL>"})]}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:o.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:"Password Reset"})}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"2025-05-20"}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Admin (<EMAIL>)"})]}),o.jsxs("tr",{className:"hover:bg-gray-50",children:[o.jsxs("td",{className:"px-6 py-4 whitespace-nowrap",children:[o.jsx("div",{className:"text-sm font-medium",children:"John Doe"}),o.jsx("div",{className:"text-xs text-gray-500",children:"<EMAIL>"})]}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:o.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"Email Credential Reset"})}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"2025-05-21"}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"Admin (<EMAIL>)"})]})]})]})})})]})]})},R5=()=>{const[e,t]=f.useState(""),{toast:n}=Qr(),r=jo.filter(i=>i.number.includes(e)||i.assignedTo.toLowerCase().includes(e.toLowerCase())),s=i=>{n({title:"Reassign Number",description:`Prepare to reassign ${i}`})};return o.jsxs("div",{children:[o.jsx(ft,{title:"Twilio Numbers Management",description:"Manage and assign Twilio phone numbers",actions:o.jsxs(V,{className:"bg-primary hover:bg-primary/90",children:[o.jsx($a,{className:"h-4 w-4 mr-2"})," Add New Number"]})}),o.jsx("div",{className:"mb-6",children:o.jsxs("div",{className:"relative",children:[o.jsx(gr,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),o.jsx(Be,{placeholder:"Search by number or assigned user...",className:"pl-10",value:e,onChange:i=>t(i.target.value)})]})}),o.jsx("div",{className:"bg-white overflow-hidden shadow-sm rounded-lg",children:o.jsx("div",{className:"overflow-x-auto",children:o.jsxs("table",{className:"w-full",children:[o.jsx("thead",{className:"bg-gray-50 text-left",children:o.jsxs("tr",{children:[o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Number"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Assigned To"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Assigned Date"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),o.jsx("tbody",{className:"divide-y divide-gray-200",children:r.map(i=>o.jsxs("tr",{className:"hover:bg-gray-50",children:[o.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:o.jsxs("div",{className:"flex items-center",children:[o.jsx(bn,{className:"h-5 w-5 text-primary mr-3"}),o.jsx("span",{className:"text-sm font-medium",children:i.number})]})}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm",children:i.assignedTo}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:i.date}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:o.jsx(V,{variant:"outline",size:"sm",onClick:()=>s(i.number),children:"Reassign"})})]},i.id))})]})})}),o.jsxs("div",{className:"mt-8",children:[o.jsx("h2",{className:"text-lg font-medium mb-4",children:"Twilio Account Overview"}),o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[o.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[o.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"Total Numbers"}),o.jsx("p",{className:"mt-2 text-3xl font-semibold",children:jo.length})]}),o.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[o.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"Available Numbers"}),o.jsx("p",{className:"mt-2 text-3xl font-semibold",children:"0"})]}),o.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[o.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"Monthly Cost"}),o.jsx("p",{className:"mt-2 text-3xl font-semibold",children:"$15"})]}),o.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[o.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"Status"}),o.jsx("p",{className:"mt-2 text-xl font-semibold text-green-600",children:"Active"})]})]})]}),o.jsxs("div",{className:"mt-8",children:[o.jsx("h2",{className:"text-lg font-medium mb-4",children:"Recent Activity"}),o.jsx("div",{className:"bg-white overflow-hidden shadow-sm rounded-lg",children:o.jsx("div",{className:"overflow-x-auto",children:o.jsxs("table",{className:"w-full",children:[o.jsx("thead",{className:"bg-gray-50 text-left",children:o.jsxs("tr",{children:[o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Action"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Number"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Details"})]})}),o.jsxs("tbody",{className:"divide-y divide-gray-200",children:[o.jsxs("tr",{className:"hover:bg-gray-50",children:[o.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:o.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"Assigned"})}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm",children:jo[2].number}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"2025-05-05"}),o.jsx("td",{className:"px-6 py-4 text-sm text-gray-500",children:"Assigned to Sarah Johnson"})]}),o.jsxs("tr",{className:"hover:bg-gray-50",children:[o.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:o.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:"Purchased"})}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm",children:jo[2].number}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"2025-05-05"}),o.jsx("td",{className:"px-6 py-4 text-sm text-gray-500",children:"New number purchased for $5"})]}),o.jsxs("tr",{className:"hover:bg-gray-50",children:[o.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:o.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800",children:"Reassigned"})}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm",children:jo[1].number}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"2025-05-02"}),o.jsx("td",{className:"px-6 py-4 text-sm text-gray-500",children:"Reassigned from Jane Smith to John Doe"})]})]})]})})})]})]})},M5=()=>{const[e,t]=f.useState(""),[n,r]=f.useState("all"),[s,i]=f.useState("all"),{toast:a}=Qr(),l=LP.filter(u=>{const p=u.description.toLowerCase().includes(e.toLowerCase())||u.userName.toLowerCase().includes(e.toLowerCase())||u.id.toLowerCase().includes(e.toLowerCase()),m=n==="all"||u.priority===n,d=s==="all"||u.status===s;return p&&m&&d}),c=u=>{a({title:"Issue Resolved",description:`Issue ${u} has been marked as resolved`})};return o.jsxs("div",{children:[o.jsx(ft,{title:"Issues Management",description:"Track and resolve issues reported by users",actions:o.jsxs(V,{variant:"outline",className:"flex items-center gap-2",children:[o.jsx(Yr,{className:"h-4 w-4"})," Export Issues"]})}),o.jsxs("div",{className:"mb-6 flex flex-col sm:flex-row gap-4",children:[o.jsxs("div",{className:"relative flex-grow",children:[o.jsx(gr,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),o.jsx(Be,{placeholder:"Search by description, user, or ID...",className:"pl-10",value:e,onChange:u=>t(u.target.value)})]}),o.jsxs("div",{className:"grid grid-cols-2 gap-4 sm:w-auto",children:[o.jsxs(kn,{value:n,onValueChange:r,children:[o.jsx(ln,{className:"w-full",children:o.jsx(Pn,{placeholder:"Priority"})}),o.jsxs(cn,{children:[o.jsx(le,{value:"all",children:"All Priorities"}),o.jsx(le,{value:"High",children:"High"}),o.jsx(le,{value:"Medium",children:"Medium"}),o.jsx(le,{value:"Low",children:"Low"})]})]}),o.jsxs(kn,{value:s,onValueChange:i,children:[o.jsx(ln,{className:"w-full",children:o.jsx(Pn,{placeholder:"Status"})}),o.jsxs(cn,{children:[o.jsx(le,{value:"all",children:"All Status"}),o.jsx(le,{value:"Pending",children:"Pending"}),o.jsx(le,{value:"Resolved",children:"Resolved"})]})]})]})]}),o.jsx("div",{className:"bg-white overflow-hidden shadow-sm rounded-lg",children:o.jsx("div",{className:"overflow-x-auto",children:o.jsxs("table",{className:"w-full",children:[o.jsx("thead",{className:"bg-gray-50 text-left",children:o.jsxs("tr",{children:[o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Issue ID"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Description"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Priority"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),o.jsx("th",{className:"px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),o.jsx("tbody",{className:"divide-y divide-gray-200",children:l.map(u=>o.jsxs("tr",{className:"hover:bg-gray-50",children:[o.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:o.jsx("span",{className:"text-sm font-mono",children:u.id})}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:o.jsx("div",{className:"text-sm",children:u.userName})}),o.jsx("td",{className:"px-6 py-4",children:o.jsx("div",{className:"text-sm",children:u.description})}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:o.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${u.priority==="High"?"bg-red-100 text-red-800":u.priority==="Medium"?"bg-yellow-100 text-yellow-800":"bg-green-100 text-green-800"}`,children:u.priority})}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:u.date}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:o.jsx(ir,{status:u.status})}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:o.jsxs("div",{className:"flex space-x-2",children:[o.jsx(V,{variant:"ghost",size:"sm",children:"View"}),u.status==="Pending"&&o.jsx(V,{variant:"outline",size:"sm",onClick:()=>c(u.id),children:"Resolve"})]})})]},u.id))})]})})}),o.jsxs("div",{className:"mt-8 grid grid-cols-1 md:grid-cols-3 gap-6",children:[o.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[o.jsx("h3",{className:"text-sm font-medium text-gray-500 mb-2",children:"Issues by Status"}),o.jsxs("div",{className:"mt-4 space-y-4",children:[o.jsxs("div",{children:[o.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[o.jsx("span",{children:"Pending"}),o.jsx("span",{className:"font-medium",children:"2"})]}),o.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:o.jsx("div",{className:"bg-blue-600 h-2.5 rounded-full",style:{width:"67%"}})})]}),o.jsxs("div",{children:[o.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[o.jsx("span",{children:"Resolved"}),o.jsx("span",{className:"font-medium",children:"1"})]}),o.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:o.jsx("div",{className:"bg-green-500 h-2.5 rounded-full",style:{width:"33%"}})})]})]})]}),o.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[o.jsx("h3",{className:"text-sm font-medium text-gray-500 mb-2",children:"Issues by Priority"}),o.jsxs("div",{className:"mt-4 space-y-4",children:[o.jsxs("div",{children:[o.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[o.jsx("span",{children:"High"}),o.jsx("span",{className:"font-medium",children:"1"})]}),o.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:o.jsx("div",{className:"bg-red-500 h-2.5 rounded-full",style:{width:"33%"}})})]}),o.jsxs("div",{children:[o.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[o.jsx("span",{children:"Medium"}),o.jsx("span",{className:"font-medium",children:"1"})]}),o.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:o.jsx("div",{className:"bg-yellow-500 h-2.5 rounded-full",style:{width:"33%"}})})]}),o.jsxs("div",{children:[o.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[o.jsx("span",{children:"Low"}),o.jsx("span",{className:"font-medium",children:"1"})]}),o.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:o.jsx("div",{className:"bg-green-500 h-2.5 rounded-full",style:{width:"33%"}})})]})]})]}),o.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[o.jsx("h3",{className:"text-sm font-medium text-gray-500 mb-2",children:"Response Time"}),o.jsxs("div",{className:"mt-6",children:[o.jsxs("div",{className:"text-center",children:[o.jsx("p",{className:"text-3xl font-semibold",children:"6.2 hrs"}),o.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Average response time"})]}),o.jsxs("div",{className:"mt-6",children:[o.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[o.jsx("span",{children:"Target: 8 hours"}),o.jsx("span",{className:"font-medium text-green-600",children:"On Track"})]}),o.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:o.jsx("div",{className:"bg-green-500 h-2.5 rounded-full",style:{width:"77.5%"}})})]})]})]})]})]})},A5=new XS,I5=()=>o.jsx(ZS,{client:A5,children:o.jsxs(PS,{children:[o.jsx(Rb,{}),o.jsx(t2,{}),o.jsx(YC,{children:o.jsx(eE,{children:o.jsxs(UC,{children:[o.jsx(Oe,{path:"/",element:o.jsx(CP,{})}),o.jsx(Oe,{path:"/dashboard",element:o.jsx(Xt,{children:o.jsx(fn,{children:o.jsx(zP,{})})})}),o.jsx(Oe,{path:"/job-roles",element:o.jsx(Xt,{children:o.jsx(fn,{children:o.jsx($P,{})})})}),o.jsx(Oe,{path:"/job-roles/create",element:o.jsx(Xt,{children:o.jsx(fn,{children:o.jsx(HP,{})})})}),o.jsx(Oe,{path:"/candidates",element:o.jsx(Xt,{children:o.jsx(fn,{children:o.jsx(w5,{})})})}),o.jsx(Oe,{path:"/candidates/:id",element:o.jsx(Xt,{children:o.jsx(fn,{children:o.jsx(j5,{})})})}),o.jsx(Oe,{path:"/candidates/:id/questions",element:o.jsx(Xt,{children:o.jsx(fn,{children:o.jsx(N5,{})})})}),o.jsx(Oe,{path:"/interviews",element:o.jsx(Xt,{children:o.jsx(fn,{children:o.jsx(b5,{})})})}),o.jsx(Oe,{path:"/activity",element:o.jsx(Xt,{children:o.jsx(fn,{children:o.jsx(S5,{})})})}),o.jsx(Oe,{path:"/config",element:o.jsx(Xt,{children:o.jsx(fn,{children:o.jsx(C5,{})})})}),o.jsx(Oe,{path:"/onboarding",element:o.jsx(Xt,{children:o.jsx(E5,{})})}),o.jsx(Oe,{path:"/admin/dashboard",element:o.jsx(yo,{children:o.jsx(wo,{children:o.jsx(k5,{})})})}),o.jsx(Oe,{path:"/admin/users",element:o.jsx(yo,{children:o.jsx(wo,{children:o.jsx(P5,{})})})}),o.jsx(Oe,{path:"/admin/accounts",element:o.jsx(yo,{children:o.jsx(wo,{children:o.jsx(T5,{})})})}),o.jsx(Oe,{path:"/admin/twilio",element:o.jsx(yo,{children:o.jsx(wo,{children:o.jsx(R5,{})})})}),o.jsx(Oe,{path:"/admin/issues",element:o.jsx(yo,{children:o.jsx(wo,{children:o.jsx(M5,{})})})}),o.jsx(Oe,{path:"*",element:o.jsx(EP,{})})]})})})]})});gg(document.getElementById("root")).render(o.jsx(I5,{}));
