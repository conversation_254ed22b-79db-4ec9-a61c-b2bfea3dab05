import { createRoot } from 'react-dom/client'
import App from './App.jsx'
// import './index.css'  // Temporarily disabled to test

// Test if <PERSON><PERSON> is working
console.log('React app starting...')

// Simple test component
const TestComponent = () => {
  return (
    <div style={{ padding: '20px', fontSize: '18px', color: 'blue' }}>
      <h1>React is working!</h1>
      <p>If you see this, <PERSON><PERSON> is rendering correctly.</p>
    </div>
  )
}

try {
  createRoot(document.getElementById("root")).render(<TestComponent />);
  console.log('Test component rendered successfully')
} catch (error) {
  console.error('Error rendering React app:', error)
  document.getElementById("root").innerHTML = `<div style="padding: 20px; color: red;">Error: ${error.message}</div>`
}
