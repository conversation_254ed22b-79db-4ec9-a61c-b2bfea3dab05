{"name": "@vitejs/plugin-react-swc", "version": "3.10.1", "license": "MIT", "author": "<PERSON><PERSON><PERSON> (https://github.com/ArnaudBarre)", "description": "Speed up your Vite dev server with SWC", "keywords": ["vite", "vite-plugin", "react", "swc", "react-refresh", "fast refresh"], "type": "module", "repository": {"type": "git", "url": "git+https://github.com/vitejs/vite-plugin-react.git", "directory": "packages/plugin-react-swc"}, "bugs": {"url": "https://github.com/vitejs/vite-plugin-react/issues"}, "homepage": "https://github.com/vitejs/vite-plugin-react/tree/main/packages/plugin-react-swc#readme", "dependencies": {"@rolldown/pluginutils": "1.0.0-beta.9", "@swc/core": "^1.11.22"}, "peerDependencies": {"vite": "^4 || ^5 || ^6"}, "main": "index.cjs", "types": "index.d.ts", "module": "index.mjs", "exports": {".": {"types": "./index.d.ts", "require": "./index.cjs", "import": "./index.mjs"}}}