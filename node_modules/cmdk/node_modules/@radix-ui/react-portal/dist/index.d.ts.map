{"mappings": ";;;AAaA,yBAAyB,MAAM,wBAAwB,CAAC,OAAO,UAAU,GAAG,CAAC,CAAC;AAC9E,4BAAsB,SAAQ,iBAAiB;IAC7C;;OAEG;IACH,SAAS,CAAC,EAAE,WAAW,GAAG,IAAI,CAAC;CAChC;AAED,OAAA,MAAM,0FAKJ,CAAC;AAMH,OAAA,MAAM,wFAAa,CAAC", "sources": ["packages/react/portal/src/packages/react/portal/src/Portal.tsx", "packages/react/portal/src/packages/react/portal/src/index.ts", "packages/react/portal/src/index.ts"], "sourcesContent": [null, null, "export {\n  Portal,\n  //\n  Root,\n} from './Portal';\nexport type { PortalProps } from './Portal';\n"], "names": [], "version": 3, "file": "index.d.ts.map"}