{"mappings": "AAAA,qCAA8B,CAAC,EAC7B,oBAAoB,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,IAAI,EACzC,eAAe,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,IAAI,EACpC,EAAE,wBAA+B,EAAE;;CAAK,WAEL,CAAC,UAOrC", "sources": ["packages/core/primitive/src/packages/core/primitive/src/primitive.tsx", "packages/core/primitive/src/packages/core/primitive/src/index.ts", "packages/core/primitive/src/index.ts"], "sourcesContent": [null, null, "export { composeEventHandlers } from './primitive';\n"], "names": [], "version": 3, "file": "index.d.ts.map"}