# Alex Interviewer - AI-Powered Interview Platform

An AI-powered interview platform built with React and modern web technologies.

## Features

- AI-powered interview automation
- Candidate management system
- Job role configuration
- Interview scheduling and tracking
- Admin dashboard
- User authentication

## Technologies Used

- **Frontend**: React 18 with Vite
- **UI Components**: shadcn/ui
- **Styling**: Tailwind CSS
- **Routing**: React Router
- **State Management**: TanStack Query
- **Icons**: Lucide React

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn

### Installation

1. Clone the repository:
```sh
git clone <repository-url>
cd alex-interviewer
```

2. Install dependencies:
```sh
npm install
```

3. Start the development server:
```sh
npm run dev
```

4. Open your browser and navigate to `http://localhost:8080`

### Building for Production

```sh
npm run build
```

## Login Credentials

- **User**: `<EMAIL>` / `User123!`
- **Admin**: `<EMAIL>` / `Admin123!`
- **Demo**: Click "Try Demo" button

## Project Structure

```
src/
├── components/     # Reusable UI components
├── contexts/       # React contexts
├── hooks/          # Custom hooks
├── lib/            # Utility functions
├── pages/          # Page components
└── data/           # Mock data and models
```

## License

© 2025 Alex Interviewer. All rights reserved.

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)
